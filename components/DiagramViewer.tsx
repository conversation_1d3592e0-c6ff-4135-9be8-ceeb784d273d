'use client';

import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { DiagramConfig, validateDrawioXML, exportDiagram, ExportOptions } from '@/lib/drawio';

interface DiagramViewerProps {
  xml: string;
  config?: DiagramConfig;
  className?: string;
}

export interface DiagramViewerRef {
  exportDiagram: (options: ExportOptions) => Promise<string>;
}

const DiagramViewer = forwardRef<DiagramViewerRef, DiagramViewerProps>(
  ({ xml, config, className = '' }, ref) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // 暴露导出功能给父组件
    useImperativeHandle(ref, () => ({
      exportDiagram: async (options: ExportOptions) => {
        if (!iframeRef.current) {
          throw new Error('图表未加载完成');
        }
        return exportDiagram(iframeRef.current, options);
      }
    }));

  useEffect(() => {
    if (!xml) {
      setError('没有提供XML数据');
      setIsLoading(false);
      return;
    }

    // 验证XML格式
    if (!validateDrawioXML(xml)) {
      setError('XML格式验证失败，请重新生成');
      setIsLoading(false);
      return;
    }

    const iframe = iframeRef.current;
    if (!iframe) return;

    setIsLoading(true);
    setError(null);

    // 使用embed模式的正确URL
    const embedUrl = 'https://embed.diagrams.net/?embed=1&ui=min&spin=1&modified=unsavedChanges&proto=json&noSaveBtn=1&noExitBtn=1';

    iframe.src = embedUrl;

    // 监听来自iframe的消息
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://embed.diagrams.net') return;

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

        if (data.event === 'init') {
          // 发送XML数据到iframe
          const message = {
            action: 'load',
            xml: xml,
            title: 'Generated Diagram'
          };
          iframe.contentWindow?.postMessage(JSON.stringify(message), '*');
          setIsLoading(false);
        } else if (data.event === 'load') {
          // 图表加载完成
          console.log('Diagram loaded successfully');
        } else if (data.event === 'save') {
          // 处理保存事件（如果需要）
          console.log('Diagram saved');
        }
      } catch (err) {
        console.error('处理iframe消息失败:', err);
        setError('加载图表时出错');
        setIsLoading(false);
      }
    };

    // 监听iframe加载错误
    const handleError = () => {
      setError('无法连接到draw.io服务');
      setIsLoading(false);
    };

    window.addEventListener('message', handleMessage);
    iframe.addEventListener('error', handleError);

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage);
      iframe.removeEventListener('error', handleError);
    };
  }, [xml, config]);

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-8 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 text-lg font-medium mb-2">加载失败</div>
          <div className="text-red-500 text-sm">{error}</div>
          <button
            onClick={() => window.open(`https://app.diagrams.net/`, '_blank')}
            className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
          >
            在draw.io中打开
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">加载中...</span>
          </div>
        </div>
      )}
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        style={{ minHeight: '500px' }}
        title="Draw.io Diagram Viewer"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
      />
    </div>
  );
}
);

DiagramViewer.displayName = 'DiagramViewer';

export default DiagramViewer;
