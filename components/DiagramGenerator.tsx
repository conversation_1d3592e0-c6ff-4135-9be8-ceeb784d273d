'use client';

import React, { useState, useRef } from 'react';
import <PERSON><PERSON>ram<PERSON>iewer, { DiagramViewerRef } from './DiagramViewer';
import { cleanXML, createSampleXML, downloadDataUri } from '@/lib/drawio';

export default function DiagramGenerator() {
  const [description, setDescription] = useState('');
  const [generatedXML, setGeneratedXML] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const diagramViewerRef = useRef<DiagramViewerRef>(null);

  const handleGenerate = async () => {
    if (!description.trim()) {
      setError('请输入流程图描述');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/generate-diagram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '生成失败');
      }

      if (data.success && data.xml) {
        console.log('Raw XML from API:', data.xml);
        const cleanedXML = cleanXML(data.xml);
        console.log('Cleaned XML:', cleanedXML);
        setGeneratedXML(cleanedXML);
      } else {
        throw new Error('生成的XML无效');
      }
    } catch (err) {
      console.error('生成流程图失败:', err);
      setError(err instanceof Error ? err.message : '生成失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadSample = () => {
    const sampleXML = createSampleXML();
    setGeneratedXML(sampleXML);
    setDescription('示例流程图：开始 -> 处理 -> 结束');
  };

  const handleClearAll = () => {
    setDescription('');
    setGeneratedXML('');
    setError(null);
  };

  const handleDownloadXML = () => {
    if (!generatedXML) return;

    const blob = new Blob([generatedXML], { type: 'application/xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'diagram.drawio';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleDownloadPNG = async (quality: 'standard' | 'high' = 'high') => {
    if (!diagramViewerRef.current || !generatedXML) {
      setError('图表未加载完成，无法导出');
      return;
    }

    setIsExporting(true);
    setError(null);

    try {
      // 根据质量设置不同的参数
      const exportOptions = quality === 'high' ? {
        format: 'png' as const,
        scale: 3, // 高质量：3倍缩放
        border: 30,
        shadow: false,
        grid: false,
        transparent: true,
        background: '',
        width: 2400, // 高分辨率
        height: 1800
      } : {
        format: 'png' as const,
        scale: 2, // 标准质量：2倍缩放
        border: 20,
        shadow: false,
        grid: false,
        transparent: true,
        background: '',
        width: 1600,
        height: 1200
      };

      const dataUri = await diagramViewerRef.current.exportDiagram(exportOptions);

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const qualityPrefix = quality === 'high' ? 'hq_' : '';
      const filename = `${qualityPrefix}diagram_${timestamp}.png`;

      // 下载文件
      downloadDataUri(dataUri, filename);
    } catch (err) {
      console.error('导出PNG失败:', err);
      setError(err instanceof Error ? err.message : '导出PNG失败');
    } finally {
      setIsExporting(false);
    }
  };

  const handleOpenInDrawio = () => {
    if (!generatedXML) return;

    const encodedXML = encodeURIComponent(generatedXML);
    const drawioUrl = `https://app.diagrams.net/?lightbox=1#R${encodedXML}`;
    window.open(drawioUrl, '_blank');
  };

  const handleTestAPI = async () => {
    try {
      const response = await fetch('/api/generate-diagram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description: '简单的测试流程：开始->处理->结束' }),
      });

      const data = await response.json();
      console.log('API Test Response:', data);

      if (data.success && data.xml) {
        console.log('Raw XML length:', data.xml.length);
        console.log('Raw XML preview:', data.xml.substring(0, 300));

        // 直接使用原始XML，不经过cleanXML处理
        setGeneratedXML(data.xml);
        setDescription('API测试流程');
      }
    } catch (err) {
      console.error('API测试失败:', err);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* 标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          AI 流程图生成器
        </h1>
        <p className="text-gray-600">
          描述你的流程，AI 将自动生成专业的流程图
        </p>
      </div>

      {/* 输入区域 */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          流程描述
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="请描述你想要创建的流程图，例如：用户登录流程、订单处理流程、数据处理管道等..."
          className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          disabled={isLoading}
        />

        {/* 示例提示 */}
        <div className="mt-2">
          <p className="text-sm text-gray-600 mb-2">💡 示例描述：</p>
          <div className="flex flex-wrap gap-2">
            {[
              "用户注册和登录流程",
              "在线购物订单处理流程",
              "软件开发生命周期",
              "客户服务处理流程",
              "数据备份和恢复流程"
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setDescription(example)}
                disabled={isLoading}
                className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {example}
              </button>
            ))}
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* 按钮组 */}
        <div className="mt-4 flex flex-wrap gap-3">
          <button
            onClick={handleGenerate}
            disabled={isLoading || !description.trim()}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            <span>{isLoading ? '生成中...' : '生成流程图'}</span>
          </button>

          <button
            onClick={handleLoadSample}
            disabled={isLoading}
            className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            加载示例
          </button>

          <button
            onClick={handleClearAll}
            disabled={isLoading}
            className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            清空
          </button>

          <button
            onClick={handleTestAPI}
            disabled={isLoading}
            className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            测试API
          </button>
        </div>
      </div>

      {/* 流程图显示区域 */}
      {generatedXML && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">生成的流程图</h2>
            <div className="flex space-x-2">
              <div className="relative">
                <button
                  onClick={() => handleDownloadPNG('high')}
                  disabled={isExporting}
                  className="px-4 py-2 bg-purple-600 text-white rounded-l-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isExporting ? '导出中...' : '下载 PNG (高清)'}
                </button>
                <button
                  onClick={() => handleDownloadPNG('standard')}
                  disabled={isExporting}
                  className="px-3 py-2 bg-purple-500 text-white rounded-r-md hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed border-l border-purple-400"
                  title="标准质量PNG"
                >
                  标准
                </button>
              </div>
              <button
                onClick={handleDownloadXML}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm"
              >
                下载 XML
              </button>
              <button
                onClick={handleOpenInDrawio}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm"
              >
                在 Draw.io 中编辑
              </button>
            </div>
          </div>

          <DiagramViewer
            ref={diagramViewerRef}
            xml={generatedXML}
            className="w-full"
            config={{
              width: 800,
              height: 600,
              toolbar: false,
              menubar: false,
              edit: false,
            }}
          />

          {/* XML 代码显示（可选） */}
          <details className="mt-4">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
              查看 XML 代码
            </summary>
            <pre className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-xs overflow-x-auto">
              <code>{generatedXML}</code>
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
