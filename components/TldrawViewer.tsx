'use client';

import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Tld<PERSON>, Editor, TLUiOverrides, exportToBlob, TLShapeId } from 'tldraw';
import 'tldraw/tldraw.css';

interface TldrawViewerProps {
  className?: string;
  initialData?: any;
  readOnly?: boolean;
}

export interface TldrawViewerRef {
  exportToPNG: (options?: { scale?: number; background?: boolean }) => Promise<string>;
  exportToSVG: () => Promise<string>;
  loadData: (data: any) => void;
  getEditor: () => Editor | null;
}

const TldrawViewer = forwardRef<TldrawViewerRef, TldrawViewerProps>(
  ({ className = '', initialData, readOnly = false }, ref) => {
    const editorRef = useRef<Editor | null>(null);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      exportToPNG: async (options = {}) => {
        if (!editorRef.current) {
          throw new Error('编辑器未初始化');
        }

        const editor = editorRef.current;
        const shapeIds = editor.getCurrentPageShapeIds();

        if (shapeIds.size === 0) {
          throw new Error('没有可导出的内容');
        }

        try {
          const blob = await exportToBlob({
            editor,
            ids: Array.from(shapeIds) as TLShapeId[],
            format: 'png',
            opts: {
              scale: options.scale || 2, // 高清导出
              background: options.background !== false, // 默认包含背景
            },
          });

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (error) {
          console.error('PNG导出失败:', error);
          throw new Error('PNG导出失败');
        }
      },

      exportToSVG: async () => {
        if (!editorRef.current) {
          throw new Error('编辑器未初始化');
        }

        const editor = editorRef.current;
        const shapeIds = editor.getCurrentPageShapeIds();

        if (shapeIds.size === 0) {
          throw new Error('没有可导出的内容');
        }

        try {
          const blob = await exportToBlob({
            editor,
            ids: Array.from(shapeIds) as TLShapeId[],
            format: 'svg',
            opts: {
              scale: 1,
              background: true,
            },
          });

          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (error) {
          console.error('SVG导出失败:', error);
          throw new Error('SVG导出失败');
        }
      },

      loadData: (data: any) => {
        if (editorRef.current && data) {
          try {
            // 清空当前内容
            editorRef.current.selectAll();
            editorRef.current.deleteShapes(editorRef.current.getSelectedShapeIds());

            // 加载新数据
            editorRef.current.loadSnapshot(data);

            // 调整视图以适应内容
            setTimeout(() => {
              if (editorRef.current) {
                editorRef.current.zoomToFit();
              }
            }, 100);
          } catch (error) {
            console.error('加载数据失败:', error);
          }
        }
      },

      getEditor: () => editorRef.current,
    }));

    // 自定义UI覆盖（隐藏不需要的工具）
    const uiOverrides: TLUiOverrides = {
      tools(editor, tools) {
        // 只保留基本绘图工具
        return {
          select: tools.select,
          hand: tools.hand,
          draw: tools.draw,
          eraser: tools.eraser,
          arrow: tools.arrow,
          line: tools.line,
          rectangle: tools.rectangle,
          ellipse: tools.ellipse,
          diamond: tools.diamond,
          triangle: tools.triangle,
          star: tools.star,
          hexagon: tools.hexagon,
          cloud: tools.cloud,
          text: tools.text,
          note: tools.note,
          frame: tools.frame,
        };
      },
      actions(editor, actions) {
        // 保留基本操作
        return {
          'zoom-in': actions['zoom-in'],
          'zoom-out': actions['zoom-out'],
          'zoom-to-fit': actions['zoom-to-fit'],
          'zoom-to-selection': actions['zoom-to-selection'],
          'reset-zoom': actions['reset-zoom'],
          undo: actions.undo,
          redo: actions.redo,
          cut: actions.cut,
          copy: actions.copy,
          paste: actions.paste,
          'select-all': actions['select-all'],
          'delete': actions['delete'],
          'duplicate': actions['duplicate'],
        };
      },
    };

    const handleMount = (editor: Editor) => {
      editorRef.current = editor;

      // 如果有初始数据，加载它
      if (initialData) {
        editor.loadSnapshot(initialData);
      }
    };

    return (
      <div className={`tldraw-container ${className}`} style={{ height: '500px' }}>
        <Tldraw
          onMount={handleMount}
          overrides={uiOverrides}
          options={{
            maxPages: 1, // 限制为单页
          }}
          components={{
            // 可以自定义组件
          }}
        />
      </div>
    );
  }
);

TldrawViewer.displayName = 'TldrawViewer';

export default TldrawViewer;
