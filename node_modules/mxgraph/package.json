{"name": "mxgraph", "description": "mxGraph is a fully client side JavaScript diagramming library that uses SVG and HTML for rendering.", "version": "4.2.2", "homepage": "https://github.com/jgraph/mxgraph", "author": {"name": "JGraph Ltd", "email": "<EMAIL>"}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/jgraph/mxgraph.git"}, "bugs": {"url": "https://github.com/jgraph/mxgraph/issues"}, "main": "./javascript/dist/build.js", "scripts": {"prepare": "grunt build --base ./ --gruntfile etc/build/Gruntfile.js"}, "devDependencies": {"grunt": "^1.0.1", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-copy": "^1.0.0", "grunt-webpack": "^2.0.1", "load-grunt-tasks": "^3.5.2", "webpack": "^2.2.1"}}