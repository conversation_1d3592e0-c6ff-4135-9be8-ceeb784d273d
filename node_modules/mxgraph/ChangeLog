28-OCT-2020: 4.2.2

- Corrected build process

28-OCT-2020: 4.2.1

- Removes leading and trailing spaces for text in mxUtils.getPrettyXml
- Adds option for mxVertexHandler.movePreviewToFront
- Adds optional arguments, redraw of parent highlight
- Fixes ignored pointerEvents flag for text in SVG
- Adds mxShape.beforePaint and afterPaint hooks

19-JUN-2020: 4.2.0

- Adds ghost preview in mxVertexHandler
- Makes mxHandle.positionChanged optional
- Executes bottom to top before top to bottom in mxLayoutManager
- Adds selection handling for cell hierarchies
- Adds event transparency for filled swimlane content
- Adds mxSelectionCellsHandler.isHandled
- Adds mxGraph.getCellToSelect
- Adds mxGraphHandler.isPropagateSelectionCell
- Removes mxClient.IOS_VERSION and workaround for event handling
- Fixes mxClient.IS_SF for iframe
- Adds optional textWidth for mxGraph.getPreferredSizeForCell
- Adds vertical switch for mxLine
- Adds mxLayoutManager.hasLayout hook
- Adds optional shape argument in mxHandle
- Adds mxGraphHandler.getHandledSelectionCells
- Adds mxVertexHandler.getSizerBounds, isParentHighlightVisible, getSelectionBorderBounds

31-MAR-2020: 4.1.1

- Fixes use of userAgent to detect browsers and platforms
- Reduces number of layout runs in mxLayoutManager
- Adds resizeCell in mxGraphLayout
- Adds eventName in mxLayoutManager.getLayout
- Uses all layers in getSwimlaneAt
- Fixes update of indicator shape after style change
- Adds support for referenced font colors

24-JAN-2020: 4.1.0

- Adds mxEdgeHandler.isCellEnabled
- Enables reflow in HTML output
- Fixes mxUtils.equalPoints
- Fixes event handling for desktop mode in mobile Safari
- Adds mxGraph.snapDelta, uses raster for moving cells
- Adds optional cells argument in mxGraphHandler.start
- Fixes mxGraph.sizeDidChange for negative coordinates
- Allows for corner rotation in mxVertexHandler
- Adds mxGraphHandler.suspend and resume
- Fixes moving and resizing on iOS 13
- Adds mxSvgCanvas2D.addForeignObject
- Adds mxClient.IOS_VERSION

08-NOV-2019: 4.0.6

- New vagrant build system
- Fixes ignored direction for horizontal/vertical flip in mxGraph.getConnectionPoint
- Consumes iOS touch events by default in mxMouseEvent.consume
- Fixes handling of empty strings in mxCell.getAttribute
- Restores clipping in mxPrintPreview

17-OCT-2019: 4.0.5

- Fixes border handling in mxGraph.fit, center and sizeDidChange
- Makes mxXmlUtils.getDocumentBuilder thread-safe [382]
- Fixes ignored alignment in mxGraph.cellSizeUpdated
- Adds optional argument in redraw for handlers
- Disables resize live preview for groups
- Adds mxGraphHandler.maxLivePreview
- Adds mxGuide.isStateIgnored hook

20-AUG-2019: 4.0.4

- Moved PHP code to own repo
- Adds DPI support in advanced dialog

26-JUL-2019: 4.0.3

- Reverts forcing quotes around font names in CSS and SVG
- Ignores hidden cells in mxGraph.selectAll
- Adds mxClient.IS_CHROMEOS

15-JUL-2019: 4.0.2

- Adds mxCellEditor.setAlign for block alignment changes
- Adds validation for label background and border colors
- Adds mxGraphHandler.removeEmptyParents

02-JUL-2019: 4.0.1

- Forces quotes around font names in CSS and SVG
- No longer prevents default for pen/touch events in mxMouseEvent
- Adds validation for color codes
- Fixes handling of Infinity in mxObjectCodec
- Add target for Chrome in mxEvent.addMouseWheelListener
- Removes scroll handler in mxOutline.destroy
- Adds mxStackLayout.allowGaps and gridSize
- Fixes possible NPE in mxGraph.sizeDidChange

11-MAR-2019: 4.0.0

- mxClient.IS_SVG is true for all non-VML browsers
- Adds mxCellState.invalidStyle
- Removes mxGraph.EMPTY_ARRAY
- Fixes events for transparent unclosed SVG paths
- Adds mxGuide.rounded, getDelta
- Disables drag and drop for text in mxCellEditor
- Adds mxUtils.getDocumentSize
- Handles self references and duplicates in mxCodec
- Make navigation steps significant in undo history
- Fixes terminal point after disconnect with navigation
- Adds invisible connected edges in mxGraph.removeCells
- Ignores current root in mxGraphView.getVisibleTerminal
- Adds mxGraph.updateSelection
- Fixes edit replay for mxChildChange
- Removes mxGraph.getRemovedCellsForChanges
- Fixes possible type errors in mxSvgCanvas

14-DEC-2018: 3.9.12

- Fixes mxGraph.getSelectionCellsForChanges
- Fixes duplicate clones in mxGraphModel.cloneCellImpl
- Fixes invalidation bug in mxGraph.processChange
- Adds mxGraph.cloneCell helper function
- Fixes possible NPE in mxEdgeSegmentHandler.start
- Ignores errors in mxEvent.release, mxPrintPreview.closeDocument
- Adds keepPosition in mxGraph.cloneCells
- Updates move preview during remote changes

06-NOV-2018: 3.9.11

- Fixes use of undefined variable in mxGraphLayout.isAncestor
- Fixes lookup for clones in mxGraphModel.cloneCells
- Improved handling for numeric values in mxObjectCodec
- Removes ctor error in mxCodec.getElementById
- Adds boolean clone argument in mxGuide.move

08-OCT-2018: 3.9.10

- Fixes swimlane rendering for certain arc sizes
- Ignores constraint check for different source and target in mxConnectionHandler

21-SEP-2018: 3.9.9

- Uses mxRectangleShape.paintBackground in mxSwimlane
- Fixes cell order, adds checkConstraints in mxConnectionHandler
- Fixes duplicate call to onload in mxXmlRequest.send
- Removes mxEvent.objects and mxClient.dispose
- Fixes mxPrintPreview.appendGraph for IE and Edge
- Using CSS transform in mxPrintPreview (experimental)
- Adds mxShape.isRoundable

06-JUL-2018: 3.9.8

- Fixes incorrect logging level in codecs

22-JUN-2018: 3.9.7

- Adds mxCylinder.getCylinderSize
- Adds mxConstants.STYLE_ANCHOR_POINT_DIRECTION
- Fixes mxGraphicsCanvas2D rotated text with HTML_SCALE [Java]
- Adds webkit-print-color-adjust in mxPrintPreview
- Adds mxConstants.STYLE_BACKGROUND_OUTLINE

31-MAY-2018: 3.9.6

- Adds mxConnectionHandler.isCellEnabled
- Fixes outline for shapes with no stroke

24-MAY-2018: 3.9.5

- Fixes possible NPE in mxDragSource.dragEnter

17-MAY-2018: 3.9.4

- Adds optional state argument in mxTooltipHandler.reset
- Adds mxDragSource.checkEventSource, getElementForEvent
- Adds mxCellRenderer.minSvgStrokeWidth
- Fixes use of innerText in mxUtils.getTextContent
- Adds mxTooltipHandler.getStateForEvent hook
- Adds tolerance for straight edge detection
- Uses Object for mxResources.resources

05-MAR-2018: 3.9.3

- Fixes post is not a function

28-FEB-2018: 3.9.2

- Adds supercall to fire execute event in mxMorphing
- Allows for 0.5px steps in mxGraphHandler.roundLength
- Fixes possible inconsistency in mxChildChangeCodec
- Clears selection after editing in mxCellEditor
- Hides special handles while editing in mxVertexHandler
- Handles file protocol in mxUrlConverter
- mxCellEditor handles SVG with absolute position
- Fixes cloning of edges in mxEdgeHandler

27-JAN-2018: 3.9.1

- Adds resources.html example
- Adds mxCellEditor.isCancelEditingKeyEvent
- Adds mxClient.loadResources
- Adds mxGraph.getLinkForCell hook

19-JAN-2018: 3.9.0

- Add layout.isAncestor where child == parent returns false
- Removes duplicate WeightedCellSorter
- Fixes recursion in mxGraph.cellsFolded
- Adds mxRubberband.fadeOut switch
- Fixes ignored node values in mxUtils.getPrettyXml 
- Adds editing for edge terminal points
- Fixes redraw after remove change in mxEdgeHandler
- Adds loggers, fixes silent fail cases
- Fixes inline handler in mxEvent.disableContextMenu
- Fixes mxPanningHandler for mouse up over scrollbar
- Float loop router ignored for more than 1 point
- Adds mxVertexHandler.getRotationHandlePosition

30-NOV-2017: 3.8.0

- Moves mxCellRenderer.prototype.defaultShapes to mxCellRenderer
- Fixes handling of touch events in mxGraph.isEventIgnored
- Adds mxSelectionCellsHandler.isHandlerActive hook

24-NOV-2017: 3.7.6

- Adds toString methods in some classes in Java
- Fixes text flow after shape change, text size cache
- Fixes return value of mxUtils.loadDocument in Java
- Resets constraint handler after translate
- Adds mxCellRenderer.checkPlaceholderStyles
- Adds links on shapes in mxPrintPreview
- Adds mxCellRenderer.doRedrawShape

28-AUG-2017: 3.7.5

- Adds line jumps in grapheditor example
- Adds mxCellRenderer.isShapeInvalid hook
- Adds mxConstants.STYLE_ABSOLUTE_ARCSIZE for rectangles
- Calls updateCachedBounds in mxGraphView.validateCellState
- Adds mxShape.getLabelMargins, exclude argument in addPoints
- Adds mxUtils.getDirectedBounds and getPerimeterPoint
- Handles flipH/-V in mxGraphView.getPerimeterPoint
- Fixes pen events for Chrome on Windows
- Adds fixDash style, mxEvent.isPenEvent
- Fixes possible NPE in mxUtils.ltrim and rtrim
- mxUtils.getScrollOrigin ignores ancestors by default
- Adds optional arguments in mxUtils.getScrollOrigin
- Adds mxGraph.isTransparentClickEvent hook

27-JUN-2017: 3.7.4

- Fixes mxXmlUtils.parseXml null return value

27-JUN-2017: 3.7.3

- Fixes horizontal/vertical flip for perimeter projections
- Changes mxConstants.PAGE_FORMAT_A4_PORTRAIT/LANDSCAPE width/height to 827
- Disables external XML entities in all backends
- Fixes ignored underline in mxUtils.getFont
- Resets tooltip content in mxTooltipHandler.hideTooltip
- Fixes custom handle visible during resize with strict HTML
- Fixes custom handle text flow preview with strict HTML
- Fixes reset of inactive mxHandles in mxVertexHandler.reset
- Adds mxConstants.STYLE_SOURCE/TARGET_PORT_CONSTRAINT styles
- Fixes cache invalidation for custom handle reset
- Fixes submenu showing with negative x-coordinate
- Adds optional inverse argument for mxGraph.traverse
- Fixes possible String type for spacing in mxText
- Adds mxForceIncludes global config variable
- Fixes mxCellHighlight for scaled arrows
- Removes fix for cloned start/end arrow when splitting edge
- Fixes touch events in Chrome for hiding modal dialogs

12-APR-2017: 3.7.2

- Adds checks for swimlane rendering boundary cases
- Fixes cloned start/end arrow in mxGraph.splitEdge
- Removes mxShape.arrowStrokewidth, fixes mxCellHighlight for arrows
- Adds automatic NPM release
- Adds optional type argument in mxForm.addText
- Fixes selection of removed parents in grapheditor
- Fixes Graph.distributeCells for child cells in groups
- Fixes Graph.encodeCells for children of selected parents
- Fixes possible NPE in mxUtils.getOffset/getScrollOrigin
- Fixes dashed and strokewidth tag in stencils for Java
- Fixes markup for same window for IE in mxPrintPreview

01-MAR-2017: 3.7.1

- Uses semantic versioning
- Fixes mxUtils.getOffset and getScrollOrigin for fixed elements
- Adds handling for general case in mxGraph.getCellValidationError
- Ignores connected terminals in mxGraph.getBoundingBoxFromGeometry
- Adds mxCellEditor.prepareTextarea for linefeeds in FF
- Fixes linefeeds in mxUtils.extractTextWithWhitespace
- Adds mxSvgCanvas2D.imageOffset option
- Refactors link handling in grapheditor
- Fixes extendParent order for mxGraph.groupCells

29-JAN-2017: 3.7.0.1

- Fixes mxXmlRequest.simulate in Webkit
- Adds HTML_SCALE, HTML_UNIT in mxGraphicsCanvas2D
- Fixes repaint in mxConstraintHandler

16-JAN-2017: 3.7.0.0

- mxXmlRequest.simulate no longer adds form to DOM in Webkit
- Adds workaround for repaint issues in mxWindow
- Adds mxGraphHandler.consumeMouseEvent hook
- Removes word wrap in mxLog for performance reasons
- Fixes mxClient.IS_POINTER for Chrome on Mac
- Fixes bounding box for rotated text
- Fixes default alignment in mxText.resetStyles
- Uses UTF-8 charset in mxClient.link
- Adds fallback for missing function in mxCell.hasAttribute
- Fixes trailing page break after appendices in mxPrintPreview
- Resets default parent if root changes in mxGraph.processChange
- Fixes version format in package.json in mxgraph-js repo
- Fixes initial scrollbars for hidden content in grapheditor
- Adds mxRubberband.isActive, execute
- Adds mxPrintPreview.appendGraph, addPageBreak, closeDocument
- Adds optional name for mxConnectionConstraint
- Fixes handling of terminal points in mxGraph.getBoundingBoxFromGeometry
- Fixes vertical-/horizontalPageBreaks in mxGraph instance
- Uses minimum of 1 page for empty graphs with preferPageSize
- Adds crisp rendering, fixes scrollbars in IE for page breaks
- Fixes scaled return value in mxGraph.getPreferredPageSize
- Adds snapToPoint custom attribute in grapheditor
- Adds mxCell.hasAttribute, scope for placeholder in grapheditor
- Adds Alt+(Shift+)Tab for select parent/child in grapheditor
- Resets undo history after editing in mxCellEditor

07-SEP-2016: 3.6.0.0

- Adds Alt+Shift+Cursor to connect and clone in grapheditor
- Adds placeholders for custom shape text in grapheditor
- Moves Editor.(de)compress to Graph in grapheditor
- Ctrl+A selects all cells recursively in grapheditor
- Adds optional boolean argument in mxGraph.selectAll
- Enables relative links for labels in grapheditor
- Fixes use of Navigator.pointerEnabled in mxClient.IS_POINTER
- Adds mxCellEditor.escapeCancelsEditing, uses false in grapheditor
- Fixes bounds for zoomed markup in-place editor in grapheditor example
- Ignores scale in mxGuide.getGuideTolerance
- Removes clipping for general text in grapheditor example
- Adds mxPrintPreview.prototype.clipping switch
- Adds Ant tasks for running Java examples [Java]
- Wheel zooms to mouse cursor in grapheditor example
- Ignores edge childs for vertices in mxGraph.selectCells
- Adds support for placeholder metadata
- Adds mxPopupMenu.addCheckmark
- Changes signature for mxICanvas.get/setTranslate [Java]
- Uses Ctrl+Shift+Z for redo on Linux in grapheditor example
- Adds Ctrl+,/. for subscript/superscript in grapheditor example

20-JUN-2016: *******

- Adds padding for pages in print preview in grapheditor example
- Moves getEditBlankXml to EditorUi in grapheditor example
- Makes mxGraph.constrainChildrenOnResize default, removes switch
- Adds mxRectangle.intersect, mxGraph.constrainRelativeChildren
- Fixes handling of bounding box in mxGraph.constrainChild
- Fixes ignored constraints in mxVertexHandler.resizeVertex
- Consumes event for mxEdgeHandler.addPoint
- Adds rounding for middle segment in elbow edge styles
- Fixes synchronous loading of special bundle in mxResources
- Fixes resources loading with callback in mxResources
- Adds elbow and isometric edge styles in grapheditor example
- Adds View, Page scale in grapheditor example
- Uses letter paper size in US, Canada and Mexico in grapheditor example
- Fixes synchronous request warning in grapheditor example
- Fixes overriden mxVertexHandler.isSizerVisible
- Fixes horizontal/vertical flip in mxText
- Fixes mxSvgCanvas2D state after addNode with no shape
- Fixes folding icon in tree.html example

02-JUN-2016: *******

- Fixes possible string concatenation for font size in mxAbstractCanvas [JavaScript]
- Fixes ignored strokewidth in background section of stencil [JavaScript]

09-MAY-2016: *******

- Enables floating point numbers for font sizes, removes rounding [JavaScript]
- Fixes inconsistent results in mxGraph.fit, doResizeContainer [JavaScript]
- Changes default border for mxGraph.fit to this.border [JavaScript]
- Adds updateContainerStyle in mxGraphView for older browsers [JavaScript]
- Moves Editor.init hook to after c'tor in grapheditor [JavaScript]
- Adds mxGraph.isIgnoreTerminalEvent hook [JavaScript]
- Adds mxCellRenderer.forceControlClickHandler switch [JavaScript]
- Fires mouseup event for click on folding icon, fixes blurred rendering [JavaScript]
- Fixes ignored scaled for border in mxGraph.sizeDidChange [JavaScript]
- Changes default for margin in mxGraph.fit to 0 [JavaScript]
- Fixes possible NPE in mxConnectionHandler.reset [JavaScript]
- Adds mxConstants.WORD_WRAP global switch for word wrapping [JavaScript]
- Changes type of mxStencilRegistry.stencils from array to object [JavaScript]
- Fixes drawing of edges connected to hidden layers [JavaScript]
- Sets state invalid in mxGraphView.removeState [JavaScript]
- Adds mxUtils.parseCssNumber, fixes mxGraph.getBorderSizes for quirks [JavaScript]
- Adds ignoreWidth/-Height arguments in mxGraph.fit [JavaScript]
- Adds padding, border and margin in mxGraph.fit [JavaScript]
- Adds mxUtils.forEach helper function [JavaScript]
- Remove stylesheet for IE6 in grapheditor [JavaScript]
- Removes tapAndHoldStartsConnection, showConnectorImg in grapheditor [JavaScript]
- Moves useLocalStorage, counter to Editor in grapheditor [JavaScript]
- Moves fileSupport, touchStyle to Graph in grapheditor [JavaScript]

15-APR-2016: *******

- Fixes lazy shape loading in grapheditor [JavaScript]
- Fixes isOffline is not a function in grapheditor [JavaScript]

13-APR-2016: *******

- Preserves visible state of text node during update [JavaScript]
- Fixes cache invalidation for plain text labels [JavaScript]
- Fixes textOpacity not updated after change [JavaScript]
- Fixes spacing in mxText shape, reset of spacing [JavaScript]
- Fixes compile errors in Dotnet project [Dotnet]
- Adds mxGraphModel.ignoreRelativeEdgeParent switch [JavaScript]
- Fixes reset of mxText.unrotatedBoundingBox [JavaScript]
- Fixes delayed selection of non-movable child cells [JavaScript]

06-APR-2016: *******

- Fixes vertex label moving with live preview [JavaScript]
- Adds argument in mxGraph.createEdgeHandler [JavaScript]
- Updates handler after style change of cell [JavaScript]
- Resets text shape state before using new style [JavaScript]
- Fixes gradients for certain URLs in IE11/Edge [JavaScript]
- Adds comic style and shapes in grapheditor [JavaScript]
- Fixes 1 pixel offset for move preview [JavaScript]
- Adds mxSelectionCellsHandler.updateHandler [JavaScript]
- Changes default for mxConstants.ARROW_SPACING to 0 [JavaScript]
- Fixes chart.js output in jquery.html example [JavaScript]
- mxCellRenderer.redrawShape creates new shape if needed [JavaScript]
- mxStyleChange no longer requires a new mxCellState [JavaScript]
- Avoids unnecessary text repaints in mxCellRenderer [JavaScript]
- Adds mxShape.initStyles, resetStyles, mxText.resetStyles [JavaScript]
- Fixes mxUtils.equalEntries for new properties in second argument [JavaScript]
- Adds export for strokeAlpha and fillAlpha in .NET backend [Dotnet]
- Fixes export of strokeAlpha and fillAlpha in Java backend [Java]

01-MAR-2016: *******

- Ctrl+Shift+Drag added for create/remove space in grapheditor [JavaScript]
- Adds mxPrintPreview.writePostfix hook [JavaScript]
- Adds mxSvgCanvas2D.foOffset, textOffset [JavaScript]
- Adds mapping argument for mxGraph.importCells, moveCells, cloneCells [JavaScript]
- Adds mxClient.IS_CHROMEAPP [JavaScript]
- Uses getBoundingClientRect in mxUtils.getOffset [JavaScript]
- Fixes text size for hidden containers in mxText [JavaScript]
- Fixes valid data URIs in mxGraph.postProcessCellStyle [JavaScript]
- Fixes bounding box for special cases of empty strings [JavaScript]
- Fixes label rendering for schema example in IE10/11 [JavaScript]
- Adds mxUtils.getAll asynchronous request barrier [JavaScript]
- Adds asynchronous loading for resources in grapheditor [JavaScript]
- Adds mxResources.loadResources, callback argument in add [JavaScript]
- Changes type of mxClient.IS_EDGE to boolean [JavaScript]
- Adds mxVertexHandler.isCenteredEvent hook [JavaScript]
- Adds enabled parameter in mxGraph.fit [JavaScript]
- Fixes mxGraph.center for scales other than 1 [JavaScript]
- Removes vendor prefix for MS pointer events [JavaScript]
- Adds mxGraph.removeCellsAfterUngroup hook [JavaScript]
- Fixes autopanning acceleration in mxPanningManager [JavaScript]

18-JAN-2016: *******

- Adds mxGraph.translateToScrollPosition switch [JavaScript]
- Fixes possible NPE in mxGraph.getBorderSizes [JavaScript]
- Fixes undefined uiTheme in grapheditor example
- Sets currentFocus in mxConstraintHandler.setFocus w/o constraints [JavaScript]
- Osgify the JGraphX lib using the maven-bundle-plugin [Java]
- Adds mxObjectCodec.isAttributeIgnored hook [JavaScript]

08-JAN-2016: *******

- Fixes inconsistent loop routing for fixed connection points [JavaScript]

06-JAN-2016: *******

- Fixes preview for fixed points in mxConnectionHandler, mxEdgeHandler [JavaScript]
- Adds mxGraphView.isLoopStyleEnabled, mxEdgeStyle.getJettySize [JavaScript]
- Adds mxConstants.STYLE_ORTHOGONAL_LOOP for orthogonal loop routing [JavaScript]
- Adds mxConstants.STYLE_(SOURCE/TARGET_)JETTY_SIZE for orthogonal router [JavaScript]

11-DEC-2015: *******

- mxCellTracker.mouseUp no longer invokes reset [JavaScript]
- Adds mxConstants.ARROW_BLOCK/OPEN/CLASSIC_THIN and markers [JavaScript]
- Fixes mxConstraintHandler.setFocus for disabled state [JavaScript]
- Adds mxKeyHandler.isEnabledForEvent hook [JavaScript]
- Fixes inconsistent text rendering in quirks/IE8 [JavaScript]
- Adds clipping for overflow=width style [JavaScript]
- Fixes label positions for some overflows and alignments [JavaScript] 
- Moves call to stopEditing to end of mxGraph.fireMouseEvent [JavaScript] 
- Fixes cached label width for vertical resize in mxVertexHandler [JavaScript]
- Adds jsondata.html example [JavaScript]
- Removes duplicates in mxGraph.removeCells return value [JavaScript]
- Adds mxUtils.removeDuplicates [JavaScript]
- Fixes redraw and update of word wrapping during mouse event in mxHandle [JavaScript]
- Fixes hit detection for grid, outline in mxConnectionHandler, mxEdgeHandler [JavaScript]
- Uses graph tolerance, adds mxCellHighlight.isHighlightAt [JavaScript]
- Adds mxConnectionHandler.livePreview, cursor and mxConstraintHandler.setFocus [JavaScript]
- Adds optional point argument in mxConstraintHandler.getCellForEvent [JavaScript]
- Fixes mxUtils.getFunctionName for normal strings [JavaScript]
- Adds mxConstraintHandler.createHighlightShape hook [JavaScript]
- Adds mxConstants.HIGHLIGHT_SIZE, HIGHLIGHT_OPACITY [JavaScript]
- Fixes text size for hidden containers in mxSvgCanvas2D [JavaScript]

04-NOV-2015: *******

- Fixes init sequence for format panel
- Fixes NPE in grapheditor example

03-NOV-2015: *******

- Fixes drawing of page breaks in mxGraph.updatePageBreaks [JavaScript]
- Fixes firing of translate event in mxGraph.scrollCellToVisible [JavaScript]
- Adds mxPrintPreview.marginTop and marginBottom [JavaScript]
- Fixes handling of existing waypoints in mxGraph.splitEdge [JavaScript]
- Adds mxConstants.VML_SHADOWCOLOR, mxGuide.getGuideColor hook [JavaScript]
- Adds mxClient.IS_EDGE, fixes mxPrintPreview for Edge [JavaScript]
- Adds mxCellState.setState for restoring state [JavaScript]
- Adds mxCellRenderer.redrawLabelShape hook [JavaScript]
- Fixes possible NPE in mxCellRenderer.insertStateAfter [JavaScript]
- Fixes clipping for edges with hidden terminals in mxPrintPreview [JavaScript]
- Fixes mxEdgeSegmentHandler.connect for changed parent [JavaScript]
- Adds constituent.html example [JavaScript]
- Adds mxResources.replacePlaceholders helper method [JavaScript]
- Adds mxConnectionHandler.isInsertBefore, insertBeforeSource [JavaScript]
- Fixes tolerance for rounding errors in mxEdgeStyle.SegmentConnector [JavaScript]
- Changes mxConstants.HANDLE_SIZE default value to 6 [JavaScript]
- Fixes index for virtual handle in mxEdgeHandler.createHandleShape [JavaScript]
- Fixes 1px offset for image handles with uneven size [JavaScript]
- Fixes cursor for terminal handle in mxEdgeSegment/ElbowEdgeHandler [JavaScript]
- Fixes handling of escape for inactive mxVertexHandler [JavaScript]
- Adds mxCellState.unscaledWidth for cache invalidation [JavaScript]
- Adds mxSvgCanvas2D.cacheOffsetSize, invalidateCachedOffsetSize [JavaScript]
- Adds clipping for faster mxPrintPreview [JavaScript]
- Limits use of getBBox in mxShape to mxArrow/Connector [JavaScript]

01-OCT-2015: *******

- Adds mxGuide.setVisible [JavaScript]
- Adds mxConstants.STYLE_RESIZE_WIDTH/HEIGHT resize styles [JavaScript]
- Fixes handling of movable and resizable in mxGraph.scaleCell [JavaScript]
- Fixes in-place change for input in mxCellCodec.beforeDecode [JavaScript]
- Fixes URL references for Chrome Apps in mxSvgCanvas [JavaScript]
- Adds stroke width for native bbox in mxShape.updateBoundingBox [JavaScript]
- Adds mxGraph.getBoundingBox, mxPrintPreview.backgroundColor [JavaScript]
- Adds targetWindow argument in mxPrintPreview.open [JavaScript]
- Fixes consumed mouse event in mxDragSource.mouseDown [JavaScript]
- Fixes mxEvent.isLeft/Middle/RightMouseButton [JavaScript]
- Fixes scale of preferred page size in mxGraph.sizeDidChange [JavaScript]
- Fixes HTML entities in XHTML for SVG export in IE9/10 [JavaScript]
- Fixes mxGraph.cellsRemoved for loops, terminals in groups [JavaScript]
- Fixes mxGraph.fireMouseEvent for mouseUp on scrollbars in IE11 [JavaScript]
- Fixes possible NaN for scale in mxOutline [JavaScript]
- Replaces connect handle with hover icons in grapheditor
- Adds mxVertexHandler.getHandlePadding hook [JavaScript]
- Renames actual size to reset view in grapheditor
- mxGraph.isEventIgnored no longer returns true while editing [JavaScript]
- Adds mxCellEditor.getBackgroundColor hook [JavaScript]
- Adds mxShape.useSvgBoundingBox switch, uses getBBox in SVG [JavaScript]
- Fixes mxUtils.extractTextWithWhitespace for text sequence [JavaScript]
- Adds Shift+Click to toggle selection state in grapheditor
- Adds optional textAntiAlias argument in mxImageCanvas c'tor [Java]
- Adds optional type argument in mxUtils.createBufferedImage [Java]
- Adds cx, cy args in mxGraph.center [JavaScript]
- Avoids output of invisible nodes in mxSvgCanvas2D [JavaScript]
- Adds margin, fixes scrollbars in mxGraph.fit [JavaScript]

28-AUG-2015: *******

- Uses CSS transforms in mxCellEditor.resize [JavaScript]
- Adds create and destroy for grapheditor instances
- Fixes global mousewheel handling in grapheditor
- Fixes short, straight orthogonal connector case [JavaScript]
- Uses dictionary in mxGraphModel.getTopmostCells [JavaScript]
- Implements sets of cells using mxObjectIdentity [JavaScript]
- Fixes possible NPE in mxObjectIdenity.get [JavaScript]
- Ignores relative edges labels with selected terminals in mxGraph.moveCells [JavaScript]
- Moves logic for ignoring descendants to mxGraph.moveCells [JavaScript]
- Adds mxConstraintHandler.intersects, updateEdgeState hooks [JavaScript]
- Fixes possible NPE In mxGraph.scaleCell [JavaScript]
- Sets mxClient.IS_NS, IS_SF and IS_GC to false in Microsoft Edge [JavaScript]
- Disposes Graphics instance in mxGraphicsCanvas2D.restore [Java]
- Fixes mxGraph.cellsRemoved for recursive delete with no edges [Java,JavaScript]
- Fixes repaint for zoom while editing label in grapheditor
- Fixes font size in format panel for zoomed text in grapheditor
- Adds ctrl+connect to copy source terminal in grapheditor
- Adds ctrl+delete to delete shape with connections in grapheditor
- Deletes shapes and connections separately in grapheditor
- Changes plus icon to arrow icon in grapheditor
- Adds rounded to copy style and default style in grapheditor
- Adds ctrl+mouse wheel zoom on Windows and Linux in grapheditor
- mxKeyHandler ignores key events with alt key pressed [JavaScript]
- Adds event argument in mxConnectionHandler.isCreateTarget [JavaScript]
- Adds mxPlainTextEditor CSS class in common.css [JavaScript]
- Fixes possible NaN in mxArrowConnector [JavaScript]
- mxCellEditor ignores focus lost, adds blurEnabled switch [JavaScript]
- Fixes stylesheets access in mxUtils.show [JavaScript]
- Adds mxUtils.extractTextWithWhitespace [JavaScript]
- Fixes rendering of trailing newline in HTML labels [JavaScript]
- Removes mxCellEditor.modified and accessors, adds initialValue [JavaScript]
- Uses div and contentEditable for text editing in mxCellEditor [JavaScript]
- Fixes parsing of decimal values in mxSaxOutputHandler [Dotnet]
- Fixes possible border for older browsers in mxImageShape [JavaScript]
- Fixes possible NPE in mxEdgeStyle.SegmentConnector [JavaScript]
- Ignores doctype, validation in mxUtils.parseXml for IE<=9 [JavaScript]
- Rearranges menus, removes toolbar items in grapheditor
- Adds locking for layers, updates dialog in grapheditor
- Fixes possible division by zero in mxGraph.fit [JavaScript]
- Adds space+drag for panning, handles tab while editing in grapheditor

23-JUN-2015: *******

- Adds support for mxConstants.STYLE_SWIMLANE_FILLCOLOR [Php,Dotnet]
- Replaces swimlane rendering code to match JavaScript output [Java]
- Adds mxConstants.STYLE_SWIMLANE_LINE, STYLE_ARCSIZE for swimlanes [Java]
- Adds mxCodec.elements, addElement to cache mxCodec.getElementById [Java,Php,Dotnet]
- No longer uses document.getElementById in mxCodec.getElementById [Java,Php,Dotnet]
- Adds pageNumber in mxPrintPreview.renderPage [JavaScript]
- Changes default for mxText.prototype.cacheEnabled to true [JavaScript]

18-JUN-2015: *******

- Adds support for mxConstants.STYLE_SWIMLANE_FILLCOLOR [Java]
- Fixes jetty positions for South and East direction hierarchical layout [Java]
- mxLayoutManager no longer calls parent layout moveCells if selected [JavaScript] 
- Enter key starts editing like F2 key in grapheditor
- Click on sidebar stops editing in grapheditor
- Fixes size of replace shape for touch devices in grapheditor
- Uses (shift) click on plus icon to (connect and) clone in grapheditor
- Uses move cursor for sidebar items in grapheditor
- Fixes timeout handling for IE11 in mxXmlRequest [JavaScript]
- Adds clipboard.html example [JavaScript]
- Limits zoom to 20x in grapheditor
- Sets initial state of general sidebar to expanded in grapheditor
- Fixes delete action during interactive operation in grapheditor
- Removes duplicate points mxEdgeStyle.OrthConnector [JavaScript]
- Fixes rounding errors in mxGraphView.getPerimeterPoint [JavaScript]
- Fixes rounding errors in mxEdgeStyle.OrthConnector [JavaScript]
- Fixes routing tolerance in orthogonal connector [JavaScript]
- Improves mousewheel zoom performance in grapheditor
- Fixes possible NaN in mxShape, mxText for invalid scales [JavaScript]
- Fixes DOM order, scrollbar state in scrollbars.html example [JavaScript]
- Adds mxSvgCanvas2D.updateText for updating cached labels [JavaScript]
- Adds mxText.prototype.cacheEnabled for HTML labels [JavaScript]
- Reduces number of DOM updates in mxCellRenderer.insertStateAfter [JavaScript]
- Adds folding icon in mxCellRenderer.getShapesForState [JavaScript]
- Fixes edge cases in mxStackLayout.moveCell [JavaScript]
- Adds PartialRectangleShape in grapheditor
- Fixes lost connections for duplicate cells in grapheditor
- Runs layouts for groups and finds tree roots in grapheditor
- Adds rounding in mxGraph.updateGroupBounds [JavaScript]
- Fixes moved edges labels when zoom is changed in mxGraphView.getPoint [JavaScript]
- Disables HTML in collapse/expand tooltip [JavaScript]
- Uses connectable parent in mxConnectionHandler, mxEdgeHandler, mxConstraintHandler [JavaScript]
- Fixes handling of direction mask in ER edge style [JavaScript]
- Adds collate and sort shape, schema rows in grapheditor
- Drop on lifelines replaces participant shape in grapheditor
- Adds support for dropTarget style in grapheditor
- Adds clipping for labels with overflow=fill [JavaScript]
- Fixes text size for overflow=fill in mxSvg-/VmlCanvas, mxText [JavaScript]
- Sets value for mxSvgCanvas2D.lineHeightCorrection to 1 [JavaScript]
- Safeguards capability check, fixes offsets in mxSvgCanvas2D [JavaScript]
- Fixes handling of clicks on folding icon on iOS [JavaScript]
- Fixes rectangle rendering for small, divider-less swimlanes [JavaScript]
- Fixes paste here menu for chromeless mode in grapheditor
- Makes toolbar for chromeless mode transparent in grapheditor

19-MAY-2015: *******

- Adds paste here action in contextmenu for grapheditor
- Returns inserted cells in mxClipboard.paste [JavaScript]
- Adds mxCodec.elements, addElement to cache mxCodec.getElementById [JavaScript]
- No longer uses document.getElementById in mxCodec.getElementById [JavaScript]
- Fixes moving and selection for parts and groups in grapheditor
- Adds binary, timeout args in mxUtils.get, mxXmlRequest.send
- Fixes composite drop connect offset in grapheditor
- Removes old UML shapes with HTML labels in grapheditor
- Adds timer for drop connect target switching in grapheditor
- Adds mxCellEditor.applyValue hook [JavaScript]
- Adds mxVertexHandler.moveLabel, resizeVertex, rotateVertex, updateLivePreview [JavaScript]
- Adds extendcanvas.html example [JavaScript]
- Fixes possible NPE in mxGraphView.transformControlPoint [JavaScript]
- Fixes accidental loss of meta data in grapheditor
- Fixes handling of clicks in layers dialog for Chrome dev channel in grapheditor

23-APR-2015: *******

- Adds orthogonalPerimeter for UML activation in grapheditor
- Adds UML sequence diagram building blocks in grapheditor
- Adds timer for replace shape and connect icons in grapheditor
- Adds timer-based outline connect in grapheditor
- Fixes possible NPE in mxGraphView.updateContainerStyle [JavaScript]
- Adds shape search in grapheditor
- Adds UML boundary, entity and control shapes and lifelines in grapheditor
- Fixes image action if no cells are selected in grapheditor
- Adds zoom buttons in scrollbars.html example [JavaScript]
- Adds enabled-if attribute in default popup menu configuration [JavaScript]
- Uses data URIs for various images in grapheditor
- Removes optional attr argument in mxCodec.getElementById [Java,JavaScript,Php,Dotnet]
- Removes mxUtils.findNodeByAttribute, selectSingleNode [Java,JavaScript,Php,Dotnet]
- Adds support for functions in mxObjectIdentity [JavaScript]
- Adds plus click handling for child cells in grapheditor
- Adds support for composite style in grapheditor [JavaScript]
- Disables numeric conversion for value in mxCellCodec [JavaScript]
- Adds mxUtils.isInteger, used in mxObjectCodec.encodeObject [JavaScript]
- Fixes shadow for transparent ellipses in Firefox [JavaScript]
- Adds composite style, no longer selects editing cell in grapheditor
- Adds mxGraph.getEventState, mxMouseEvent.sourceState [JavaScript]
- Adds handle for hexagon shape in grapheditor
- Fixes undo for text editing and insert labels in grapheditor
- Uses Ctrl+Shift+Z for redo on Mac in grapheditor
- Changes keyboard shortcut for autosize to Ctrl+Shift+Y in grapheditor
- Adds mxKeyHandler.isEventIgnored hook [JavaScript]
- Fixes undo behaviour for text editing with no changes in grapheditor
- No longer calls getCellAt for Graph.dblClick in grapheditor
- Bypasses mxShape.getLabelBounds for non centered labels [JavaScript]
- Adds mxGraphView.getFixedTerminalPoint/getFloatingTerminalPoint hooks [JavaScript]
- Fixes ignored aspect for routing stencil connections [JavaScript]
- Adds UML frame shape and handle, participant style for lifeline in grapheditor
- Adds support for recursiveResize style in grapheditor
- Ignores pointer events for transparent groups in grapheditor
- Fixes handling of keys for substrings in mxUtils.setStyle [JavaScript]
- Fixes removing cells from selected parents in grapheditor
- Adds mxConstants.STYLE_POINTER_EVENTS and support in mxRectangleShape.paintBackground [JavaScript]
- Adds evtName argument in mxGraph.updateMouseEvent [JavaScript]
- Adds optional ignoreFn argument in mxGraph.getCellAt [JavaScript]
- Fixes return type in mxCell.isVertex/-Edge/-Connectable/-Visible/-Collapsed [JavaScript]
- Fixes transition to unconnectable parent in mxConstraintHandler.update [JavaScript]
- Removes checks for shape.content in mxCellRenderer.installListeners [JavaScript]
- Fires UP/DOWN events after validation in mxCurrentRootChange.execute [JavaScript]
- Fixes update of unchanged geometries in mxStackLayout [JavaScript]  
- Fixes enabled state of navigation menu, renames navigation switch/removes from sidebar in grapheditor
- Fixes possible NPE in mxResources.add [JavaScript]
- Fixes possible NPE in mxAbstractCanvas2D.restore [JavaScript]
- Hides inactive custom handles for edges if custom handle is active [JavaScript]

13-MAR-2015: *******

- Adds arcSize handle in grapheditor [JavaScript]
- Fixes scrolling in scrollbars.html example [JavaScript]
- Adds padding in mxGraph.fit for IE 9 and later [JavaScript] 
- Adds shape argument in mxCellRenderer.initializeLabel [JavaScript]
- Fixes secondlabel.html example, adds wrapping [JavaScript]
- Adds keyboard shortcuts for zoom actions in grapheditor
- Adds Or-, Sum- and LineEllipse shape in Advanced sidebar
- Adds live preview for custom edge handles, fixes mxHandle.reset [JavaScript]
- Shows/hides link hint if link changes in grapheditor
- Removes text format change for edit link action in grapheditor
- Fixes conversion of newlines for formattedText action in grapheditor
- Adds rounding in mxConnectionHandler.createTargetVertex [JavaScript]
- Adds EditorUi.setFoldingEnabled, foldingEnabledChanged event in grapheditor
- Adds mxCellRenderer.createControlClickHandler hook [JavaScript]
- Adds key shortcuts for select next/previous/parent/child (Ctrl+Tab/Ctrl+Shift+Tab) in grapheditor
- Changes key shortcuts for edit and enter group (Ctrl+Shift+Home/End) in grapheditor
- Changes key shortcuts for expand and collapse (Ctrl+Home/End) in grapheditor
- Fixes focus after stop editing with Ctrl+Enter in grapheditor
- Adds Ctrl+Enter for cloning cells "in-place" in grapheditor
- Shift+expand/collapse moves sibling cells (experimental) in grapheditor
- Removes mxSpaceManager class [Java,JavaScript]
- Shift+expand/collapse moves neighbour cells in grapheditor (experimental)
- Sets collapsed swimlane label in vertical/horizontal stack to horizontal/vertical in grapheditor
- Ignores previous width/height for stack child folding in grapheditor
- Disables extending parents with stack layouts on add in grapheditor
- Fixes format panel close icon position for FF in grapheditor
- Fixes delayed selection after folding in grapheditor
- Adds mxMouseEvent argument in mxGraphHandler.isDelayedSelection [JavaScript]
- Maintains cell order for duplicates in grapheditor
- Selects parents after deleting cells in grapheditor
- Adds optional event argument in mxGraph.foldCells [JavaScript]
- Adds optional cell argument in mxGraph.isExtendParentsOnAdd [JavaScript]
- Adds mxStackLayout.resizeParentMax switch [JavaScript]
- Adds UML shapes and elements in grapheditor UML sidebar 
- Changes UML lifeline container state in grapheditor
- Adds support for childLayout style in grapheditor
- Fixes size handles for locked shape in grapheditor
- Adds capture phase, fixes duplicates in mxLayoutManager [JavaScript]
- Fixes mxStackLayout.moveCell for scaled graphs [JavaScript]
- Changes title and size of edit style dialog in grapheditor
- Changes default of mxStencil.allowEval to false [JavaScript]
- Adds control key to apply arrow handle to opposite side in grapheditor
- Fixes arrow handles to appear on same side in grapheditor
- Removes shadow action in mxEditor [JavaScript]
- Removes mxConstants.FONT_SHADOW [Java,JavaScript,Dotnet,Php]

05-MAR-2015: *******

- Invokes turn for click on rotate handle in grapheditor [JavaScript]
- Adds mxVertexHandler.rotateClick hook for clicks on rotate handle [JavaScript]
- Changes sizer cursor in mxOutline to nwse-resize [JavaScript]
- Changes mxCellRenderer.createShape to return shape instead of updating state [JavaScript]
- Fixes zoom buttons for chromeless mode in grapheditor
- Adds mxgraph-js with package.json for automated builds [JavaScript]
- Adds mxConnectionHandler.snapToPreview [JavaScript]
- Adds mxEdgeHandler.isAddVirtualBendEvent, mxEdge/VertexHandler.isCustomHandleEvent [JavaScript]
- Simplified edge entries for general sidebar in grapheditor
- Adds simple arrow, vertical text option, edge shape/style toolbar in grapheditor
- Adds mxArrowConnector shape, mxConstants.SHAPE_ARROW_CONNECTOR [JavaScript]
- Fixes bounding box in mxArrow [JavaScript]
- Disables zoom with mousewheel if dialogs are open in grapheditor
- Adds zoom controls for chromeless mode in grapheditor
- Fixes mxGraph.center for certain scales [JavaScript]
- Adds mxUtils.ptLineDist [JavaScript]
- Adds glass effect to current style in grapheditor
- Fixes gradient color action state in grapheditor
- Adds arrows to advanced sidebar in grapheditor
- Adds connections to general sidebar in grapheditor
- Removes edge styles from toolbar in grapheditor
- Separates waypoints and connection in style panel and menu in grapheditor
- Adds edge width option in arrange panel in grapheditor
- Fixes connection points for tee and corner shape in grapheditor
- Adds rounded support for single- and doubleArrow vertices in grapheditor
- Disables splitting edges with edges in grapheditor
- Inverts order of lineend/-start options for format panel in grapheditor
- Adds support for custom handles in mxEdgeHandler [JavaScript]
- Adds rounding in mxGraphHandler.getPreviewBounds [JavaScript]
- Adds mxEdgeHandler.straightRemoveEnabled switch [JavaScript]
- Adds mxMouseEvent argument in mxEdgeHandler.getPreviewPoints [JavaScript]
- Adds text direction in all canvas implementations [Java,JavaScript,Dotnet]
- Adds mxConstants.STYLE_TEXT_DIRECTION for writing direction (experimental) [JavaScript]
- Fixes mxClient.IS_OP for newer versions of Opera [JavaScript]
- Ignores children of selected parents in mxClipboard.copy [JavaScript] 
- Adds recursive rubberband selection in grapheditor
- Adds decimal values in mxVertexHandler.roundAngle [JavaScript]
- Adds mxEdgeHandler.prototype.virtualBendOpacity [JavaScript]
- Fixes possible NPE in mxVertexHandler.redrawHandles [JavaScript]

09-FEB-2015: *******

- mxText.updateBoundingBox sets unrotatedBoundingBox [JavaScript] 
- Adds additional check for DOMParser functionality [JavaScript]
- Rounds coordinates in mxCellEditor.getEditorBounds [JavaScript]
- Fixes handling of HTML entities for SVG export in quirks, IE11 [JavaScript]
- Adds mxSvgCanvas2D.convertHtml for HTML to XHTML conversion [JavaScript]
- Adds Xml2Svg.java example [Java]
- Fixes mxGraph.extendParent for relative children [JavaScript]
- Fixes mxGraph.getBoundingBoxFromGeometry for relative children if parent is not in cells argument [JavaScript]
- Fixes possible NPE in mxGraph.getBoundingBoxFromGeometry [JavaScript]
- Fixes incorrect header when parsing PNG+XML files [Java]
- Adds mxUtils.toDegree [JavaScript]
- Adds pageScale option for print dialog in grapheditor
- mxPrintPreview.autoOrigin no longer overrides x0, y0 in open [JavaScript]
- Fixes mxDisabled class in explorer.css for IE8 standards [JavaScript]
- Fixes unspecified error in mxPopupMenu.addItem for quirks/IE8 [JavaScript]
- Adds active argument in mxPopupMenu.addItem [JavaScript]

05-JAN-2015: *******

- Fixes flipH/V in mxSvgCanvas2D.image [JavaScript] 
- Fixes offset in mxPrintPreview for poster print in IE8-10 [JavaScript]
- Adds Graph.pasteHtmlAtCaret, insertRow/-Column, deleteRow/-Colum in grapheditor
- Adds Graph.getSelectedElement, getParentByName, selectNode in grapheditor
- Fixes padding, action states, renames straight to sharp in grapheditor
- Moves style action to edit menu, editStyle, layers to view in grapheditor
- Adds mxEvent.EDITING_STOPPED event in mxGraph.stopEditing [JavaScript]
- Adds mxEvent.EDITING_STARTED event in mxGraph.startEditingAtCell [JavaScript]
- Ignores height for rotation raster in mxVertexHandler [JavaScript]
- Removes dashed line style for guides in grapheditor
- Fixes length of guides in mxGraphHandler [JavaScript]
- Fixes missing line-height CSS in inner DIV in mxText [JavaScript]
- Fixes ignored text opacity for quirks, NO_FO in mxText [JavaScript]
- EditorUi.showImageDialog invokes callback with null [JavaScript]
- Fixes rendering of image border in mxImageShape [JavaScript]
- Fixes mxEvent.EVENT_HIDE in mxPopupMenu.hideMenu [JavaScript]
- Removes graph model listener for overridden handlers in grapheditor [JavaScript]
- Fixes possible parent highlight in destroyed mxVertexHandler [JavaScript]
- Adds vertical, horizontal arguments, rounding in mxGraph.center [JavaScript]
- Assigns Ctrl+Shift+R to clearDefaultStyle in grapheditor
- Renames reset- to clearDefaultStyle, reset- to clearWaypoints in grapheditor
- Hides ungroup for containers and edges with labels in grapheditor
- Fixes 1px offset in mxGraphHandler.getPreviewBounds [JavaScript]
- Adds EditorUi.set/getScrollbars, scrollbarsChanged event in grapheditor
- Adds switch shape, moves Editor.resetScrollbars to EditorUi in grapheditor
- Adds mxVertexHandler.updateParentHighlight called from redrawHandles [JavaScript]
- Adds mxImageExport.drawShape, drawText hooks [JavaScript]
- Adds alt key to disable drop into groups for existing cells in grapheditor
- Adds padding to avoid border in mxCellRenderer.getLabelBounds [JavaScript]
- Fixes possible NPE in AnalyzeGraph [Java]
- Fixes mxEdgeSegmentHandler, merges segments in connect [JavaScript]
- Returns point in mxElbowEdgeHandler.convertPoint [JavaScript]
- Adds mxGraphicsCanvas2D.createHtmlDocument hook [Java]
- Changes shortcuts for zoomin, zoomout to ctrl+plus/minus in grapheditor
- Adds PageSetupDialog.getFormats, ExportDialog.getExportParameter in grapheditor
- Fixes arrow directions, possible NPEs in SegmentConnector [JavaScript]
- Adds rounding in OrthConnector, mxGraphView.getPerimeterPoint [JavaScript]
- Adds Graph.linkTarget option in grapheditor
- Shows connect icon with control instead of shift, for selected cells in grapheditor
- Renames switchDirection action to turn in grapheditor
- Rounds coordinates in mxGraph.getConnectionPoint result [JavaScript]
- Adds mxConstraintHandler.isKeepFocusEvent to keep focus with shift key [JavaScript]
- Keeps connection points visible in constraint handler while over cell [JavaScript]
- Fixes group resize for multiple hierarchies in mxHierarchicalLayout [JavaScript]
- mxGraph.selectCells ignores child count for edges [JavaScript]
- Sets mxGraph.nativeDblClickEnabled to false for all browsers [JavaScript]

28-NOV-2014: *******

- Changes background color of sidebar to white in grapheditor
- Fixes autosize in mxCellEditor for IE11 [JavaScript]
- mxCellEditor.resize no longer ignores labelWidth style [JavaScript]
- Uses text bounding box for edge label cells in grapheditor
- Uses state position in handle for vertex with size 1 or 0 [JavaScript]
- Moves cellsInserted event from EditorUi to Graph in grapheditor
- Undo in text editor stops editing after last change in grapheditor
- Fixes focus rectangle for edge label editing in IE in grapheditor
- Fixes gradients with no fill color in mxShape for VML [JavaScript]
- Fixes autoscroll after click on scrollbar in IE11 [JavaScript]
- Uses Helvetica Neue as default CSS font family in grapheditor
- Adds mxVertexHandler.isRotationHandleVisible, fixes possible NPEs [JavaScript]
- mxCellEditor.resize ignores wrapping for cells with size 1 or 0 [JavaScript]
- No longer replaces spaces with &nbsp; in mxCellEditor.resize
- Adds nl2Br style to disable conversion of linefeeds in grapheditor
- Hides handles for editing cell in grapheditor
- Automatically removes empty edge labels in grapheditor
- Double click on edge adds new label in grapheditor
- Adds mxCellRenderer.antiAlias, mxShape.antiAlias switches [JavaScript]
- Rounds all numbers in mxSvgCanvas2D to 2 decimal points [JavaScript]
- Fixes dangling edge handling in mxHierarchicalLayout.getVisibleTerminal [JavaScript]
- Fixes jetty positioning for South and East hierarchical orientation [JavaScript]
- Uses helper method in mxShape.createBoundingBox [JavaScript]
- Fixes italic rich text editing in grapheditor
- Fixes handling of XHTML entities in mxSvgCanvas2D.createDiv [JavaScript]
- Fixes HTML source mode editor size in grapheditor [JavaScript]
- Fixes possible NPE in mxEdgeSegmentHandler.start [JavaScript]
- Fixes bug in mxCellEditor.resize for edge labels [JavaScript]
- Fixes handling of special cases in mxEdgeStyle.SegmentConnector, mxEdgeSegmentHandler [JavaScript]
- Allows for fixed points in mxGraphView.getNextPoint [Java,JavaScript,Dotnet,Php]
- Adds CSS styles for all buttons in grapheditor
- Click on background closes modal dialogs, makes color dialogs closable in grapheditor
- Adds mxCellEditor.minResize [JavaScript]
- Fixes rendering of glass effect for mxRectangleShape in outline mode [JavaScript]
- Fixes scrollbars.html example in FF [JavaScript]
- Fixes rounding errors, intersecting points in mxEdgeStyle.SegmentConnector [JavaScript]
- Uses nearest instead of first handle in mxEdgeHandler.getHandleForEvent [JavaScript]
- Updates visible terminal state for preview in mxEdgeHandler.updatePreviewState [JavaScript]
- Adds alt-wheel zoom, chromeless mode, page view, infinite canvas in grapheditor [JavaScript]
- Adds mxCellRenderer.legacySpacing for spacing with overflow fill/width [JavaScript]
- Fixes top-/leftSpacing in mxCellEditor.resize [JavaScript]
- Fixes special label bounds in mxCellEditor.getEditorBounds for overflow=fill [JavaScript]
- Fixes width in mxCellEditor.resize if word wrapping is enabled [JavaScript]
- Adds mouse event argument in mxConstraintHandler.getTolerance [JavaScript]
- Uses nearest instead of first point in mxConstraintHandler.update [JavaScript]
- Fixes ignored scale for handle center in mxHandle.processEvent [JavaScript]
- Fixes tooltip, dblclick for mxEdgeSegmentHandler bends [JavaScript]
- Adds dblClickHandler argument in mxElbowEdgeHandler.createVirtualBend [JavaScript]
- Adds data store, activity markers and task types in BPMN sidebar
- Fixes possible NPE in mxGraph.constrainChild [JavaScript]
- mxGraph.getBoundsForGroup no longer ignores child edges [JavaScript]
- Uses mxGraph.updateGroupBounds in mxGraphLayout.arrangeGroups [JavaScript]
- Inverts cells order, adds child edges in mxGraph.updateGroupBounds [JavaScript]
- Fixes mxGraph.getConnectionPoint for direction=north [JavaScript]
- Adds mxCompactTreeLayout, mxHierarchicalLayout.maintainParentLocation switch [JavaScript]
- Increments version number to reflect API changes [JavaScript]
- Fixes merging of 2 segments in mxEdgeSegmentHandler [JavaScript]

10-NOV-2014: *******

- Fixes ignored fixed aspect style in mxGraph.scaleCell [JavaScript]
- Adds fixedAspect argument in mxGeometry.scale [JavaScript]
- Fixes spacing for mxShape.getLabelBounds in mxCellEditor.resize [JavaScript]
- Fixes dangling edge validation for fixed points in mxEdgeHandler, mxConnectionHandler [JavaScript]
- Adds validation for fixed points in mxEdgeHandler, mxConnectionHandler [JavaScript]
- Removes mxSession, mxEvent.SESSION and related resources [Java,JavaScript,Dotnet,Php]
- Improves vertical position in mxCellEditor [JavaScript]
- Adds Curly Bracket in General, Parallel Marker in BPMN sidebar
- Adds Corner, Tee, Vertical/Horizontal Tree in Advanced sidebar
- Merges tilt and reverseEdge into switchDirection in grapheditor
- Replaces alt-drag from sidebar with refresh icon in grapheditor
- Alt-/shift-drag from sidebar disables actions in grapheditor
- Adds formatted text state for new connections in grapheditor
- Moves collapsible item to Navigation submenu in grapheditor
- Shows focus rectangle for edge label editing in grapheditor
- Makes formatted text default for all cells in grapheditor
- Resizes text editor for rich text editing in grapheditor
- Rearranges general shapes based on usage in grapheditor
- Hides custom handles for locked cells in grapheditor
- Adds reset default style menu item in grapheditor
- Removes Text & Images sidebar in grapheditor
- Fixes tolerances for managed sizers in mxVertexHandler.redrawHandles [JavaScript]
- mxGraph.getBoundingBoxFromGeometry uses relative child bounds for selected parents [JavaScript]
- Fixes ignored maximumGraphBounds for child cells in mxGraph.constrainChild [JavaScript]
- Fixes white-space CSS in mxUtils.createTable [Java]
- Fixes handling for style overflow=fill in mxCellEditor, adds getCurrentHtmlValue [JavaScript]
- Adds mxGraph.createElbowEdgeHandler, createEdgeSegmentHandler, createEdgeHandler, createVertexHandler [JavaScript]
- Adds mxGraph.createTooltipHandler, createSelectionCellsHandler, createConnectionHandler [JavaScript]
- Adds mxGraph.createGraphHandler, createPanningHandler, createPopupMenuHandler [JavaScript]
- Adds mxCompactTreeLayout.groupPaddingTop, -Right, -Bottom and -Left [JavaScript]
- Adds top-, right-, bottom- and leftBorder in mxGraphLayout.arrangeGroups [JavaScript]
- Fixes ID attributes in SVG for URLs with brackets [JavaScript]
- Fixes text and image rendering for mxStencil in outline mode [JavaScript]
- Fixes tolerance check in mxPopupMenuHandler for touch events [JavaScript]

30-OCT-2014: *******

- Adds workaround for limited number of stylesheets in quirks mode [JavaScript]
- Adds bends argument in mxEdgeHandler.destroyBends [JavaScript]
- Adds mxEdgeHandler.mergeRemoveEnabled, dblClickRemoveEnabled [JavaScript]
- Adds mxEdgeHandler.virtualBendsEnabled and getter, createVirtualBends [JavaScript]
- Fixes double click handling on edge handles in quirks mode [JavaScript]
- mxGraph.fireMouseEvent fires native dblclick event in quirks mode [JavaScript]
- Adds mxConstants.CURSOR_TERMINAL/VIRTUAL_BEND_HANDLE [JavaScript]
- Centers handle shape in mxHandle [JavaScript]
- Fixes possible NPE in mxEdgeHandler.refresh [JavaScript]
- Adds mxEdgeHandler.parentHighlightEnabled, createParentHighlightShape [JavaScript]
- Adds mxVertexHandler.parentHighlightEnabled, createParentHighlightShape [JavaScript]
- Fixes offset for vertical position on mxCellEditor [JavaScript]
- Adds mxDoubleEllipse.getLabelBounds for margin size [JavaScript]
- Includes child cell bounds in mxGraph.constrainChild [JavaScript]
- mxGraph.cellsFolded calls constrainChild if constrainChildrenOnResize is true [JavaScript]
- Handles relative geometries, offsets in mxGraph.getBoundingBoxFromGeometry [JavaScript]
- Fixes optional border argument in mxGraph.getBoundsForGroup [JavaScript]
- Fixes mxGraph.getCells to return vertices and edges only [JavaScript]
- Fixes mxShape.updateHtmlColors for outline mode [JavaScript]
- Fixes cleared selection in mxPopupMenu items in IE8 and earlier [JavaScript]
- Fixes item size for CSS icons with no labels in mxPopupMenu in IE8 standards [JavaScript]
- Resets current cursor in mxShape.clear [JavaScript]
- Fixes cursors for edge handles in IE [JavaScript]
- Disables dragstart for handles in mxEdgeSegmentHandler, mxElbowEdgeHandler [JavaScript]
- Adds alt-shift to disable connect to outline [JavaScript]
- Fixes rendering of clipped HTML labels in SVG [JavaScript]
- Adds mxClient.IS_EM, experimental IE11 enterprise mode support [JavaScript]
- Adds Editor.cancelFirst option in grapheditor example [JavaScript]
- Fixes rounding errors in mxEdgeStyle.OrthConnector [JavaScript]
- Fixes printing for mxUtils.show/printScreen in IE9 standards [JavaScript]
- Fixes rounding for image position in mxHandle [JavaScript]
- Uses center handles for managedSizers in mxVertexHandler [JavaScript]
- Adds mxVertexHandler, mxEdgeHandler.setHandlesVisible [JavaScript]
- Replaces deprecated nodeValue with value for DOM attributes [JavaScript]
- Adds mxShape.addPoints, rounded mxTriangle, mxRhombus, mxHexagon [JavaScript]
- Adds caching for HTML labels in htmllabel.html example [JavaScript]
- Fixes possible NPE in mxEdgeHandler.reset, drawPreview [JavaScript]

09-OCT-2014: *******

- Uses edgeState style for preview shape in mxConnectionHandler [JavaScript]
- Fixes translation of relative geometries in mxGraph.cloneCells [JavaScript]
- Adds event argument in mxDragSource.getDropTarget [JavaScript]
- mxGraph.toggleCellStyle(s) returns new value [JavaScript]
- Fixes possible NPE in mxEdgeHandler.reset [JavaScript]
- Selects new edges after cloning in mxEdgeHandler [JavaScript]

03-OCT-2014: *******

- Adds more cloning of edges if control is pressed in mxEdgeHandler [JavaScript]
- Removes event argument for mxDragSource.stopDrag [JavaScript]
- Adds mxDragSource.isActive, reset, removeDragElement/Listeners [JavaScript]
- Adds terminalInserted for mxEvent.CONNECT in mxConnectionHandler [JavaScript]
- Consumes all mxMouseEvents in mxDragSource for current graph while active [JavaScript]
- Consumes event in mxPanningHandler.mouseUp if active [JavaScript]
- Fires PAN_END if PAN_START was fired in mxPanningHandler [JavaScript]
- Removes possibly blocked ActiveX in mxUtils.createXmlDocument, getXml, parseXml [JavaScript]
- Fixes possible cropping of diagram in mxUtils.show [JavaScript]
- Adds allowEval = true in mxStencil, mxStylesheetCodec, mxDefaultToolbarCodec [JavaScript]
- Converts HTML entities for non-HTML labels in mxGraph.getPreferredSizeForCell [JavaScript]
- Uses mxCellRenderer.getLabelValue in mxGraph.getPreferredSizeForCell [JavaScript]
- Creates state in mxGraph.getPreferredSizeForCell if required [JavaScript]
- Adds mxObjectCodec.allowEval = false switch [JavaScript]
- Adds HTML label sanitation in grapheditor example [JavaScript]
- Fixes inputing edges to roots array of hierarchical layout [JavaScript]
- Fixes offset in mxEdgeHandler.addPointAt for edges in containers [JavaScript]
- Fixes moving of edges in mxVertexHandler.moveChildren [JavaScript]
- Adds mxPrintPreview.printControls [JavaScript]
- Fixes moving of relative child cells in rotated parents [JavaScript]
- Fixes mxGraph.getCellContainmentArea for swimlanes with direction, flipH/V [JavaScript]
- Fixes live preview of relative child resize [JavaScript]
- Adds handles.html example [JavaScript]
- Adds mxHandle, mxVertexHandler.createCustomHandles [JavaScript]
- Adds mxEvent.CUSTOM_HANDLE [JavaScript]
- Adds mxGeometry.rotate, scale [JavaScript]
- Adds mxCellState.updateCachedBounds, getCellBounds, getPaintBounds [JavaScript]
- Fixes multiple moves of descendants in mxGraph.cellsMoved [JavaScript]
- Adds rounding for new label positions in mxEdgeHandler [JavaScript]
- Adds clone argument in mxGraph.getDropTarget [JavaScript]
- Fixes rotation of relative edge child cells in mxGraphView.updateVertexState [JavaScript]
- Adds mxRectangle.fromRectangle static function [JavaScript]
- Adds pom.xml for Maven in JGraphX [Java]
- Fixes relative edge child positions in containers [Java,JavaScript,Dotnet,Php]
- Fixes possible concatenation in mxGeometry.translate, mxGraph.translateCell [JavaScript]
- Calls checkBounds in mxImageExport.drawCellState [JavaScript]
- Adds mxRectangle.rotate90 [JavaScript]
- Adds handles.html example [JavaScript]

11-SEP-2014: *******

- Fixes gradients in SVG on pages with empty search string [JavaScript]
- Fixes rendering of negative values for startSize in mxSwimlane [JavaScript]

10-SEP-2014: *******

- Improves radial tree output [JavaScript]
- Fixes artifacts when moving cells on iOS [JavaScript]
- Fixes underline style for HTML labels in SVG, VML [JavaScript]
- Fixes gradients, clipping in SVG if base tag is used [JavaScript]
- Adds new example drop.html [JavaScript]

29-AUG-2014: *******

- Fixes resize of relative child cells in mxGraph.scaleCell [JavaScript]
- Uses graph.cloneCells for cloning templates in toolbars [JavaScript]
- Adds isCellVisibleFn argument in mxTemporaryCellStates [JavaScript]
- Adds clip argument in mxPrintPreview.addGraphFragment [JavaScript]
- Does no longer ignore all clipped labels in graph bounds [Java/JavaScript]
- Cell style passed to mxText in mxCellRenderer.createLabel [JavaScript]
- Documents values for all style constants in mxConstants [JavaScript]
- Fixes clipping for wrapped HTML labels in image export [Java]
- Fixes hidden VML shape after live preview with size of 0 [JavaScript]
- Spacing ignored for label size if not aligned in center/middle [JavaScript]
- Adds mxConstants.STYLE_LABEL_WIDTH to override label width [JavaScript]
- Fixes bounding box for HTML labels with word wrapping [JavaScript]
- Fixes XHTML export in mxSvgCanvas for IE11 standards [JavaScript]
- Fixes SVG export for HTML labels in IE9 standards mode [JavaScript]
- mxUtils.parseXml uses createXmlDocument in IE9 standards mode [JavaScript]
- Adds mxClient.IS_IE11 [JavaScript]

31-JUL-2014: *******

- Fixes possible NPE in mxEdgeHandler.mouseMove [JavaScript]

25-JUL-2014: *******

- Uses selectAll command in mxCellEditor.startEditing for iOS [JavaScript]
- mxGraph.fireMouseEvent fires mouseUp after partial double click for quirks [JavaScript]
- Ignores document events from container, if mouse not down in mxGraphView [JavaScript]
- Disables dragStart in quirks mode on anchors in mxConstraintHandler [JavaScript]
- Fixes virtual dblClick in mxGraph.fireMouseEvent if disabled [JavaScript]
- Fixes mxUtils.get/setTextContent for HTML nodes in quirks mode [JavaScript]
- Adds mxRadialTreeLayout [JavaScript]
- Resize bend in mxEdgeHandler if intersects with label handle [JavaScript]
- Uses shape.visible to hide label shape in mxEdgeHandler [JavaScript]
- Adds mxShape.visible [JavaScript]
- Fixes mxUtils.getDocumentScrollOrigin for quirks mode [JavaScript]
- Fixes rounding errors in mxVertexHandler [JavaScript]
- mxEdgeHandler calls refresh if waypoints have changed [JavaScript]
- Fixes alignment in mxConnectionHandler.createTargetVertex [JavaScript]
- Adds mxCellMarker.setCurrentState, mxConnectionHandler.getCellAt [JavaScript]
- Adds handling for fixed points in mxEdgeStyle.Loop [JavaScript]
- Adds mxConstants.OUTLINE_HIGHLIGHT_COLOR, OUTLINE_HIGHLIGHT_STROKEWIDTH [JavaScript]
- Adds mxEdgeHandler.createaLabelHandleShape, getCellAt, isSnapToTerminalsEvent [JavaScript]
- Fixes snap to waypoints in edge handler, alt to disable [JavaScript]
- Adds mxConnectionHandler.outlineConnect, isOutlineConnectEvent [JavaScript]
- Adds mxEdgeHandler.outlineConnect, isOutlineConnectEvent [JavaScript]
- Adds mxEdgeHandler.manageLabelShape, checkLabelHandle [JavaScript]
- Adds mxGraph.getOutlineConstraint, mxGraphView.decoratorPane [JavaScript]
- Adds mxSvgCanvas2D.pointerEventsValue [JavaScript]
- Adds mxShape.svgPointerEvents, arrowStrokewidth [JavaScript]
- Uses left or Ctrl-click on Macs in mxEvent.isPopupTrigger [JavaScript]
- Changes default for mxShape.svgStrokeTolerance to 8 [JavaScript]
- Adds mxGraph.center to center graph in container [JavaScript]
- mxCellHighlight clones original shape for highlight [JavaScript]
- Adds mxShape.outline to indicate no fill, outline use [JavaScript]
- mxEdgeHandler.createSelectionShape clones edge shape [JavaScript]
- Adds support for curved style in mxPolyline [JavaScript]
- Moves mxConnector.paintCurvedLine to mxPolyline [JavaScript]
- Fixes tolerance check in mxPopupMenuHandler [JavaScript]
- Adds state parameter in mxGraph.isRecursiveResize [JavaScript]
- Fixes bounds to match preview in mxVertexHandler [JavaScript]
- Fixes offset of overlapping, wrapped labels in mxSvgCanvas2D, mxText [JavaScript]
- mxGraph.getCellAt, getCells use current root as parent [JavaScript]
- Adds mxSwimlane.getTitleSize hook [JavaScript]
- Adds new example htmllabel.html [JavaScript]

11-JUN-2014: *******

- Fixes reconnect event handling for image handles in VML [JavaScript]
- Fixes blocking of select click for mxGraph container in IE [JavaScript]
- mxEdgeHandler.isHandleVisible returns false for entity relation waypoints [JavaScript]
- Adds terminal property for mxEvent.CONNECT in mxConnectionHandler [JavaScript]
- Target null if not inserted in mxConnectionHandler.selectCells [JavaScript]
- Visibility and style changes trigger layout in mxLayoutManager [JavaScript]
- Adds mxCellEditor.isStopEditingEvent, installListeners [JavaScript]
- Disables mxTooltipHandler.show if destroy was called [JavaScript]
- Adds mxGraphView.isCellCollapsed indirection [JavaScript] 

02-JUN-2014: *******

- Uses mxUtils.importNode in mxCodec.encode, mxCellCodec.afterEncode [JavaScript]
- Adds mxUtils.importNode cross-browser implementation [JavaScript]
- Corrects rotated bounding box around port constraints in orthogonal router [JavaScript]
- Uses ActiveX in mxUtils.createaXmlDocument, getXml for IE9 standards [JavaScript]
- Adds mxSvgCanvas2D.getCurrentStrokeWidth hook [JavaScript] 
- Adds experimental support for links in mxAbstractCanvas2D, mxSvgCanvas2D [JavaScript]
- Adds support for links in mxImageExport.getLinkForCellState [JavaScript]
- Adds morph.html example [JavaScript]
- Adds semicolon to line-height CSS in HTML labels [Java]
- Adds mxPerimeter.HexagonPerimeter, mxConstants.PERIMETER_HEXAGON [JavaScript]

15-MAY-2014: *******

- Adds mxGraphView.getEmptyBounds [JavaScript]
- Empty graph returns graph bounds at translate [JavaScript]
- Fixes bug in mxMorphing.updateAnimation [JavaScript]
- Adds mxVertexHandler.rotationCursor, uses crosshair cursor [JavaScript]
- mxUtils.getBoundingBox adds optional rotation center parameter [JavaScript]
- Fixes PAN_START event in mxPanningHandler for forced panning [JavaScript]
- Adds mxVertexHandler.rotationHandleVSpacing, horizontal-/verticalOffset [JavaScript]
- mxGraph.isEventSourceIgnored ignores anchors and some inputs [JavaScript]
- Ignores events with alt and meta in mxEvent.isPopupTrigger [JavaScript]
- Adds mxGraph.consumeMouseEvent hook [JavaScript]
- Checks placeholder value for clearOnChange in mxCellEditor [JavaScript]

12-MAY-2014: *******

- Adds mxGraphHandler, mxEdgeHandler.roundLength for rounding coordinates [JavaScript]
- Adds mxVertexHandler.roundAngle, roundLength for rounding rotate and size [JavaScript]
- Adds updateHint, removeHint in mxGraphHandler, mxVertexHandler, mxEdgeHandler [JavaScript]
- Disables tooltips in mxTooltipHandler while mouse button is pressed [JavaSCript]
- Adds escape event handling in mxConnectionHandler, mxGraphHandler, mxVertexHandler, mxEdgeHandler [JavaScript]
- mxGraph.escape now only fires mxEvent.ESCAPE event [JavaScript]
- Adds mxEdgeHandler.refresh, destroyBends [JavaScript]
- Adds mouse event parameter in mxConnectionHandler.isValidSource [JavaScript]
- Adds mxPanningHandler.isActive, pinchEnabled [JavaScript]
- Moves native gesture event handling from shapes to container [JavaScript]
- Disables autoscroll for multi touch events in mxGraph.fireMouseEvent [JavaScript]
- Ignores multi touch events in mxGraph.startEditingAtCell, mxGraphHandler, mxRubberband, mxPopupMenuHandler [JavaScript]
- Disables handling of multi touch events in mxGraphHandler [JavaScript]
- Adds mxEvent.isMultiTouchEvent [JavaScript]

05-MAY-2014: *******

- Adds mxClient.IS_WIN [JavaScript]
- Adds mxCellEditor.isSelectText function [JavaScript]
- Allows changes of translate, scroll in mxGraph.zoom [JavaScript]
- Fixes mxEvent.isTouchEvent, isMouseEvent for Firefox [JavaScript]
- Sets position:absolute for mxWindow.div in common.css [JavaScript]
- Uses shadow filter for mxWindow only in quirks mode [JavaScript]
- Adds mxOutline.createGraph, getSourceGraphBounds hooks [JavaScript]
- Adds mxShape.shapePointerEvents switch [JavaScript]
- Fixes transformed event handling with mxShape.stencilPointerEvents [JavaScript]
- Removes mxShape.paintStencilShape [JavaScript]
- Changes mxShape.addTransparentBackgroundRectangle to createTransparentSvgRectangle [JavaScript]
- Adds mxGraph.keepEdgesInBackground switch [JavaScript]
- Fixes possible infinite loop via mxGraph.getDropTarget [JavaScript]
- Calls configureShape in mxCellRenderer.initializeShape [JavaScript]
- Fixes origin of currentRoot in mxGraphView [Java,JavaScript]

13-APR-2014: *******

- Adds mxVertexHandler.isRecursiveResize hook [JavaScript]
- Fixes NS_ERROR_FAILURE in mxCellEditor.startEditing for Firefox [JavaScript]
- Adds rectangle fallback for rendering SVG shapes in graph.container [JavaScript]
- Uses display CSS style for mxWindow.show, hide and isVisible [JavaScript]

01-APR-2014: *******

- Fixes NPE in hierarchical layout when resizeParent=true [JavaScript]
- Hides edges with no visible terminals in mxGraphView.updateEdgeState [JavaScript]
- Fixes rounding of bounding box in mxShape and mxText [JavaScript]
- Fixes native HTML label check in mxCellRenderer.insertStateAfter [JavaScript]
- Adds layers dialog in grapheditor example [JavaScript]
- Fixes mxEvent.addMouseWheelListener for IE11 [JavaScript]
- Handles spaces for rgb(a) values in mxHtmlColor [Java]
- Fixes HTML edge label wrapping for geometry.width > 0 [Java]
- Fixes possible NPE in mxOutline.update [JavaScript]

14-MAR-2014: *******

- Adds support for RGB(A) color values in mxHtmlColor [Java]
- Fixes possible NPE in mxGraphView.updateEdgeState [Java,JavaScript,Dotnet,Php]
- Uses selectall document command in mxCellEditor.startEditing [JavaScript]
- Fixes mxPanningHandler.isForcePanningEvent with ignoreCell [JavaScript]
- Fixes mxUtils.getDocumentScrollOrigin in standards mode [JavaScript]
- Adds viewer.html in grapheditor example [JavaScript]
- Fixes possible NPE in mxGraph.isEventSourceIgnored [JavaScript]

27-FEB-2014: *******

- Removes Test label in mxConnectPreview [Java]
- Fixes possible NPE in mxGraph.isEventSourceIgnored [JavaScript]
- Fixes handling of data URIs in mxGraph.postProcessCellStyle [JavaScript]
- Fixes possible type error in mxSvgCanvas2D.createDashPattern, mxVmlCanvas2D.getVmlDashStyle [JavaScript]
- Adds HTML5 doctype in mxPrintPreview for CSS1Compat documents [JavaScript]
- Adds mxXmlRequest.withCredentials option [JavaScript]
- Fixes constrainChild in mxGraph.cellsAdded after extending parent [JavaScript]

21-FEB-2014: *******

- Fixes mxVmlCanvas2D.ellipse, mxVmlCanvas2D.image [JavaScript]

19-FEB-2014: *******

- Adds mxVmlCanvas2D.create(Vml)Element hooks [JavaScript]
- Adds child bounds for connected edges in mxGraph.getCellBounds [Java,Dotnet,Php]
- Replaces mxGraphView.getBoundingBox for arrays with getBounds and boolean argument [Php]
- Adds mxGraphView.validateCell(State), updateCell-/Vertex-/EdgeState, getBoundingBox [Java,JavaScript,Dotnet,Php]
- Removes mxGraphView.validateBounds/-Points and childMoved [Java,JavaScript,Dotnet,Php]
- Adds mxCellState.invalid [Dotnet]
- Ignores empty fore-/background divs in mxGraph.panGraph [JavaScript]
- Adds mxEvent.isMiddleMouseButton [JavaScript]
- Disables DnD copy if cloneEnabled is false in mxGraphHandler [Java]
- Fixes selection in mxGraphHandler for no cloneable cells [Java]
- Invalidates connected edges in mxGraph.removeStateForCell [Java,JavaScript]
- Adds mxAnimation.isRunning [Java,JavaScript]
- Ignores possible NS_ERROR_FAILURE in mxSvgCanvas2D.addTextBackground for FF [JavaScript]
- Removes mxUtils.open, save, saveAs and copy [JavaScript]

10-FEB-2014: *******

- Adds mxPrintPreview.printBackgroundImage switch [JavaScript]
- Fixes mxVertexHandler to use getSelectionBounds in redrawHandles [JavaScript]
- Adds mxDoubleRectangleShape and mxConstants.SHAPE_DOUBLE_RECTANGLE [Java]
- Adds new map.html example for implementing Google Maps overlays [JavaScript]
- Adds relative HTML line heights, mxConstants.ABSOLUTE_LINE_HEIGHT [Java,JavaScript]
- Converts mxUrlConverter to standard class notation [JavaScript]
- Checks for window.DOMParser in mxUtils.parseXml [JavaScript]
- Adds mxXmlRequest.decodeSimulateValues switch [JavaScript]

31-JAN-2014: *******

- Fixes repaint of mxCellHighlight if highlighted cell is deleted [JavaScript]
- Fixes in-place editor position for (vertical)LabelPosition style [JavaScript]
- Fixes repaint after drop in collapsed cell in mxGraph.processChange [JavaScript]
- Fixes swimlane label bounds for STYLE_DIRECTION [JavaScript]
- Adds mxSwimlane.getLabelBounds, used in mxCellRenderer.getLabelBounds [JavaScript]
- Passes vertical bounds into mxShape.getLabelBounds for vertical labels [JavaScript]

22-JAN-2014: *******

- Adds vertex label offsets in mxCellEditor.resize [JavaScript]
- Adds mxStack.borderCollapse switch [JavaScript]
- mxCodec.decode no longer uses eval to convert name to ctor [JavaScript]
- Fixes mxEdgeHandler.moveLabel for non-relative geometries [JavaScript]
- Fixes hit detection order for handles in mxEdgeHandler [JavaScript]

17-JAN-2014: *******

- Removes mxCellPath.create usage for in hierarchical layout [JavaScript]
- Fixes mxUtils.getTextContent for Firefox, adds mxUtils.setTextContent [JavaScript]
- Adds mxUtils.getDocumentScrollOrigin, fixes deprecation warnings [JavaScript]
- Adds update of graph bounds in mxGraphView if rendering is false [JavaScript]
- Ignores edges with no points in mxGraph.getBoundingBoxFromGeometry [JavaScript]
- Creates mxShape in mxGraphView.createState if rendering is false [JavaScript]

13-JAN-2014: *******

- Improves edge crossing calculation performance in hierarchical layout [JavaScript]
- Rounds scale to 2 decimals in mxGraph.zoom, mxOutline.update [JavaScript]
- Fixes lost folding icon size in mxCellRenderer.getControlBounds [JavaScript]
- Corrects issue with the hierarchical layout where there are multiple parallel, bi-directional edges between two
    vertices and the initial traversal from roots doesn't follow the direction the majority of edges travel in. [JavaScript]
- Fixes possible bounding box error for empty strings in mxText [JavaScript]

08-JAN-2014: *******

- Changes disabled CSS class to mxDisabled in common.css, explorer.css [JavaScript]
- Adds anchor tag for sprite, uses pointer cursor in mxPopupMenu [JavaScript] 
- Passes blur event to mxCellEditor.focusLost [JavaScript]
- Adds mxUtils.isAncestorNode [JavaScript]
- Adds mxVertexHandler.isLivePreviewBorder for transparent shapes [JavaScript]
- Adds export examples for exporting SVG and bitmaps using backends [Java,Dotnet]
- Adds mxObjectCodec.isBooleanAttribute, isNumericAttribute [JavaScript]
- Replaces mxObjectCodec.convertValueTo/FromXml with convertAttributeTo/FromXml [JavaScript]
- Fixes image namespaces for SVG export in IE standards mode [JavaScript]
- Adds mxGraph.getCursorForMouseEvent [JavaScript]

20-DEC-2013: *******

- Adds option to rotate port constraints with vertices [JavaScript]
- Fixes label position for live preview in mxVertexHandler [JavaScript]
- Checks for mouseDown in mxGraph.isEventSourceIgnored [JavaScript]
- Updates dialect of text shape in mxCellRenderer.redrawLabel [JavaScript]
- Creates shape with mxCellState in mxGraphView.createState [JavaScript]
- Fixes possible undefined function in mxUtils.clearSelection [JavaScript]
- Adds mxConstants.STYLE_ASPECT for mxVertexHandler.isConstrainedEvent [JavaScript]
- Fixes focus on invisible container in mxDragSource.drop [JavaScript]

09-DEC-2013: *******

- Moves updateDocumentTitle, addBeforeUnloadListener to EditorUi in grapheditor [JavaScript]
- Maintains aspect ratio when holding down shift key in mxVertexHandler [JavaScript]
- Invalidates edges for removed terminals in mxGraph.processChange [JavaScript]
- Fixes mxGraph.getPreferredSizeForCell for HTML with linebreaks [JavaScript]
- Fixes deprecated returnValue in mxGraph, mxMouseEvent, mxEvent [JavaScript]
- Uses pt in HTML instead of mxConstants.PX_PER_PIXEL [Java]
- mxHtmlTextShape, mxGraphView no longer ignore overflow [Java]
- Fixes handling of STYLE_INDICATOR_STROKECOLOR style [JavaScript]
- Adds mxGraph.set-/isPortsEnabled switch [Java]
- Adds mxGraphTransferable.enableImageSupport switch [Java]

18-NOV-2013: *******

- Fixes try-catch flow in mxCodec.decode [JavaScript]
- Fixes mxUtils.isNumeric for certain cases [JavaScript]
- Adds mxStackLayout.marginTop/-Left/-Bottom/-Right, setChild-/updateParentGeometry [JavaScript]

11-NOV-2013: *******

- Minor fixes in grapheditor.html example [JavaScript]
- Fixes reset of validation state in mxTemporaryCellStates [JavaScript]
- Adds resetValidationState in mxGraphView [JavaScript]
- Resets visible style in mxShape.clear after invalid bounds [JavaScript]

01-NOV-2013: *******

- Fixes possible component merge error in hierarchical layout [JavaScript]

28-OCT-2013: *******

- Fixes repaint in mxShape when switch from HTML to VML dialect [JavaScript]
- Adds optional width, height arguments in mxUtils.show [JavaScript]
- Fixes handling of NaN in mxUtils.equalEntries, adds mxUtils.isNaN [JavaScript]
- Removes mxCellRenderer.order(Edge), insertState [JavaScript]
- Removes mxCellRenderer.get/findPreviousStateInContainer [JavaScript]
- Replaces mxCellRenderer.initialize with lazy initialization [JavaScript]
- Adds mxCellRenderer.insertStateAfter, getShapesForState, redrawShape [JavaScript]
- Adds mxGraphView.stateValidated to implement DOM node order [JavaScript]
- Removes orderChanged argument in mxGraphView.invalidate [JavaScript]
- Removes mxCellState.invalidOrder, orderChanged flags [JavaScript]
- Replaces check in mxGraphHandler with mxGraph.isEventSourceIgnored [JavaScript]
- Changes default of mxGraph.keepEdgesInForeground to false [JavaScript]
- Removes mxGraph.ordered, keepEdgesInBackground switches [JavaScript]
- Removes dbeditor.html, extjs.html examples [Java,JavaScript,Dotnet,Php]

23-OCT-2013: *******

- Fixes mxGraph.prototype.autoSizeCell [JavaScript]
- Removes old ExtJs-based grapheditor example [JavaScript]

22-OCT-2013: *******

- Fixes rubberband trigger in IE9 for double click outside tolerance [JavaScript]
- Hides tooltips if mouse leaves container in mxGraph.init [JavaScript]
- Restores double ellips shape, adds documentation for override [JavaScript] 
- Adds mxConstants.STYLE_MARGIN for double ellipse shape [JavaScript]
- Fixes mxEvent.isTouch/MouseEvent for IE11 [JavaScript]
- Fixes textarea height in mxLog for IE11 [JavaScript]
- Uses same backend for grapheditor.html and diagrameditor.html example [Java]
- Adds SVG export, fixes blocked popup in diagrameditor.html example [JavaScript]
- Removes old image export from diagrameditor.html example [JavaScript]
- Adds mxGraph.prototype.autoSizeCellsOnAdd switch [JavaScript]

16-OCT-2013: *******

- Fixes duplicate labels in IE9 print preview, print preview in IE11 [JavaScript]
- Fixes double ellipse shape to fill only inner circle, adds inset style [JavaScript]
- Fixes scrollbar positions after optimizeVmlReflows in mxGraphView [JavaScript]
- Fixes dx event property in mxGraph.cellsMoved [JavaScript]
- Adds mxVertexHandler.constrainGroupByChildren switch [JavaScript]
- Fixes mxGraph.keepEdgesInBackground/Foreground switch [JavaScript]
- Adds example for XML input/output to/from Backend [Php]
- Ignores possible NS_ERROR_FAILURE in mxText.updateBoundingBox for FF [JavaScript]
- Fixes mxGraphHandler.selectDelayed for popup event on selected cell [JavaScript]
- Adds boundary.html example [JavaScript]
- Adds new mxGraphHandler.getDelta hook [JavaScript]

27-SEP-2013: *******

- Adds mxGeometry.equals, geometry check in mxGraph.processChange [JavaScript]
- Fixes possible NPE in mxPoint.equals, mxRectangle.equals [JavaScript]
- Adds example for XML input/output to/from Backend [JavaScript,Java,Dotnet]
- Fixes handling of NaN in mxUtils.equalEntries [JavaScript]
- Fixes check for redrawing edges in mxCellRenderer.redraw [JavaScript]
- Adds Maven targets in JGraphX build.xml [Java]

25-SEP-2013: *******

- Fixes page selector for Firefox, minor bugs for quirks mode in mxPrintPreview [JavaScript]
- Fixes ignored tolerance for double click within double click delay in IE9 [JavaScript]
- Fixes usage of mxText in secondlabel.html example for quirks mode [JavaScript]
- Fixes use of white-space CSS in layout-, process- and workfloweditor.html examples [JavaScript]
- Moves calls to mxText.updateMargin from mxCellRenderer to mxText [JavaScript]

19-SEP-2013: *******

- Adds mxGraph.cellResized, resize/constrainChildCells, scaleCell functions [JavaScript] 
- Adds optional recurse argument in mxGraph.resizeCells, cellsResized [JavaScript]
- Adds mxGraph.recursiveResize, constrainChildrenOnResize, extendParentsOnMove with accessors [JavaScript]
- Fixes ignored right click after left click within double click interval [JavaScript]
- Ignores overlapping points in mxGdiCanvas [Dotnet]
- Fixes label clipping for mxGraph.labelsClipped switch [Java]
- Adds showregion.html example [JavaScript]
- Fixes page break dash pattern is no longer scaled [JavaScript]

09-SEP-2013: *******

- Fixes NPE in Shapes.js for grapheditor example on touch devices [JavaScript]
- Fixes undefined value in FF for null defaultValue in mxUtils.prompt [JavaScript]
- Fixes double tap handling in IE for touch devices [JavaScript]

03-SEP-2013: *******

- Fixes non-native double click handling in quirks mode [JavaScript]
- Adds anchors.html example [JavaScript]
- Hides rotation handle for small vertices in mxVertexHandler [JavaScript]
- Adds parent orientation in mxSwimlaneManager.resizeSwimlane [JavaScript,Java]
- Ignores overlapping points in mxConnectorShape.getMarkerVector, mxGraphics2DCanvas.paintPolyline [Java]
- Adds cloning of label in mxCellState.clone [Java]
- Fixes minor bug in mxConnectionHandler.createMarker [JavaScript]

21-AUG-2013: *******

- Adds mxCellEditor.isEventSource hook [JavaScript]
- Adds mxGraph.nativeDblClickEnabled switch for event handling [JavaScript]
- Unifies double click handling for various browsers and versions [JavaScript]
- Changes default value for mxGraph.doubleTapTimeout to 500 ms [JavaScript]
- Fixes double click if style changes after first click [JavaScript]
- Fixes bounding box for stencils with directions, uses constants [JavaScript]
- Adds null check for state.text in mxCellRenderer.getLabelBounds [JavaScript]
- Adds mxText.apply to update text styles [JavaScript]

09-AUG-2013: *******

- Fixes handling of generic list types in mxObjectCodec.SetFieldValue [Dotnet]
- Fixes rendering issues in mxWindow, mx(Rectangle)Shape for IE7/8 standards [JavaScript]
- Adds mxOutline.forceVmlHandles to fix rendering issues for IE8 standards [JavaScript]
- Fixes rendering issues in mxEditor.showOutline for IE8 standards [JavaScript]
- Handles //-prefix as absolute URLs in mxUrlConverter [JavaScript]

05-AUG-2013: *******

- Fixes memory leak in mxEvent.objects array [JavaScript]
- Fixes maximizing mxWindow in standards compliant mode [JavaScript]

31-JUL-2013: *******

- Fixes hierarchical layout dual-direction parallel edges regression [JavaScript]

30-JUL-2013: *******

- Fixes scaling for HTML labels in non-IE browsers [JavaScript]
- Fixes rendering bugs in IE8 standards mode with filters [JavaScript]
- Fixes maximizing mxWindow in standards mode [JavaScript]

26-JUL-2013: *******

- Adds mxRubberband.start, isForcePanningEvent hook [JavaScript]
- Fixes mxRubberband.isForceRubberbandEvent, changes argument to mxMouseEvent [JavaScript]

25-JUL-2013: *******

- Fixes edge calculations in mxGraph.getBoundingBoxFromGeometry [JavaScript]
- Fixes event handling on background image in IE8 standards mode [JavaScript]
- Adds mxGraphView.validateBackgroundImage/Page hooks [JavaScript]
- Redraws edge handlers during live preview in mxVertexHandler [JavaScript]

19-JUL-2013: *******

- Removes internal handler in mxKeyHandler.destroy [JavaScript]
- Fixes possible NPE in mxGraph.getConnectionPoint [JavaScript]
- Fixes mxShape.getSvgScreenOffset for stencils [JavaScript]
- Fixes text wrapping alignment for long words [JavaScript]
- Adds mxClipboard.getCells, setCells hooks [JavaScript]
- Fixes default arcsize for roundrects in stencils [JavaScript]
- Fixes possible NPE in mxAbstractCanvas2D.arcTo [JavaScript]

12-JUL-2013: *******

- Fixes possible index out of bounds in mxPolyline [JavaScript]
- Adds option in tree layout to align ranks [JavaScript]

12-JUL-2013: *******

- Ignores overlapping points in mxConnector, mxPolyline [JavaScript]
- Removes mxClient.CSS_PREFIX, adds mxUtils.setPrefixedStyle [JavaScript]
- Uses mxText base spacing, getLabelBounds hook in mxCellEditor [JavaScript]
- Fixes scaling of dash array in mxSvgCanvas2D [JavaScript]
- Adds hoverstyle.html example [JavaScript]

28-JUN-2013: *******

- Fixes mxDragSource inner mouseDown call for overriding [JavaScript]
- Improves event handling for auto size in mxCellEditor [JavaScript]
- Returns event in mxGraph.updateMouseEvent [JavaScript]
- Improves touch support, hit detection and event handling [JavaScript]
- Adds tolerance before moving handles in mxVertexHandler.checkTolerance [JavaScript]
- Fixes handling of pointer-events in mxSvgCanvas2D.image [JavaScript]
- Moves mxGraph.isForceMarqueeEvent to mxRubberband.isForceRubberbandEvent [JavaScript]
- Moves mxGraph.handleGesture to fireGestureEvent [JavaScript]
- Adds mxGraph.tapAndHold, isEventIgnored, getStateForEvent [JavaScript]
- Moves mxPanningHandler after mxGraphHandler in mxGraph.createHandlers [JavaScript]
- Adds mxEvent.GESTURE, mxEvent.TAP_AND_HOLD in mxGraph [JavaScript]
- Moves tapAndHold detection from mxConnectionHandler to mxGraph [JavaScript]
- Adds handling of touch gestures via event source in mxGraph, mxDragSource [JavaScript]
- Fixes rendering of image background and border in mxImageShape for HTML [JavaScript]
- Uses graph.tolerance for hit detection of zoom handle in mxOutline [JavaScript]
- Adds lod.html, folding.html and collapse.html examples [JavaScript]
- Adds mxCellEditor.selectText, fixes selection for iOS [JavaScript]
- Adds mxClient.IS_IOS, mxClient.IS_FF [JavaScript]
- Fixes hit detection for iOS, removes live preview in mxEdgeHandler [JavaScript]
- No longer consumes events for cursor changes in mxGraphHandler.mouseMove [JavaScript]
- Creates mxPopupMenuHandler in mxGraph.createHandlers [JavaScript]
- Adds mxVertexHandler.manageSizers, live preview for iOS [JavaScript]
- Adds mxTooltipHandler.ignoreTouchEvents switch [JavaScript]
- Adds mxPopupMenuHandler, removes from mxPanningHandler [JavaScript]
- Enables mxConnectionHandler.tapAndHold for mouse events [JavaScript]
- Adds mxShape.createSvgCanvas, createVmlCanvas hooks [JavaScript]
- Fixes handling of disabled pointerEvents in mxSvgCanvas2D [JavaScript]
- Fixes rendering of underlined text in mxText, mxSvgCanvas2D [JavaScript]
- Adds mxEvent.isTouchEvent, isMouseEvent [JavaScript]
- Adds mxOutline.prototype.labelsVisible switch [JavaScript]
- Adds new autolayout.html, touch.html examples [JavaScript]

23-MAY-2013: *******

- Adds mxPanningHandler.prototype.scaleGraph for pinch gesture events [JavaScript]
- Uses capability check for mxClient.IS_TOUCH, adds mxClient.IS_POINTER [JavaScript]
- Adds new visibility.html example [JavaScript]
- Adds support for Microsoft pointer events in IE10 and later [JavaScript]
- Updates Visual Studio files for VS Express for Web 2012 [Dotnet]
- Adds experimental mxVertexHandler, mxEdgeHandler.livePreview switch [JavaScript]
- Adds Sidebar.itemClicked, EditorUi.createSidebarFooterContainer hooks in grapheditor
- Adds layers menu, new shapes in grapheditor [JavaScript]
- Adds overflow=width and changes vertical align for overflow=fill [JavaScript]
- Removes mxCellRenderer.shapes, makes registerShape static, adds getShape [JavaScript]
- Adds mxText.baseSpacingTop/-Bottom/-Left/-Right for legacy support [JavaScript]
- Fixes order of edits in mxGraph.groupCells [JavaScript]
- Changes arguments of mxPrintPreview.renderPage, takes callback [JavaScript]
- Adds mxPrintPreview.getAppendices, getCoverPages hooks [JavaScript]
- Adds mxCellEditor.autoSize, fixes lineHeight and fontStyle [JavaScript]
- Adds mxPanningHandler.triggerX, triggerY for initial event coordinates [JavaScript]
- Adds mxGraph-/mxVertexHandler.prototype.rotationEnabled, rotationRaster [JavaScript]
- Changes default for mxResources.resourcesEncoded to false [JavaScript]
- Adds mxResourceExtension global variable, sets default to .txt [JavaScript]
- Adds align-shape, rotation attributes in text element for stencils [JavaScript]
- Adds mxConstants.STYLE_SWIMLANE_FILLCOLOR, STYLE_SWIMLANE_LINE, LINE_HEIGHT [JavaScript]
- Adds mxClient.VML_PREFIX, OFFICE_PREFIX, CSS_PREFIX [JavaScript]
- Adds dashed option to mxCellHighlight [JavaScript]
