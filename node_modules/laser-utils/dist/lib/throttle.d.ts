import type { Func } from "./types";
type ThrottledFn<T extends Func.Any> = T & {
    flush: () => void;
    cancel: () => void;
};
type Options = {
    wait: number;
    leading?: boolean;
    trailing?: boolean;
};
/**
 * 节流函数
 * @param {Func.Any} fn
 * @param {Options | number} options
 * @returns {ThrottledFn<Func.Any>}
 */
export declare const throttle: <T extends Func.Any>(fn: T, options: Options | number) => ThrottledFn<T>;
export {};
