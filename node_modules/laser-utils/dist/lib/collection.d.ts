import type { Array } from "./types";
import type { Object } from "./types";
export declare class Collection {
    /**
     * Pick
     * @param {Object.Any} target
     * @param {string} keys
     */
    static pick<T extends Object.Any, K extends keyof T>(target: T, keys: K | K[]): Pick<T, K>;
    /**
     * Omit
     * @param {Array.Any | Object.Any} target
     * @param {Array.Any} keys
     */
    static omit<T extends Array.Any>(target: T, keys: T): T;
    static omit<T extends Object.Any, K extends keyof T>(target: T, keys: K | K[]): Omit<T, K>;
    /**
     * Patch 差异
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static patch<T>(a: Set<T> | T[], b: Set<T> | T[]): {
        effects: Set<T>;
        added: Set<T>;
        removed: Set<T>;
    };
    /**
     * Union 并集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static union<T>(a: Set<T> | T[], b: Set<T> | T[]): Set<T>;
    /**
     * Intersection 交集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static intersection<T>(a: Set<T> | T[], b: Set<T> | T[]): Set<T>;
    /**
     * IsSubset 判断子集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static isSubset<T>(a: Set<T> | T[], b: Set<T> | T[]): boolean;
    /**
     * IsSuperset 判断超集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static isSuperset<T>(a: Set<T> | T[], b: Set<T> | T[]): boolean;
    /**
     * Symmetric 对等差集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static symmetric<T>(a: Set<T> | T[], b: Set<T> | T[]): Set<T>;
}
