"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Storage = void 0;
const json_1 = require("./json");
let prefix = "";
let suffix = "__STORAGE__";
/**
 * 补充 key 前后缀
 * @param {string} key
 * @returns {string}
 * */
const convert = (key) => prefix + String(key) + suffix;
/**
 * 序列化
 * @param {T} origin
 * @param {number} ttl
 * @returns {string | null}
 * */
const serialize = (origin, ttl) => {
    try {
        const data = { origin, expire: null };
        if (ttl) {
            const now = Date.now();
            data.expire = ttl + now;
        }
        return (0, json_1.encodeJSON)(data);
    }
    catch (error) {
        console.log("Serialize Storage Error:", error);
        return null;
    }
};
/**
 * 反序列化
 * @param {string} str
 * @returns {null | T}
 * */
const deserialize = (str) => {
    try {
        const data = (0, json_1.decodeJSON)(str);
        if (!data)
            return null;
        if (Number.isNaN(data.expire))
            return null;
        if (data.expire && Date.now() > data.expire)
            return null;
        return data.origin;
    }
    catch (error) {
        console.log("Deserialize Storage Error:", error);
        return null;
    }
};
const base = {
    /**
     * 读取存储数据
     * @param {globalThis.Storage} scope
     * @param {string} name
     * @returns {null | T}
     */
    get: function (scope, name) {
        const key = convert(name);
        const str = scope.getItem(key);
        if (!str)
            return null;
        const origin = deserialize(str);
        if (origin === null)
            this.remove(scope, key);
        return origin;
    },
    /**
     * 设置存储数据
     * @param {globalThis.Storage} scope
     * @param {string} name
     * @param {T} data
     * @param {number} ttl ms
     * @returns {void}
     */
    set: function (scope, name, data, ttl = null) {
        const key = convert(name);
        const str = serialize(data, ttl);
        if (!str)
            return void 0;
        return scope.setItem(key, str);
    },
    /**
     * 读取存储数据 原始值
     * @param {globalThis.Storage} scope
     * @param {string} name
     * @returns {string | null}
     */
    getOrigin: function (scope, name) {
        const key = convert(name);
        return scope.getItem(key);
    },
    /**
     * 移除存储数据
     * @param {globalThis.Storage} scope
     * @param {string} name
     * @returns {void}
     */
    remove: function (scope, name) {
        const key = convert(name);
        return scope.removeItem(key);
    },
};
exports.Storage = {
    /**
     * 设置 key 前缀
     * @param {string} key
     * @returns {void}
     */
    setPrefix: function (key) {
        prefix = key;
    },
    /**
     * 设置 key 后缀
     * @param {string} key
     * @returns {void}
     */
    setSuffix: function (key) {
        suffix = key;
    },
    /** local storage */
    local: {
        /**
         * 读取存储数据
         * @param {string} name
         * @returns {null | T}
         */
        get: function (name) {
            return base.get(localStorage, name);
        },
        /**
         * 设置存储数据
         * @param {string} name
         * @param {T} data
         * @param {number} ttl ms
         * @returns {void}
         */
        set: function (name, data, ttl = null) {
            return base.set(localStorage, name, data, ttl);
        },
        /**
         * 读取存储数据 原始值
         * @param {string} name
         * @returns {string | null}
         */
        getOrigin: function (name) {
            return base.getOrigin(localStorage, name);
        },
        /**
         * 移除存储数据
         * @param {string} name
         * @returns {void}
         */
        remove: function (name) {
            return base.remove(localStorage, name);
        },
    },
    /** session storage */
    session: {
        /**
         * 读取存储数据
         * @param {string} name
         * @returns {null | T}
         */
        get: function (name) {
            return base.get(sessionStorage, name);
        },
        /**
         * 设置存储数据
         * @param {string} name
         * @param {T} data
         * @param {number} ttl ms
         * @returns {void}
         */
        set: function (name, data, ttl = null) {
            return base.set(sessionStorage, name, data, ttl);
        },
        /**
         * 读取存储数据 原始值
         * @param {string} name
         * @returns {string | null}
         */
        getOrigin: function (name) {
            return base.getOrigin(sessionStorage, name);
        },
        /**
         * 移除存储数据
         * @param {string} name
         * @returns {void}
         */
        remove: function (name) {
            return base.remove(sessionStorage, name);
        },
    },
};
