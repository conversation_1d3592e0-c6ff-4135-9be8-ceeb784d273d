export declare class Scroll {
    /**
     * 检查元素 X 轴溢出
     * @param {Element} dom
     * @returns {boolean}
     */
    static isOverflowX(dom: Element): boolean;
    /**
     * 检查元素 Y 轴溢出
     * @param {Element} dom
     * @returns {boolean}
     */
    static isOverflowY(dom: Element): boolean;
    /**
     * X 轴滚动指定距离
     * @param {Element | Window} scroll
     * @param {number} deltaX
     */
    scrollDeltaX(scroll: Element | Window, deltaX: number): void;
    /**
     * Y 轴滚动指定距离
     * @param {Element | Window} scroll
     * @param {number} deltaY
     */
    scrollDeltaY(scroll: Element | Window, deltaY: number): void;
    /**
     * 检查元素滚动到顶部
     * @param {Element} dom
     * @param {number} threshold
     * @returns {boolean}
     */
    static isCloseToTop(dom: Element, threshold?: number): boolean;
    /**
     * 检查元素滚动到底部
     * @param {Element} dom
     * @param {number} threshold
     * @returns {boolean}
     */
    static isCloseToBottom(dom: Element, threshold?: number): boolean;
    /**
     * 检查元素滚动到左侧
     * @param {Element} dom
     * @param {number} threshold
     * @returns {boolean}
     */
    static isCloseToLeft(dom: Element, threshold?: number): boolean;
    /**
     * 检查元素滚动到右侧
     * @param {Element} dom
     * @param {number} threshold
     * @returns {boolean}
     */
    static isCloseToRight(dom: Element, threshold?: number): boolean;
    /**
     * 滚动到顶部
     * @param {Element | Window} scroll
     */
    static scrollToTop(scroll: Element | Window): void;
    /**
     * 滚动到底部
     * @param {Element | Window} scroll
     */
    static scrollToBottom(scroll: Element | Window): void;
    /**
     * 滚动到左侧
     * @param {Element | Window} scroll
     */
    static scrollToLeft(scroll: Element | Window): void;
    /**
     * 滚动到右侧
     * @param {Element | Window} scroll
     */
    static scrollToRight(scroll: Element | Window): void;
}
