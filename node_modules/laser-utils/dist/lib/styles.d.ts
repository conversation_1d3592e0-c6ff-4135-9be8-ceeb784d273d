import type { Primitive } from "./types";
export declare class Styles {
    /**
     * 转换为像素值
     * @param {string | number | Primitive.Nil} value
     * @returns {string | null}
     */
    static pixelate(value: string | number | Primitive.Nil): string | null;
    /**
     * 转换为数字值
     * @param {string | number | Primitive.Nil} value
     * @returns {number | null}
     */
    static digitize(value: string | number | Primitive.Nil): number | null;
    /**
     * 设置样式到 DOM
     * @param {HTMLElement} dom
     * @param {Record<T, CSSStyleDeclaration[T]>} styles
     * @returns {void}
     */
    static setToDOM<T extends keyof CSSStyleDeclaration>(dom: HTMLElement, styles: Record<T, CSSStyleDeclaration[T]>): void;
    /**
     * 组合计算 class-name plain
     * @param {Array<unknown>} values
     * @returns {string}
     */
    static classes(...values: Array<unknown>): string;
    /**
     * 组合计算 class-name complex
     * @param {Array<unknown>} values
     * @returns {string}
     */
    static cx(...values: Array<unknown>): string;
    /**
     * 为 DOM 创建动画效果
     * @param {Element | Primitive.Nil} dom
     * @param {Keyframe[] | PropertyIndexedKeyframes} keyframes
     * @param {number | KeyframeAnimationOptions} options
     * @returns {Primitive.Nil | Animation}
     */
    static animation(dom: Element | Primitive.Nil, keyframes: Keyframe[] | PropertyIndexedKeyframes, options?: number | KeyframeAnimationOptions): Primitive.Nil | Animation;
}
/**
 * 组合计算 class-name plain
 * @param {Array<unknown>} values
 * @returns {string}
 */
export declare const cs: typeof Styles.classes;
/**
 * 组合计算 class-name complex
 * @param {Array<unknown>} values
 * @returns {string}
 */
export declare const cx: typeof Styles.cx;
