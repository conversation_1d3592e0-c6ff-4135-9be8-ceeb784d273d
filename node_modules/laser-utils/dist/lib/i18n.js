"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.I18n = exports.hasOwnProperty = exports.I18N_SYMBOL = void 0;
const format_1 = require("./format");
const is_1 = require("./is");
const types_1 = require("./types");
exports.I18N_SYMBOL = "__I18N_SYMBOL__";
exports.hasOwnProperty = types_1.Object.prototype.hasOwnProperty;
class I18n {
    constructor(language) {
        this._cache = {};
        this._config = {};
        this._override = {};
        this._language = language;
    }
    /**
     * Set language
     * @param {string} language
     * @returns {void}
     */
    setLanguage(language) {
        this._cache = {};
        this._language = language;
    }
    /**
     * Load i18n config
     * @param {T} config
     * @returns {void}
     */
    load(language, config) {
        this._cache = {};
        this._config[language] = config;
    }
    /**
     * Get current language
     * @returns {string}
     */
    getLanguage() {
        return this._language;
    }
    /**
     * Check if language is loaded
     * @param {string} language
     * @returns {boolean}
     */
    isLoaded(language) {
        return !!this._config[language];
    }
    /**
     * Get language payload
     * @param {string} language
     * @returns {T | null}
     */
    getPayload(language) {
        return this._config[language || this._language] || null;
    }
    /**
     * Get locale
     * @returns {T | null}
     */
    get locale() {
        return this._config[this._language];
    }
    /**
     * Set override
     * @param {I18nTypes<T>} override
     * @returns {void}
     */
    override(override) {
        this._override = override;
    }
    /**
     * Format i18n value
     * @param {string} value
     * @param {Record<string, string>} variables
     * @returns {string}
     */
    format(value, variables) {
        if (types_1.Object.keys(variables).length) {
            return format_1.Format.string(value, variables);
        }
        return value;
    }
    /**
     * Compose i18n value
     * @param {keyof I18nTypes<T>} key
     * @param {T} config
     * @returns {string | null}
     */
    getValue(key, config) {
        const keys = key.split(".");
        let current = config;
        for (const key of keys) {
            if (!current || !current[key])
                break;
            current = current[key];
        }
        if (current && (0, is_1.isString)(current)) {
            this._cache[key] = current;
            return current;
        }
        return null;
    }
    /**
     * Translate i18n value
     * @param {keyof I18nTypes<T>} key
     * @param {Record<string, string>} variable
     * @returns {string}
     */
    t(key, variable, defaultValue) {
        const variables = variable || {};
        const preset = this._override[key] || this._cache[key];
        if (preset) {
            return this.format(preset, variables);
        }
        const config = this._config[this._language];
        if (!config) {
            console.warn(`[I18n] Language "${this._language}" is not loaded.`);
            return defaultValue || key;
        }
        const value = this.getValue(key, config);
        if (value) {
            return this.format(value, variables);
        }
        return defaultValue || key;
    }
    /**
     * Seal I18n Entity
     * @param {keyof I18nTypes<T>} key
     * @param {Record<string, string>} variable
     * @param {string} defaultValue
     * @returns {string}
     */
    seal(key, variable, defaultValue) {
        const transform = () => this.t(key, variable, defaultValue);
        const prototype = {
            toJSON: transform,
            valueOf: transform,
            toString: transform,
            [exports.I18N_SYMBOL]: true,
        };
        // 在原型中置于 I18N_SYMBOL 属性
        // 且将返回值强行转换为 string 类型
        return types_1.Object.assign(types_1.Object.create(prototype), {
            k: key,
            v: variable,
            d: defaultValue,
        });
    }
    /**
     * Proxy I18n Entity
     * @param {R} target
     * @returns {R}
     */
    proxy(target) {
        const copied = {};
        for (const key of types_1.Object.keys(target)) {
            const value = target[key];
            const prototype = (0, is_1.isObject)(value) && types_1.Object.getPrototypeOf(value);
            // 判断原型的 I18N_SYMBOL 直属属性
            if (prototype && exports.hasOwnProperty.call(prototype, exports.I18N_SYMBOL)) {
                const entity = value;
                const getter = () => {
                    return this.t(entity.k, entity.v, entity.d);
                };
                types_1.Object.defineProperty(copied, key, {
                    get: getter,
                    enumerable: true,
                    configurable: true,
                });
                continue;
            }
            copied[key] = value;
        }
        return copied;
    }
}
exports.I18n = I18n;
