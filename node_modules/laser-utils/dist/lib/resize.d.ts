export type Rect = {
    width: number;
    height: number;
};
export type ResizeCallback = (prev: Rect, next: Rect) => void;
export declare class Resize {
    private dom;
    private callback;
    private prevWidth;
    private prevHeight;
    private destroy;
    constructor(dom: HTMLElement, callback: ResizeCallback);
    /**
     * 观察元素
     */
    connect(): undefined;
    /**
     * 断开观察
     */
    disconnect(): void;
    /**
     * 基于 ResizeObserver 观察元素
     */
    private observeByResizeObserver;
    /**
     * 基于 iframe 观察元素
     */
    private observeByIframe;
}
