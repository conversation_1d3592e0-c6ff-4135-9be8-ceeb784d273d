import { URI } from "./uri";
export declare class Extract {
    /**
     * 提取 Email 信息
     * @param {string} str
     */
    static email(str: string): {
        name: string;
        domain: string;
    };
    /**
     * 从路径解析数据
     * @param {string} path
     * @param {string} template
     * @returns {Record<string, string>}
     * @example ("/user/123", "/user/:id") => { id: "123" }
     */
    static path: typeof URI.parsePathParams;
}
