/**
 * Decode JSON String To Object
 * @param {string} value
 * @returns {T | null}
 */
export declare const decodeJSON: <T = unknown>(value: string) => T | null;
/**
 * Encode JSON Object To String
 * @param {unknown} value
 * @returns {string | null}
 */
export declare const encodeJSON: (value: unknown) => string | null;
export declare const TSON: {
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    decode: <T = unknown>(value: string) => T | null;
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    encode: (value: unknown) => string | null;
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    parse: <T = unknown>(value: string) => T | null;
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    stringify: (value: unknown) => string | null;
};
