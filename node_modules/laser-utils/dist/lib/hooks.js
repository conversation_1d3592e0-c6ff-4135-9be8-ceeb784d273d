"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useIsFirstRender = exports.useForceUpdate = exports.useMemoFn = exports.useUpdateEffect = exports.useStateRef = exports.useSafeState = exports.useMountState = exports.useIsMounted = void 0;
const react_1 = require("react");
/**
 * 当前组件挂载状态
 */
const useIsMounted = () => {
    const isMounted = (0, react_1.useRef)(false);
    (0, react_1.useEffect)(() => {
        isMounted.current = true;
        return () => {
            isMounted.current = false;
        };
    }, []);
    return {
        mounted: isMounted,
        isMounted: () => isMounted.current,
    };
};
exports.useIsMounted = useIsMounted;
/**
 * 安全地使用 useState
 * @param {S} value 状态
 * @param {MutableRefObject<boolean>} mounted 组件挂载状态
 */
const useMountState = (value, mounted) => {
    const [state, setStateOrigin] = (0, react_1.useState)(value);
    const setCurrentState = (0, react_1.useCallback)((next) => {
        if (!mounted.current)
            return void 0;
        setStateOrigin(next);
    }, []);
    return [state, setCurrentState];
};
exports.useMountState = useMountState;
/**
 * 安全地使用 useState
 * @param {S} value 状态
 */
const useSafeState = (value) => {
    const [state, setStateOrigin] = (0, react_1.useState)(value);
    const { mounted } = (0, exports.useIsMounted)();
    const setCurrentState = (0, react_1.useCallback)((next) => {
        if (!mounted.current)
            return void 0;
        setStateOrigin(next);
    }, []);
    return [state, setCurrentState];
};
exports.useSafeState = useSafeState;
/**
 * State 与 Ref 的使用与更新
 * @param {S} value 状态
 */
const useStateRef = (value) => {
    const [state, setStateOrigin] = (0, react_1.useState)(value);
    const { mounted } = (0, exports.useIsMounted)();
    const ref = (0, react_1.useRef)(state);
    const setState = (0, react_1.useCallback)((next) => {
        if (!mounted.current)
            return void 0;
        ref.current = next;
        setStateOrigin(next);
    }, []);
    return [state, setState, ref];
};
exports.useStateRef = useStateRef;
/**
 * 避免挂载时触发副作用
 * @param {EffectCallback} effect 副作用依赖
 * @param {DependencyList} deps 依赖
 */
const useUpdateEffect = (effect, deps) => {
    const isMounted = (0, react_1.useRef)(false);
    (0, react_1.useEffect)(() => {
        if (!isMounted.current) {
            isMounted.current = true;
        }
        else {
            return effect();
        }
    }, deps);
};
exports.useUpdateEffect = useUpdateEffect;
/**
 * 保证 re-render 时的同一函数引用
 * @param {Func.Any} fn 方法
 */
const useMemoFn = (fn) => {
    const fnRef = (0, react_1.useRef)(fn);
    const memoFn = (0, react_1.useRef)();
    fnRef.current = fn;
    if (!memoFn.current) {
        memoFn.current = function (...args) {
            return fnRef.current.apply(this, args);
        };
    }
    return memoFn.current;
};
exports.useMemoFn = useMemoFn;
/**
 * 强制更新组件
 */
const useForceUpdate = () => {
    const [index, setState] = (0, react_1.useState)(0);
    const update = (0, react_1.useCallback)(() => setState(prev => prev + 1), []);
    return { index, update };
};
exports.useForceUpdate = useForceUpdate;
/**
 * 判断首次渲染
 */
const useIsFirstRender = () => {
    const isFirst = (0, react_1.useRef)(true);
    (0, react_1.useEffect)(() => {
        isFirst.current = false;
    }, []);
    return {
        firstRender: isFirst,
        isFirstRender: () => isFirst.current,
    };
};
exports.useIsFirstRender = useIsFirstRender;
