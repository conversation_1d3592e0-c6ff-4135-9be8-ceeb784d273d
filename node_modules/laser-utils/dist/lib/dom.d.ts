import DOMNodeType = globalThis.Node;
import DOMTextType = globalThis.Text;
import DOMElementType = globalThis.Element;
/**
 * 检查 DOM 节点
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isDOMNode: (value: unknown) => value is DOMNodeType;
/**
 * 检查 DOM 文本节点
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isDOMText: (value: unknown) => value is DOMTextType;
/**
 * 检查 DOM 元素节点
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isDOMElement: (value: unknown) => value is DOMElementType;
/**
 * 检查 DOM 注释节点
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isCommentNode: (value: unknown) => value is Comment;
/**
 * 检查 DOM 文档节点
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isHTMLElement: (value: unknown) => value is HTMLElement;
/**
 * 获取焦点元素
 * @returns {DOMElementType | null}
 */
export declare const getActiveElement: () => DOMElementType | null;
