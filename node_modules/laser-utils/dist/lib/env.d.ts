/** 开发模式 */
export declare const IS_DEV: boolean;
/** 生产模式 */
export declare const IS_PROD: boolean;
/** 测试模式 */
export declare const IS_TEST: boolean;
/** Node 环境 */
export declare const IS_NODE_ENV: boolean;
/** 浏览器环境 */
export declare const IS_BROWSER_ENV: boolean;
/** DOM 环境 */
export declare const IS_DOM_ENV: boolean;
/** Iframe 环境 */
export declare const IS_IFRAME: boolean;
/** 移动端 环境 */
export declare const IS_MOBILE: boolean;
/** PC 环境 */
export declare const IS_WINDOWS: boolean;
/** Mac 环境 */
export declare const IS_MAC: boolean;
/** Linux 环境 */
export declare const IS_LINUX: boolean;
/** Android 环境 */
export declare const IS_ANDROID: boolean;
/** Ipad 环境 */
export declare const IS_IPAD: boolean;
/** IOS 环境 */
export declare const IS_IOS: boolean;
/** Firefox 环境 */
export declare const IS_FIREFOX: boolean;
/** Webkit 环境 */
export declare const IS_WEBKIT: boolean;
/** Chrome 环境 */
export declare const IS_CHROME: boolean;
/** 可信环境 */
export declare const IS_TRUST_ENV: boolean;
