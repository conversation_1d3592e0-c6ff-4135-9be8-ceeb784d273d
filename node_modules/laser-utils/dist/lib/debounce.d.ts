import type { Func } from "./types";
type DebouncedFn<T extends Func.Any> = T & {
    flush: () => void;
    cancel: () => void;
};
type Options = {
    wait: number;
    leading?: boolean;
    trailing?: boolean;
};
/**
 * 防抖函数
 * @param {Func.Any} fn
 * @param {Options | number} options
 * @returns {DebouncedFn<T>}
 */
export declare const debounce: <T extends Func.Any>(fn: T, options: Options | number) => DebouncedFn<T>;
export {};
