"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSON = exports.encodeJSON = exports.decodeJSON = void 0;
/**
 * Decode JSON String To Object
 * @param {string} value
 * @returns {T | null}
 */
const decodeJSON = (value) => {
    try {
        return JSON.parse(value);
    }
    catch (error) {
        console.log("Decode JSON Error:", error);
    }
    return null;
};
exports.decodeJSON = decodeJSON;
/**
 * Encode JSON Object To String
 * @param {unknown} value
 * @returns {string | null}
 */
const encodeJSON = (value) => {
    try {
        return JSON.stringify(value);
    }
    catch (error) {
        console.log("Encode JSON Error:", error);
    }
    return null;
};
exports.encodeJSON = encodeJSON;
exports.TSON = {
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    decode: exports.decodeJSON,
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    encode: exports.encodeJSON,
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    parse: exports.decodeJSON,
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    stringify: exports.encodeJSON,
};
