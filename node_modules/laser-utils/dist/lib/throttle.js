"use strict";
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable @typescript-eslint/no-explicit-any */
Object.defineProperty(exports, "__esModule", { value: true });
exports.throttle = void 0;
const is_1 = require("./is");
const DEFAULT_OPTIONS = {
    wait: 100,
    leading: true,
    trailing: true,
};
/**
 * 节流函数
 * @param {Func.Any} fn
 * @param {Options | number} options
 * @returns {ThrottledFn<Func.Any>}
 */
const throttle = (fn, options) => {
    let lastThis;
    let lastArgs = [];
    let lastInvokeTime = 0;
    let timer = null;
    const config = Object.assign(Object.assign({}, DEFAULT_OPTIONS), (0, is_1.isNumber)(options) ? { wait: options } : options);
    const wait = config.wait;
    const leading = config.leading;
    const trailing = config.trailing;
    /**
     * 清除定时器
     */
    const clear = () => {
        timer && clearTimeout(timer);
        timer = null;
    };
    /**
     * 执行函数
     */
    const invoke = () => {
        lastInvokeTime = Date.now();
        fn.apply(lastThis, lastArgs);
        clear();
    };
    /**
     * 立即执行
     */
    const flush = () => {
        invoke();
        if (leading && !trailing) {
            timer = setTimeout(clear, wait);
        }
    };
    /**
     * 节流
     * @param {unknown} this
     * @param {Array.Any} args
     */
    function throttled(...args) {
        lastThis = this;
        lastArgs = args;
        const now = Date.now();
        if (leading && trailing && (0, is_1.isNil)(timer)) {
            // 此处没有处理多次调用才会触发`trailing`的情况
            // 即单次调用也会同时触发`leading`和`trailing`
            // 如果必须要的话就将后续的`timer`赋值写入`else`
            now - lastInvokeTime > wait && invoke();
            timer = setTimeout(invoke, wait);
            return void 0;
        }
        if (!leading && trailing && (0, is_1.isNil)(timer)) {
            timer = setTimeout(invoke, wait);
            return void 0;
        }
        if (leading && !trailing && (0, is_1.isNil)(timer)) {
            invoke();
            timer = setTimeout(clear, wait);
            return void 0;
        }
    }
    /** 立即执行 */
    throttled.flush = flush;
    /** 清除定时器 */
    throttled.cancel = clear;
    return throttled;
};
exports.throttle = throttle;
