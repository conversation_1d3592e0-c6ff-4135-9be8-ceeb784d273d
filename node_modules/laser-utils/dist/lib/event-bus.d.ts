export interface EventBusType {
}
export type Handler<T extends EventKeys> = {
    once: boolean;
    priority: number;
    listener: Listener<T>;
};
export type EventContext = {
    key: string;
    stopped: boolean;
    prevented: boolean;
    stop: () => void;
    prevent: () => void;
};
export type EventFn<T extends EventKeys> = (payload: EventBusType[T], context: EventContext) => unknown;
export type EventKeys = keyof EventBusType;
export type Listener<T extends EventKeys> = EventFn<T>;
export type Listeners = {
    [T in EventKeys]?: Handler<T>[];
};
export declare class EventBus {
    private listeners;
    /**
     * 监听事件
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     */
    on<T extends EventKeys>(key: T, listener: Listener<T>, priority?: number): void;
    /**
     * 一次性事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     */
    once<T extends EventKeys>(key: T, listener: Listener<T>, priority?: number): void;
    /**
     * 添加事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     * @param {boolean} once
     */
    private addEventListener;
    /**
     * 移除事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     */
    off<T extends EventKeys>(key: T, listener: Listener<T>): undefined;
    /**
     * 触发事件
     * @param {T} key
     * @param {Listener<T>} listener
     * @returns {boolean} prevented
     */
    emit<T extends EventKeys>(key: T, payload: EventBusType[T]): boolean;
    /**
     * 清理事件
     */
    clear(): void;
}
