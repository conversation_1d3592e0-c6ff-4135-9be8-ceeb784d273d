export declare class URI {
    /** 锚点 */
    hash: string;
    /** 端口 */
    port: string;
    /** 路径 */
    path: string;
    /** 主机名 */
    hostname: string;
    /** 协议 */
    protocol: string;
    /** 查询参数 */
    protected _search: Record<string, string[]>;
    constructor();
    /**
     * 主机
     * @returns {string} hostname + port
     */
    get host(): string;
    /**
     * 来源
     * @returns {string} protocol + hostname + port
     */
    get origin(): string;
    /**
     * 查询参数
     * @returns {string} ?key=value&key=value
     */
    get search(): string;
    /**
     * 链接
     * @returns {string} protocol + hostname + port + path + search + hash
     */
    get href(): string;
    /**
     * 设置协议
     * @param {string} protocol
     * @returns {this}
     */
    setProtocol(protocol: string): this;
    /**
     * 设置主机名
     * @param {string} hostname
     * @returns {this}
     */
    setHostname(hostname: string): this;
    /**
     * 设置端口
     * @param {string} port
     * @returns {this}
     */
    setPort(port: string): this;
    /**
     * 设置路径
     * @param {string} path
     * @returns {this}
     */
    setPath(path: string): this;
    /**
     * 设置锚点
     * @param {string} hash
     * @returns {this}
     */
    setHash(hash: string): this;
    /**
     * 获取查询参数
     * @param {string} key
     * @returns {string | null}
     */
    pick(key: string): string | null;
    /**
     * 获取所有查询参数
     * @param {string} key
     * @returns {string[]}
     */
    pickAll(key: string): string[];
    /**
     * 分配查询参数
     * @param {string} key
     * @param {string} value
     * @returns {this}
     */
    assign(key: string, value: string): this;
    /**
     * 追加查询参数
     * @param {string} key
     * @param {string} value
     * @returns {this}
     */
    append(key: string, value: string): this;
    /**
     * 删除查询参数
     * @param {string} key
     * @returns {this}
     */
    omit(key: string): this;
    /**
     * 输出格式化链接
     * @returns {string}
     */
    format(): string;
    /**
     * 从 Location 解析
     * @param {Location} location
     * @returns {URI}
     */
    static from(location: Location): URI;
    /**
     * 解析完整链接
     * @returns {URI}
     * @example https://www.google.com:333/search?q=1&q=2&w=3#world
     */
    static parse(uri: string, baseUrl?: string): URI;
    /**
     * 解析查询参数
     * @param {ConstructorParameters<typeof URLSearchParams>["0"]} query
     * @returns {URI}
     * @example ?q=1&w=3
     */
    static parseParams(query: ConstructorParameters<typeof URLSearchParams>["0"]): URI;
    /**
     * 从路径解析数据
     * @param {string} path
     * @param {string} template
     * @returns {Record<string, string>}
     * @example ("/user/123", "/user/:id") => { id: "123" }
     */
    static parsePathParams(path: string, template: string): Record<string, string>;
}
