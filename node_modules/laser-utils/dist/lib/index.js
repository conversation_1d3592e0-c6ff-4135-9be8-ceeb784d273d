"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isString = exports.isPlainObject = exports.isPlainNumber = exports.isObject = exports.isNumber = exports.isNull = exports.isNil = exports.isFunction = exports.isEmptyValue = exports.isArray = exports.I18n = exports.useUpdateEffect = exports.useStateRef = exports.useSafeState = exports.useMountState = exports.useMemoFn = exports.useIsMounted = exports.useIsFirstRender = exports.useForceUpdate = exports.Format = exports.Extract = exports.EventBus = exports.IS_WINDOWS = exports.IS_WEBKIT = exports.IS_TEST = exports.IS_PROD = exports.IS_NODE_ENV = exports.IS_MOBILE = exports.IS_MAC = exports.IS_LINUX = exports.IS_IPAD = exports.IS_IOS = exports.IS_FIREFOX = exports.IS_DOM_ENV = exports.IS_DEV = exports.IS_CHROME = exports.IS_BROWSER_ENV = exports.IS_ANDROID = exports.isHTMLElement = exports.isDOMText = exports.isDOMNode = exports.isDOMElement = exports.getActiveElement = exports.Bind = exports.debounce = exports.DateTime = exports.ROOT_BLOCK = exports.DEFAULT_PRIORITY = exports.Collection = exports.Clipboard = void 0;
exports.getUniqueId = exports.getId = exports.URI = exports.String = exports.Object = exports.Array = exports.throttle = exports.Styles = exports.cs = exports.Storage = exports.Scroll = exports.Schedule = exports.Resize = exports.RegExec = exports.TSON = exports.encodeJSON = exports.decodeJSON = exports.isUndefined = void 0;
var clipboard_1 = require("./clipboard");
Object.defineProperty(exports, "Clipboard", { enumerable: true, get: function () { return clipboard_1.Clipboard; } });
var collection_1 = require("./collection");
Object.defineProperty(exports, "Collection", { enumerable: true, get: function () { return collection_1.Collection; } });
var constant_1 = require("./constant");
Object.defineProperty(exports, "DEFAULT_PRIORITY", { enumerable: true, get: function () { return constant_1.DEFAULT_PRIORITY; } });
Object.defineProperty(exports, "ROOT_BLOCK", { enumerable: true, get: function () { return constant_1.ROOT_BLOCK; } });
var date_time_1 = require("./date-time");
Object.defineProperty(exports, "DateTime", { enumerable: true, get: function () { return date_time_1.DateTime; } });
var debounce_1 = require("./debounce");
Object.defineProperty(exports, "debounce", { enumerable: true, get: function () { return debounce_1.debounce; } });
var decorator_1 = require("./decorator");
Object.defineProperty(exports, "Bind", { enumerable: true, get: function () { return decorator_1.Bind; } });
var dom_1 = require("./dom");
Object.defineProperty(exports, "getActiveElement", { enumerable: true, get: function () { return dom_1.getActiveElement; } });
Object.defineProperty(exports, "isDOMElement", { enumerable: true, get: function () { return dom_1.isDOMElement; } });
Object.defineProperty(exports, "isDOMNode", { enumerable: true, get: function () { return dom_1.isDOMNode; } });
Object.defineProperty(exports, "isDOMText", { enumerable: true, get: function () { return dom_1.isDOMText; } });
Object.defineProperty(exports, "isHTMLElement", { enumerable: true, get: function () { return dom_1.isHTMLElement; } });
var env_1 = require("./env");
Object.defineProperty(exports, "IS_ANDROID", { enumerable: true, get: function () { return env_1.IS_ANDROID; } });
Object.defineProperty(exports, "IS_BROWSER_ENV", { enumerable: true, get: function () { return env_1.IS_BROWSER_ENV; } });
Object.defineProperty(exports, "IS_CHROME", { enumerable: true, get: function () { return env_1.IS_CHROME; } });
Object.defineProperty(exports, "IS_DEV", { enumerable: true, get: function () { return env_1.IS_DEV; } });
Object.defineProperty(exports, "IS_DOM_ENV", { enumerable: true, get: function () { return env_1.IS_DOM_ENV; } });
Object.defineProperty(exports, "IS_FIREFOX", { enumerable: true, get: function () { return env_1.IS_FIREFOX; } });
Object.defineProperty(exports, "IS_IOS", { enumerable: true, get: function () { return env_1.IS_IOS; } });
Object.defineProperty(exports, "IS_IPAD", { enumerable: true, get: function () { return env_1.IS_IPAD; } });
Object.defineProperty(exports, "IS_LINUX", { enumerable: true, get: function () { return env_1.IS_LINUX; } });
Object.defineProperty(exports, "IS_MAC", { enumerable: true, get: function () { return env_1.IS_MAC; } });
Object.defineProperty(exports, "IS_MOBILE", { enumerable: true, get: function () { return env_1.IS_MOBILE; } });
Object.defineProperty(exports, "IS_NODE_ENV", { enumerable: true, get: function () { return env_1.IS_NODE_ENV; } });
Object.defineProperty(exports, "IS_PROD", { enumerable: true, get: function () { return env_1.IS_PROD; } });
Object.defineProperty(exports, "IS_TEST", { enumerable: true, get: function () { return env_1.IS_TEST; } });
Object.defineProperty(exports, "IS_WEBKIT", { enumerable: true, get: function () { return env_1.IS_WEBKIT; } });
Object.defineProperty(exports, "IS_WINDOWS", { enumerable: true, get: function () { return env_1.IS_WINDOWS; } });
var event_bus_1 = require("./event-bus");
Object.defineProperty(exports, "EventBus", { enumerable: true, get: function () { return event_bus_1.EventBus; } });
var extract_1 = require("./extract");
Object.defineProperty(exports, "Extract", { enumerable: true, get: function () { return extract_1.Extract; } });
var format_1 = require("./format");
Object.defineProperty(exports, "Format", { enumerable: true, get: function () { return format_1.Format; } });
var hooks_1 = require("./hooks");
Object.defineProperty(exports, "useForceUpdate", { enumerable: true, get: function () { return hooks_1.useForceUpdate; } });
Object.defineProperty(exports, "useIsFirstRender", { enumerable: true, get: function () { return hooks_1.useIsFirstRender; } });
Object.defineProperty(exports, "useIsMounted", { enumerable: true, get: function () { return hooks_1.useIsMounted; } });
Object.defineProperty(exports, "useMemoFn", { enumerable: true, get: function () { return hooks_1.useMemoFn; } });
Object.defineProperty(exports, "useMountState", { enumerable: true, get: function () { return hooks_1.useMountState; } });
Object.defineProperty(exports, "useSafeState", { enumerable: true, get: function () { return hooks_1.useSafeState; } });
Object.defineProperty(exports, "useStateRef", { enumerable: true, get: function () { return hooks_1.useStateRef; } });
Object.defineProperty(exports, "useUpdateEffect", { enumerable: true, get: function () { return hooks_1.useUpdateEffect; } });
var i18n_1 = require("./i18n");
Object.defineProperty(exports, "I18n", { enumerable: true, get: function () { return i18n_1.I18n; } });
var is_1 = require("./is");
Object.defineProperty(exports, "isArray", { enumerable: true, get: function () { return is_1.isArray; } });
Object.defineProperty(exports, "isEmptyValue", { enumerable: true, get: function () { return is_1.isEmptyValue; } });
Object.defineProperty(exports, "isFunction", { enumerable: true, get: function () { return is_1.isFunction; } });
Object.defineProperty(exports, "isNil", { enumerable: true, get: function () { return is_1.isNil; } });
Object.defineProperty(exports, "isNull", { enumerable: true, get: function () { return is_1.isNull; } });
Object.defineProperty(exports, "isNumber", { enumerable: true, get: function () { return is_1.isNumber; } });
Object.defineProperty(exports, "isObject", { enumerable: true, get: function () { return is_1.isObject; } });
Object.defineProperty(exports, "isPlainNumber", { enumerable: true, get: function () { return is_1.isPlainNumber; } });
Object.defineProperty(exports, "isPlainObject", { enumerable: true, get: function () { return is_1.isPlainObject; } });
Object.defineProperty(exports, "isString", { enumerable: true, get: function () { return is_1.isString; } });
Object.defineProperty(exports, "isUndefined", { enumerable: true, get: function () { return is_1.isUndefined; } });
var json_1 = require("./json");
Object.defineProperty(exports, "decodeJSON", { enumerable: true, get: function () { return json_1.decodeJSON; } });
Object.defineProperty(exports, "encodeJSON", { enumerable: true, get: function () { return json_1.encodeJSON; } });
Object.defineProperty(exports, "TSON", { enumerable: true, get: function () { return json_1.TSON; } });
var regexp_1 = require("./regexp");
Object.defineProperty(exports, "RegExec", { enumerable: true, get: function () { return regexp_1.RegExec; } });
var resize_1 = require("./resize");
Object.defineProperty(exports, "Resize", { enumerable: true, get: function () { return resize_1.Resize; } });
var schedule_1 = require("./schedule");
Object.defineProperty(exports, "Schedule", { enumerable: true, get: function () { return schedule_1.Schedule; } });
var scroll_1 = require("./scroll");
Object.defineProperty(exports, "Scroll", { enumerable: true, get: function () { return scroll_1.Scroll; } });
var storage_1 = require("./storage");
Object.defineProperty(exports, "Storage", { enumerable: true, get: function () { return storage_1.Storage; } });
var styles_1 = require("./styles");
Object.defineProperty(exports, "cs", { enumerable: true, get: function () { return styles_1.cs; } });
Object.defineProperty(exports, "Styles", { enumerable: true, get: function () { return styles_1.Styles; } });
var throttle_1 = require("./throttle");
Object.defineProperty(exports, "throttle", { enumerable: true, get: function () { return throttle_1.throttle; } });
var types_1 = require("./types");
Object.defineProperty(exports, "Array", { enumerable: true, get: function () { return types_1.Array; } });
Object.defineProperty(exports, "Object", { enumerable: true, get: function () { return types_1.Object; } });
Object.defineProperty(exports, "String", { enumerable: true, get: function () { return types_1.String; } });
var uri_1 = require("./uri");
Object.defineProperty(exports, "URI", { enumerable: true, get: function () { return uri_1.URI; } });
var uuid_1 = require("./uuid");
Object.defineProperty(exports, "getId", { enumerable: true, get: function () { return uuid_1.getId; } });
Object.defineProperty(exports, "getUniqueId", { enumerable: true, get: function () { return uuid_1.getUniqueId; } });
