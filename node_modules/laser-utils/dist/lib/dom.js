"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getActiveElement = exports.isHTMLElement = exports.isCommentNode = exports.isDOMElement = exports.isDOMText = exports.isDOMNode = void 0;
const global = typeof globalThis !== "undefined" ? globalThis : window;
const DOMNode = global.Node;
/**
 * 检查 DOM 节点
 * @param {unknown} value
 * @returns {boolean}
 */
const isDOMNode = (value) => {
    return value instanceof Node;
};
exports.isDOMNode = isDOMNode;
/**
 * 检查 DOM 文本节点
 * @param {unknown} value
 * @returns {boolean}
 */
const isDOMText = (value) => {
    return (0, exports.isDOMNode)(value) && value.nodeType === DOMNode.TEXT_NODE;
};
exports.isDOMText = isDOMText;
/**
 * 检查 DOM 元素节点
 * @param {unknown} value
 * @returns {boolean}
 */
const isDOMElement = (value) => {
    return (0, exports.isDOMNode)(value) && value.nodeType === DOMNode.ELEMENT_NODE;
};
exports.isDOMElement = isDOMElement;
/**
 * 检查 DOM 注释节点
 * @param {unknown} value
 * @returns {boolean}
 */
const isCommentNode = (value) => {
    return (0, exports.isDOMNode)(value) && value.nodeType === DOMNode.COMMENT_NODE;
};
exports.isCommentNode = isCommentNode;
/**
 * 检查 DOM 文档节点
 * @param {unknown} value
 * @returns {boolean}
 */
const isHTMLElement = (value) => {
    return (0, exports.isDOMNode)(value) && value instanceof HTMLElement;
};
exports.isHTMLElement = isHTMLElement;
/**
 * 获取焦点元素
 * @returns {DOMElementType | null}
 */
const getActiveElement = () => {
    let activeElement = document.activeElement;
    while (activeElement && activeElement.shadowRoot && activeElement.shadowRoot.activeElement) {
        activeElement = activeElement.shadowRoot.activeElement;
    }
    return activeElement;
};
exports.getActiveElement = getActiveElement;
