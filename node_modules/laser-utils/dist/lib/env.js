"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IS_TRUST_ENV = exports.IS_CHROME = exports.IS_WEBKIT = exports.IS_FIREFOX = exports.IS_IOS = exports.IS_IPAD = exports.IS_ANDROID = exports.IS_LINUX = exports.IS_MAC = exports.IS_WINDOWS = exports.IS_MOBILE = exports.IS_IFRAME = exports.IS_DOM_ENV = exports.IS_BROWSER_ENV = exports.IS_NODE_ENV = exports.IS_TEST = exports.IS_PROD = exports.IS_DEV = void 0;
const is_1 = require("./is");
/** 开发模式 */
exports.IS_DEV = process.env.NODE_ENV === "development";
/** 生产模式 */
exports.IS_PROD = process.env.NODE_ENV === "production";
/** 测试模式 */
exports.IS_TEST = process.env.NODE_ENV === "test";
/** Node 环境 */
exports.IS_NODE_ENV = typeof process === "object" && is_1.opt.call(process) === "[object process]";
/** 浏览器环境 */
exports.IS_BROWSER_ENV = typeof navigator !== "undefined" &&
    typeof window === "object" &&
    is_1.opt.call(window) === "[object Window]";
/** DOM 环境 */
exports.IS_DOM_ENV = typeof window !== "undefined" &&
    typeof window.document !== "undefined" &&
    typeof window.document.createElement !== "undefined";
/** Iframe 环境 */
exports.IS_IFRAME = exports.IS_BROWSER_ENV && window.self !== window.top;
/** 移动端 环境 */
exports.IS_MOBILE = exports.IS_BROWSER_ENV && /Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);
/** PC 环境 */
exports.IS_WINDOWS = exports.IS_BROWSER_ENV && /Windows/i.test(navigator.userAgent);
/** Mac 环境 */
exports.IS_MAC = exports.IS_BROWSER_ENV && /Mac/i.test(navigator.userAgent);
/** Linux 环境 */
exports.IS_LINUX = exports.IS_BROWSER_ENV && /Linux/i.test(navigator.userAgent);
/** Android 环境 */
exports.IS_ANDROID = exports.IS_BROWSER_ENV && /Android/i.test(navigator.userAgent);
/** Ipad 环境 */
exports.IS_IPAD = exports.IS_BROWSER_ENV && /iPad/i.test(navigator.userAgent);
/** IOS 环境 */
exports.IS_IOS = exports.IS_BROWSER_ENV && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
/** Firefox 环境 */
exports.IS_FIREFOX = exports.IS_BROWSER_ENV && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);
/** Webkit 环境 */
exports.IS_WEBKIT = exports.IS_BROWSER_ENV && /AppleWebKit(?!.*Chrome)/i.test(navigator.userAgent);
/** Chrome 环境 */
exports.IS_CHROME = exports.IS_BROWSER_ENV && /Chrome/i.test(navigator.userAgent);
/** 可信环境 */
exports.IS_TRUST_ENV = exports.IS_BROWSER_ENV && window.isSecureContext;
