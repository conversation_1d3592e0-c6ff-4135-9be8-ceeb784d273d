"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isObjectLike = exports.isBoolean = exports.isPlainObject = exports.isFunction = exports.isString = exports.isPlainNumber = exports.isNumber = exports.isArray = exports.isObject = exports.isNil = exports.isEmptyValue = exports.isNull = exports.isUndefined = exports.opt = void 0;
/**
 * ToString 操作符
 * @description Symbol.toStringTag
 */
exports.opt = Object.prototype.toString;
/**
 * 检查 undefined
 * @param {unknown} value
 * @returns {boolean}
 */
const isUndefined = (value) => {
    return typeof value === "undefined";
};
exports.isUndefined = isUndefined;
/**
 * 检查 null
 * @param {unknown} value
 * @returns {boolean}
 */
const isNull = (value) => {
    return value === null;
};
exports.isNull = isNull;
/**
 * 检查 undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
const isEmptyValue = (value) => {
    return value === null || value === void 0;
};
exports.isEmptyValue = isEmptyValue;
/**
 * 检查 nil = undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
exports.isNil = exports.isEmptyValue;
/**
 * 检查 object
 * @param {unknown} value
 * @returns {boolean}
 */
const isObject = (value) => {
    return exports.opt.call(value) === "[object Object]";
};
exports.isObject = isObject;
/**
 * 检查 Array
 * @param {unknown} value
 * @returns {boolean}
 */
const isArray = (value) => {
    return Array.isArray(value);
};
exports.isArray = isArray;
/**
 * 检查 number
 * @param {unknown} value
 * @returns {boolean}
 */
const isNumber = (value) => {
    return exports.opt.call(value) === "[object Number]";
};
exports.isNumber = isNumber;
/**
 * 检查 plain number
 * @param {unknown} value
 * @returns {boolean}
 */
const isPlainNumber = (value) => {
    return /^(-|\+)?\d+(\.\d+)?$/.test(String(value));
};
exports.isPlainNumber = isPlainNumber;
/**
 * 检查 string
 * @param {unknown} value
 * @returns {boolean}
 */
const isString = (value) => {
    return exports.opt.call(value) === "[object String]";
};
exports.isString = isString;
/**
 * 检查 function
 * @param {unknown} value
 * @returns {boolean}
 */
const isFunction = (value) => {
    return typeof value === "function";
};
exports.isFunction = isFunction;
/**
 * 检查 plain object
 * @param {unknown} value
 * @returns {boolean}
 */
const isPlainObject = (value) => {
    if (!(0, exports.isObject)(value)) {
        return false;
    }
    if (Object.getPrototypeOf(value) === null) {
        return true;
    }
    return value.constructor === Object;
};
exports.isPlainObject = isPlainObject;
/**
 * 检查 boolean
 * @param {unknown} value
 * @returns {boolean}
 */
const isBoolean = (value) => {
    return value === true || value === false || exports.opt.call(value) === "[object Boolean]";
};
exports.isBoolean = isBoolean;
/**
 * 检查 object like
 * @param {unknown} value
 * @returns {boolean}
 */
const isObjectLike = (value) => {
    return !!value && typeof value === "object";
};
exports.isObjectLike = isObjectLike;
