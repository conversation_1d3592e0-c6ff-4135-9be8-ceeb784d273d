"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cx = exports.cs = exports.Styles = void 0;
const is_1 = require("./is");
class Styles {
    /**
     * 转换为像素值
     * @param {string | number | Primitive.Nil} value
     * @returns {string | null}
     */
    static pixelate(value) {
        if (!value)
            return null;
        return (0, is_1.isPlainNumber)(value) ? value + "px" : value;
    }
    /**
     * 转换为数字值
     * @param {string | number | Primitive.Nil} value
     * @returns {number | null}
     */
    static digitize(value) {
        if (!value)
            return null;
        if ((0, is_1.isPlainNumber)(value))
            return Number(value) || null;
        if (value.endsWith("px"))
            return Number(value.replace("px", "")) || null;
        if (value.endsWith("%"))
            return Number(value.replace("%", "")) / 100 || null;
        return null;
    }
    /**
     * 设置样式到 DOM
     * @param {HTMLElement} dom
     * @param {Record<T, CSSStyleDeclaration[T]>} styles
     * @returns {void}
     */
    static setToDOM(dom, styles) {
        Object.entries(styles).forEach(([key, value]) => {
            dom.style[key] = value;
        });
    }
    /**
     * 组合计算 class-name plain
     * @param {Array<unknown>} values
     * @returns {string}
     */
    static classes(...values) {
        return values.filter(r => (0, is_1.isString)(r)).join(" ");
    }
    /**
     * 组合计算 class-name complex
     * @param {Array<unknown>} values
     * @returns {string}
     */
    static cx(...values) {
        const res = [];
        for (const item of values) {
            if (!item) {
                continue;
            }
            if ((0, is_1.isString)(item)) {
                res.push(item);
                continue;
            }
            if ((0, is_1.isObject)(item)) {
                const keys = Object.keys(item).filter(key => item[key]);
                res.push(...keys);
                continue;
            }
        }
        return res.join(" ");
    }
    /**
     * 为 DOM 创建动画效果
     * @param {Element | Primitive.Nil} dom
     * @param {Keyframe[] | PropertyIndexedKeyframes} keyframes
     * @param {number | KeyframeAnimationOptions} options
     * @returns {Primitive.Nil | Animation}
     */
    static animation(dom, keyframes, options) {
        return dom && dom.animate(keyframes, options);
    }
}
exports.Styles = Styles;
/**
 * 组合计算 class-name plain
 * @param {Array<unknown>} values
 * @returns {string}
 */
exports.cs = Styles.classes;
/**
 * 组合计算 class-name complex
 * @param {Array<unknown>} values
 * @returns {string}
 */
exports.cx = Styles.cx;
