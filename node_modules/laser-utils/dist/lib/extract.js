"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Extract = void 0;
const uri_1 = require("./uri");
class Extract {
    /**
     * 提取 Email 信息
     * @param {string} str
     */
    static email(str) {
        const [name, domain] = str.split("@");
        return { name, domain: domain || "" };
    }
}
exports.Extract = Extract;
/**
 * 从路径解析数据
 * @param {string} path
 * @param {string} template
 * @returns {Record<string, string>}
 * @example ("/user/123", "/user/:id") => { id: "123" }
 */
Extract.path = uri_1.URI.parsePathParams;
