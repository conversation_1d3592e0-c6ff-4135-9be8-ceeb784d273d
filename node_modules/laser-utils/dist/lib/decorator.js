"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bind = void 0;
const is_1 = require("./is");
// ExperimentalDecorators
// https://www.typescriptlang.org/docs/handbook/release-notes/typescript-5-0.html
/**
 * Bind 装饰器
 * @param {T} _
 * @param {string} key
 * @param {PropertyDescriptor} descriptor
 */
function Bind(_, key, descriptor) {
    const originalMethod = descriptor.value;
    if (!(0, is_1.isFunction)(originalMethod)) {
        throw new TypeError(`${originalMethod} is not a function`);
    }
    return {
        configurable: true,
        get() {
            const boundFunction = originalMethod.bind(this);
            Object.defineProperty(this, key, {
                value: boundFunction,
                configurable: true,
                enumerable: false,
            });
            return boundFunction;
        },
    };
}
exports.Bind = Bind;
