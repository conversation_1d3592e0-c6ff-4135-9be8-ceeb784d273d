export declare class DateTime extends Date {
    constructor();
    constructor(date: Date);
    constructor(timestamp: number);
    constructor(dateTimeStr: string);
    constructor(year: number, month: number, date?: number, hours?: number, minutes?: number, seconds?: number, ms?: number);
    /**
     * 格式化时间日期
     * @param fmt string? @default: yyyy-MM-dd
     * @desc yyyy 年 MM 月 dd 日 hh 小时 mm 分 ss 秒 S 毫秒
     */
    format(fmt?: string): string;
    /**
     * 调整时间
     * @param {number} years 年
     * @param {number} months 月
     * @param {number} days 日
     */
    add(years?: number, months?: number, days?: number): DateTime;
    /**
     * 精确的时间差
     * @param {DateTime} point
     */
    diff(newDate: DateTime): {
        years: number;
        months: number;
        days: number;
        hours: number;
        minutes: number;
        seconds: number;
    };
    /**
     * 延后到第 N 个月的 1 号
     * @param {number} n
     */
    nextMonth(n?: number): this;
    /**
     * 延后到第 N 天的 0 点
     * @param {number} n
     */
    nextDay(n?: number): this;
    /**
     * 延后到第 N 小时的 0 分钟
     * @param {number} n
     */
    nextHour(n?: number): this;
    /**
     * 延后到第 N 分钟的 0 秒
     * @param {number} n
     */
    nextMinute(n?: number): this;
    /**
     * 延后 N 个月
     * @param {number} n
     */
    deferMonth(n?: number): this;
    /**
     * 延后 N 天
     * @param {number} n
     */
    deferDay(n?: number): this;
    /**
     * 延后 N 小时
     * @param {number} n
     */
    deferHour(n?: number): this;
    /**
     * 延后 N 分钟
     * @param {number} n
     */
    deferMinute(n?: number): this;
    /**
     * 克隆当前时间日期
     * @returns {DateTime}
     */
    clone(): DateTime;
    static from(date: Date | DateTime): DateTime;
}
