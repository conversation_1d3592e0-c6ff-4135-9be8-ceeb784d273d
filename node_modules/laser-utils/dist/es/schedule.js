export class Schedule {
    constructor(maxTask) {
        this.maxTask = maxTask;
        this.running = [];
        this.pending = [];
    }
    /**
     * 提交任务
     * @param {Invoke<T>} fn
     * @returns {Promise<T>}
     */
    commit(fn) {
        return new Promise(resolve => {
            if (this.running.length < this.maxTask) {
                this.dispatch({ fn, resolve });
            }
            else {
                this.pending.push({ fn, resolve });
            }
        });
    }
    /**
     * 触发任务
     * @param {Task<T>} task
     * @returns {void}
     */
    dispatch(task) {
        this.running.push(task.fn);
        task.fn().then(res => {
            task.resolve(res);
            this.running = this.running.filter(fn => fn !== task.fn);
            const nextTask = this.pending.length && this.pending.shift();
            nextTask && this.dispatch(nextTask);
        });
    }
}
