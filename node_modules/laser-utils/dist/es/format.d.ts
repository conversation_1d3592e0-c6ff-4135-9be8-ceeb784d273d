import type { Func } from "./types";
export declare class Format {
    /**
     * 格式化字符串
     * @param {string} str
     * @param {Record<string, string> | string[]} data
     * @returns {string}
     * @example ("1{{0}}1", [1]) => "111"
     * @example ("1{{id}}1", { id: 1 }) => "111"
     */
    static string(str: string, data: Record<string, string> | string[]): string;
    /**
     * 格式化数字
     * @param {number} num
     * @param {number} fixed
     * @returns {string}
     * @example 1123 => "1,123"
     */
    static number(number: number, locale?: string, options?: Func.Constructor<typeof Intl.NumberFormat>["1"]): string;
    /**
     * 格式化字节数
     * @param {number} bytes
     * @param {number} decimals
     * @returns {string}
     * @example 1024 => "1 KB"
     */
    static bytes(bytes: number, decimals?: number): string;
    /**
     * 格式化经过的时间
     * @param {number} ms
     * @returns {string}
     * @example (2000, 1000) => "1 second ago"
     */
    static time(ms: number, relative?: number): string;
}
