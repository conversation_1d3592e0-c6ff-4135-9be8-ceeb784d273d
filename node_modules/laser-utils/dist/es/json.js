/**
 * Decode JSON String To Object
 * @param {string} value
 * @returns {T | null}
 */
export const decodeJSON = (value) => {
    try {
        return JSON.parse(value);
    }
    catch (error) {
        console.log("Decode JSON Error:", error);
    }
    return null;
};
/**
 * Encode JSON Object To String
 * @param {unknown} value
 * @returns {string | null}
 */
export const encodeJSON = (value) => {
    try {
        return JSON.stringify(value);
    }
    catch (error) {
        console.log("Encode JSON Error:", error);
    }
    return null;
};
export const TSON = {
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    decode: decodeJSON,
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    encode: encodeJSON,
    /**
     * Decode JSON String To Object
     * @param {string} value
     * @returns {T | null}
     */
    parse: decodeJSON,
    /**
     * Encode JSON Object To String
     * @param {unknown} value
     * @returns {string | null}
     */
    stringify: encodeJSON,
};
