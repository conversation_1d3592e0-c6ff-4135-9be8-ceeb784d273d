/**
 * ToString 操作符
 * @description Symbol.toStringTag
 */
export declare const opt: () => string;
/**
 * 检查 undefined
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isUndefined: (value: unknown) => value is undefined;
/**
 * 检查 null
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isNull: (value: unknown) => value is undefined;
/**
 * 检查 undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isEmptyValue: (value: unknown) => value is null | undefined;
/**
 * 检查 nil = undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isNil: (value: unknown) => value is null | undefined;
/**
 * 检查 object
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isObject: <T = Record<string, unknown>>(value: unknown) => value is T;
/**
 * 检查 Array
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isArray: (value: unknown) => value is unknown[];
/**
 * 检查 number
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isNumber: (value: unknown) => value is number;
/**
 * 检查 plain number
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isPlainNumber: (value: unknown) => value is number;
/**
 * 检查 string
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isString: (value: unknown) => value is string;
/**
 * 检查 function
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isFunction: (value: unknown) => value is (...args: never[]) => unknown;
/**
 * 检查 plain object
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isPlainObject: <T = Record<string, unknown>>(value: unknown) => value is T;
/**
 * 检查 boolean
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isBoolean: (value: unknown) => value is boolean;
/**
 * 检查 object like
 * @param {unknown} value
 * @returns {boolean}
 */
export declare const isObjectLike: <T = Record<string, unknown>>(value: unknown) => value is T;
