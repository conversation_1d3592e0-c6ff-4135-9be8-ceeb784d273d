import { isArray, isObject } from "./is";
export class Collection {
    /**
     * Pick
     * @param {Object.Any} target
     * @param {string} keys
     */
    static pick(target, keys) {
        const set = new Set(isArray(keys) ? keys : [keys]);
        const next = {};
        for (const key in target) {
            if (!set.has(key))
                continue;
            next[key] = target[key];
        }
        return next;
    }
    static omit(target, keys) {
        const set = new Set(isArray(keys) ? keys : [keys]);
        if (isObject(target)) {
            const next = {};
            for (const key in target) {
                if (set.has(key))
                    continue;
                next[key] = target[key];
            }
            return next;
        }
        return target.filter(item => !set.has(item));
    }
    /**
     * Patch 差异
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static patch(a, b) {
        const prev = a instanceof Set ? a : new Set(a);
        const next = b instanceof Set ? b : new Set(b);
        const effects = new Set();
        const added = new Set();
        const removed = new Set();
        for (const id of next) {
            if (!prev.has(id)) {
                added.add(id);
                effects.add(id);
            }
        }
        for (const id of prev) {
            if (!next.has(id)) {
                removed.add(id);
                effects.add(id);
            }
        }
        return { effects, added, removed };
    }
    /**
     * Union 并集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static union(a, b) {
        return new Set([...a, ...b]);
    }
    /**
     * Intersection 交集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static intersection(a, b) {
        const prev = [...a];
        const next = b instanceof Set ? b : new Set(b);
        return new Set([...prev].filter(id => next.has(id)));
    }
    /**
     * IsSubset 判断子集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static isSubset(a, b) {
        const prev = [...a];
        const next = b instanceof Set ? b : new Set(b);
        return prev.every(id => next.has(id));
    }
    /**
     * IsSuperset 判断超集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static isSuperset(a, b) {
        const prev = a instanceof Set ? a : new Set(a);
        const next = [...b];
        return next.every(id => prev.has(id));
    }
    /**
     * Symmetric 对等差集
     * @param {Set<T> | T[]} a
     * @param {Set<T> | T[]} b
     */
    static symmetric(a, b) {
        const prev = a instanceof Set ? a : new Set(a);
        const next = [...b];
        return new Set(next.filter(id => !prev.has(id)));
    }
}
