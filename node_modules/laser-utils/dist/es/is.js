/**
 * ToString 操作符
 * @description Symbol.toStringTag
 */
export const opt = Object.prototype.toString;
/**
 * 检查 undefined
 * @param {unknown} value
 * @returns {boolean}
 */
export const isUndefined = (value) => {
    return typeof value === "undefined";
};
/**
 * 检查 null
 * @param {unknown} value
 * @returns {boolean}
 */
export const isNull = (value) => {
    return value === null;
};
/**
 * 检查 undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
export const isEmptyValue = (value) => {
    return value === null || value === void 0;
};
/**
 * 检查 nil = undefined | null
 * @param {unknown} value
 * @returns {boolean}
 */
export const isNil = isEmptyValue;
/**
 * 检查 object
 * @param {unknown} value
 * @returns {boolean}
 */
export const isObject = (value) => {
    return opt.call(value) === "[object Object]";
};
/**
 * 检查 Array
 * @param {unknown} value
 * @returns {boolean}
 */
export const isArray = (value) => {
    return Array.isArray(value);
};
/**
 * 检查 number
 * @param {unknown} value
 * @returns {boolean}
 */
export const isNumber = (value) => {
    return opt.call(value) === "[object Number]";
};
/**
 * 检查 plain number
 * @param {unknown} value
 * @returns {boolean}
 */
export const isPlainNumber = (value) => {
    return /^(-|\+)?\d+(\.\d+)?$/.test(String(value));
};
/**
 * 检查 string
 * @param {unknown} value
 * @returns {boolean}
 */
export const isString = (value) => {
    return opt.call(value) === "[object String]";
};
/**
 * 检查 function
 * @param {unknown} value
 * @returns {boolean}
 */
export const isFunction = (value) => {
    return typeof value === "function";
};
/**
 * 检查 plain object
 * @param {unknown} value
 * @returns {boolean}
 */
export const isPlainObject = (value) => {
    if (!isObject(value)) {
        return false;
    }
    if (Object.getPrototypeOf(value) === null) {
        return true;
    }
    return value.constructor === Object;
};
/**
 * 检查 boolean
 * @param {unknown} value
 * @returns {boolean}
 */
export const isBoolean = (value) => {
    return value === true || value === false || opt.call(value) === "[object Boolean]";
};
/**
 * 检查 object like
 * @param {unknown} value
 * @returns {boolean}
 */
export const isObjectLike = (value) => {
    return !!value && typeof value === "object";
};
