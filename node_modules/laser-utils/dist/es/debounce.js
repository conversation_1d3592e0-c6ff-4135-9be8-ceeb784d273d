/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { isNumber } from "./is";
const DEFAULT_OPTIONS = {
    wait: 100,
    leading: false,
    trailing: true,
};
/**
 * 防抖函数
 * @param {Func.Any} fn
 * @param {Options | number} options
 * @returns {DebouncedFn<T>}
 */
export const debounce = (fn, options) => {
    let timer = null;
    let lastThis;
    let lastArgs = [];
    const config = Object.assign(Object.assign({}, DEFAULT_OPTIONS), isNumber(options) ? { wait: options } : options);
    const wait = config.wait;
    const leading = config.leading;
    const trailing = config.trailing;
    /**
     * 清除定时器
     */
    const clear = () => {
        timer && clearTimeout(timer);
        timer = null;
    };
    /**
     * 执行函数
     */
    const invoke = () => {
        fn.apply(lastThis, lastArgs);
        clear();
    };
    /**
     * 立即执行
     */
    const flush = () => {
        invoke();
        timer = setTimeout(clear, wait);
    };
    /**
     * 防抖
     * @param {unknown} this
     * @param {Array.Any} args
     */
    function debounced(...args) {
        lastThis = this;
        lastArgs = args;
        if (!leading && trailing) {
            clear();
            timer = setTimeout(invoke, wait);
            return void 0;
        }
        if (leading && !trailing) {
            timer === null && invoke();
            clear();
            timer = setTimeout(clear, wait);
            return void 0;
        }
        if (leading && trailing) {
            timer === null && invoke();
            clear();
            timer = setTimeout(invoke, wait);
            return void 0;
        }
    }
    /** 立即执行 */
    debounced.flush = flush;
    /** 清除定时器 */
    debounced.cancel = clear;
    return debounced;
};
