import { useCallback, useEffect, useRef, useState } from "react";
/**
 * 当前组件挂载状态
 */
export const useIsMounted = () => {
    const isMounted = useRef(false);
    useEffect(() => {
        isMounted.current = true;
        return () => {
            isMounted.current = false;
        };
    }, []);
    return {
        mounted: isMounted,
        isMounted: () => isMounted.current,
    };
};
/**
 * 安全地使用 useState
 * @param {S} value 状态
 * @param {MutableRefObject<boolean>} mounted 组件挂载状态
 */
export const useMountState = (value, mounted) => {
    const [state, setStateOrigin] = useState(value);
    const setCurrentState = useCallback((next) => {
        if (!mounted.current)
            return void 0;
        setStateOrigin(next);
    }, []);
    return [state, setCurrentState];
};
/**
 * 安全地使用 useState
 * @param {S} value 状态
 */
export const useSafeState = (value) => {
    const [state, setStateOrigin] = useState(value);
    const { mounted } = useIsMounted();
    const setCurrentState = useCallback((next) => {
        if (!mounted.current)
            return void 0;
        setStateOrigin(next);
    }, []);
    return [state, setCurrentState];
};
/**
 * State 与 Ref 的使用与更新
 * @param {S} value 状态
 */
export const useStateRef = (value) => {
    const [state, setStateOrigin] = useState(value);
    const { mounted } = useIsMounted();
    const ref = useRef(state);
    const setState = useCallback((next) => {
        if (!mounted.current)
            return void 0;
        ref.current = next;
        setStateOrigin(next);
    }, []);
    return [state, setState, ref];
};
/**
 * 避免挂载时触发副作用
 * @param {EffectCallback} effect 副作用依赖
 * @param {DependencyList} deps 依赖
 */
export const useUpdateEffect = (effect, deps) => {
    const isMounted = useRef(false);
    useEffect(() => {
        if (!isMounted.current) {
            isMounted.current = true;
        }
        else {
            return effect();
        }
    }, deps);
};
/**
 * 保证 re-render 时的同一函数引用
 * @param {Func.Any} fn 方法
 */
export const useMemoFn = (fn) => {
    const fnRef = useRef(fn);
    const memoFn = useRef();
    fnRef.current = fn;
    if (!memoFn.current) {
        memoFn.current = function (...args) {
            return fnRef.current.apply(this, args);
        };
    }
    return memoFn.current;
};
/**
 * 强制更新组件
 */
export const useForceUpdate = () => {
    const [index, setState] = useState(0);
    const update = useCallback(() => setState(prev => prev + 1), []);
    return { index, update };
};
/**
 * 判断首次渲染
 */
export const useIsFirstRender = () => {
    const isFirst = useRef(true);
    useEffect(() => {
        isFirst.current = false;
    }, []);
    return {
        firstRender: isFirst,
        isFirstRender: () => isFirst.current,
    };
};
