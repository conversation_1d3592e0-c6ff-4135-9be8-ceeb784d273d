import { isNumber, isString } from "./is";
export class DateTime extends Date {
    constructor(p1, p2, p3, p4, p5, p6, p7) {
        if (p1 === void 0) {
            // 无参构建
            super();
        }
        else if (p1 instanceof Date || (isNumber(p1) && p2 === void 0)) {
            // 第一个参数为 Date 或者 Number 且无第二个参数
            super(p1);
        }
        else if (isNumber(p1) && isNumber(p2)) {
            // 第一和第二个参数都为 Number
            super(p1, p2, p3 || 1, p4 || 0, p5 || 0, p6 || 0, p7 || 0);
        }
        else if (isString(p1)) {
            // 第一个参数为String
            // 安全地使用 Date 对象 兼容 IOS
            super(p1.replace(/-/g, "/"));
        }
        else {
            throw new Error("No suitable parameters");
        }
    }
    /**
     * 格式化时间日期
     * @param fmt string? @default: yyyy-MM-dd
     * @desc yyyy 年 MM 月 dd 日 hh 小时 mm 分 ss 秒 S 毫秒
     */
    format(fmt = "yyyy-MM-dd") {
        const preset = {
            "M+": this.getMonth() + 1, // 月份
            "d+": this.getDate(), // 日
            "h+": this.getHours(), // 小时
            "m+": this.getMinutes(), // 分
            "s+": this.getSeconds(), // 秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, this.getFullYear().toString().slice(-RegExp.$1.length));
        }
        for (const k in preset) {
            if (new RegExp(`(${k})`).test(fmt)) {
                const val = preset[k].toString();
                fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? val : val.padStart(RegExp.$1.length, "0"));
            }
        }
        return fmt;
    }
    /**
     * 调整时间
     * @param {number} years 年
     * @param {number} months 月
     * @param {number} days 日
     */
    add(years = 0, months = 0, days = 0) {
        if (days)
            this.setDate(this.getDate() + days);
        if (months)
            this.setMonth(this.getMonth() + months);
        if (years)
            this.setFullYear(this.getFullYear() + years);
        return this;
    }
    /**
     * 精确的时间差
     * @param {DateTime} point
     */
    diff(newDate) {
        const diffTime = Math.abs(newDate.getTime() - this.getTime()) / 1000; // 转为秒
        const years = Math.floor(diffTime / 31536000);
        const months = Math.floor(diffTime / 2592000);
        const days = Math.floor(diffTime / 86400);
        const hours = Math.floor(diffTime / 3600) - 24 * days;
        const minutes = Math.floor((diffTime % 3600) / 60);
        const seconds = Math.floor(diffTime % 60);
        return { years, months, days, hours, minutes, seconds };
    }
    /**
     * 延后到第 N 个月的 1 号
     * @param {number} n
     */
    nextMonth(n = 1) {
        this.setMonth(this.getMonth() + n);
        this.setDate(1);
        this.setHours(0, 0, 0, 0);
        return this;
    }
    /**
     * 延后到第 N 天的 0 点
     * @param {number} n
     */
    nextDay(n = 1) {
        this.setDate(this.getDate() + n);
        this.setHours(0, 0, 0, 0);
        return this;
    }
    /**
     * 延后到第 N 小时的 0 分钟
     * @param {number} n
     */
    nextHour(n = 1) {
        this.setHours(this.getHours() + n);
        this.setMinutes(0, 0, 0);
        return this;
    }
    /**
     * 延后到第 N 分钟的 0 秒
     * @param {number} n
     */
    nextMinute(n = 1) {
        this.setMinutes(this.getMinutes() + n);
        this.setSeconds(0, 0);
        return this;
    }
    /**
     * 延后 N 个月
     * @param {number} n
     */
    deferMonth(n = 1) {
        this.setMonth(this.getMonth() + n);
        return this;
    }
    /**
     * 延后 N 天
     * @param {number} n
     */
    deferDay(n = 1) {
        this.setDate(this.getDate() + n);
        return this;
    }
    /**
     * 延后 N 小时
     * @param {number} n
     */
    deferHour(n = 1) {
        this.setHours(this.getHours() + n);
        return this;
    }
    /**
     * 延后 N 分钟
     * @param {number} n
     */
    deferMinute(n = 1) {
        this.setMinutes(this.getMinutes() + n);
        return this;
    }
    /**
     * 克隆当前时间日期
     * @returns {DateTime}
     */
    clone() {
        return new DateTime(this.getTime());
    }
    // 转换为 DateTime
    static from(date) {
        return new DateTime(date);
    }
}
