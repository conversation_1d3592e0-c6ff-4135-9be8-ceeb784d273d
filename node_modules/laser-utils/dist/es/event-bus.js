import { DEFAULT_PRIORITY } from "./constant";
export class EventBus {
    constructor() {
        this.listeners = {};
    }
    /**
     * 监听事件
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     */
    on(key, listener, priority = DEFAULT_PRIORITY) {
        this.addEventListener(key, listener, priority, false);
    }
    /**
     * 一次性事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     */
    once(key, listener, priority = DEFAULT_PRIORITY) {
        this.addEventListener(key, listener, priority, true);
    }
    /**
     * 添加事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     * @param {number} priority
     * @param {boolean} once
     */
    addEventListener(key, listener, priority, once) {
        const handler = this.listeners[key] || [];
        !handler.some(item => item.listener === listener) && handler.push({ listener, priority, once });
        handler.sort((a, b) => a.priority - b.priority);
        this.listeners[key] = handler;
    }
    /**
     * 移除事件监听
     * @param {T} key
     * @param {Listener<T>} listener
     */
    off(key, listener) {
        const handler = this.listeners[key];
        if (!handler)
            return void 0;
        // COMPAT: 不能直接`splice` 可能会导致`trigger`时打断`forEach`
        const next = handler.filter(item => item.listener !== listener);
        this.listeners[key] = next;
    }
    /**
     * 触发事件
     * @param {T} key
     * @param {Listener<T>} listener
     * @returns {boolean} prevented
     */
    emit(key, payload) {
        const handler = this.listeners[key];
        if (!handler)
            return false;
        const context = {
            key: key,
            stopped: false,
            prevented: false,
            stop: () => {
                context.stopped = true;
            },
            prevent: () => {
                context.prevented = true;
            },
        };
        for (const item of handler) {
            item.listener(payload, context);
            item.once && this.off(key, item.listener);
            if (context.stopped)
                break;
        }
        return context.prevented;
    }
    /**
     * 清理事件
     */
    clear() {
        this.listeners = {};
    }
}
