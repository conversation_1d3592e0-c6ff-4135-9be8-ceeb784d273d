export class RegExec {
    /**
     * 获取正则匹配的第 index 个结果
     * @param {RegExp} regex 正则表达式
     * @param {string} str 字符串
     * @param {number} index 匹配的索引
     * @returns {string}
     */
    static exec(regex, str, index = 1) {
        const res = regex.exec(str);
        if (!res)
            return "";
        return res[index] !== void 0 ? res[index] : res[0];
    }
    /**
     * 获取正则匹配的所有结果
     * @param {RegExp} regex 正则表达式
     * @param {string} str 字符串
     * @param {number} index 匹配的索引
     * @returns {string[]}
     */
    static match(regex, str, index = 1) {
        const result = [];
        const flags = `${regex.flags}${regex.global ? "" : "g"}`;
        const next = new RegExp(regex, flags);
        let temp = null;
        while ((temp = next.exec(str))) {
            const item = temp && temp[index] !== void 0 ? temp[index] : temp[0];
            result.push(item || "");
        }
        return result;
    }
    /**
     * 读数组索引值
     * @param {string[] | null} from
     * @param {number} index
     * @returns {string}
     */
    static get(from, index) {
        return from ? from[index] || "" : "";
    }
}
