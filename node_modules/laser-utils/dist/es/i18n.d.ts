import { Object } from "./types";
export declare const I18N_SYMBOL = "__I18N_SYMBOL__";
export declare const hasOwnProperty: (v: PropertyKey) => boolean;
export type I18nPreset = Object.Nested;
export type I18nTypes<T extends I18nPreset> = Record<Object.Flatten<T>, string>;
export declare class I18n<T extends I18nPreset> {
    private _language;
    private _config;
    private _cache;
    private _override;
    constructor(language: string);
    /**
     * Set language
     * @param {string} language
     * @returns {void}
     */
    setLanguage(language: string): void;
    /**
     * Load i18n config
     * @param {T} config
     * @returns {void}
     */
    load(language: string, config: T): void;
    /**
     * Get current language
     * @returns {string}
     */
    getLanguage(): string;
    /**
     * Check if language is loaded
     * @param {string} language
     * @returns {boolean}
     */
    isLoaded(language: string): boolean;
    /**
     * Get language payload
     * @param {string} language
     * @returns {T | null}
     */
    getPayload(language?: string): T | null;
    /**
     * Get locale
     * @returns {T | null}
     */
    get locale(): T | null;
    /**
     * Set override
     * @param {I18nTypes<T>} override
     * @returns {void}
     */
    override(override: Partial<I18nTypes<T>>): void;
    /**
     * Format i18n value
     * @param {string} value
     * @param {Record<string, string>} variables
     * @returns {string}
     */
    private format;
    /**
     * Compose i18n value
     * @param {keyof I18nTypes<T>} key
     * @param {T} config
     * @returns {string | null}
     */
    private getValue;
    /**
     * Translate i18n value
     * @param {keyof I18nTypes<T>} key
     * @param {Record<string, string>} variable
     * @returns {string}
     */
    t(key: keyof I18nTypes<T>, variable?: Record<string, string>, defaultValue?: string): string;
    /**
     * Seal I18n Entity
     * @param {keyof I18nTypes<T>} key
     * @param {Record<string, string>} variable
     * @param {string} defaultValue
     * @returns {string}
     */
    seal(key: keyof I18nTypes<T>, variable?: Record<string, string>, defaultValue?: string): string;
    /**
     * Proxy I18n Entity
     * @param {R} target
     * @returns {R}
     */
    proxy<R extends Object.Mixed>(target: R): R;
}
