export type Invoke<T> = () => Promise<T>;
export type Task<T> = {
    fn: Invoke<T>;
    resolve: (v: T) => void;
};
export declare class Scheduler<T = void> {
    private maxTask;
    private running;
    private pending;
    constructor(maxTask: number);
    /**
     * 提交任务
     * @param {Invoke<T>} fn
     * @returns {Promise<T>}
     */
    commit(fn: Invoke<T>): Promise<T>;
    /**
     * 触发任务
     * @param {Task<T>} task
     * @returns {void}
     */
    dispatch(task: Task<T>): void;
}
