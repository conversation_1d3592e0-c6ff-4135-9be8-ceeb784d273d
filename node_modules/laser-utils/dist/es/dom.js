const global = typeof globalThis !== "undefined" ? globalThis : window;
const DOMNode = global.Node;
/**
 * 检查 DOM 节点
 * @param {unknown} value
 * @returns {boolean}
 */
export const isDOMNode = (value) => {
    return value instanceof Node;
};
/**
 * 检查 DOM 文本节点
 * @param {unknown} value
 * @returns {boolean}
 */
export const isDOMText = (value) => {
    return isDOMNode(value) && value.nodeType === DOMNode.TEXT_NODE;
};
/**
 * 检查 DOM 元素节点
 * @param {unknown} value
 * @returns {boolean}
 */
export const isDOMElement = (value) => {
    return isDOMNode(value) && value.nodeType === DOMNode.ELEMENT_NODE;
};
/**
 * 检查 DOM 注释节点
 * @param {unknown} value
 * @returns {boolean}
 */
export const isCommentNode = (value) => {
    return isDOMNode(value) && value.nodeType === DOMNode.COMMENT_NODE;
};
/**
 * 检查 DOM 文档节点
 * @param {unknown} value
 * @returns {boolean}
 */
export const isHTMLElement = (value) => {
    return isDOMNode(value) && value instanceof HTMLElement;
};
/**
 * 获取焦点元素
 * @returns {DOMElementType | null}
 */
export const getActiveElement = () => {
    let activeElement = document.activeElement;
    while (activeElement && activeElement.shadowRoot && activeElement.shadowRoot.activeElement) {
        activeElement = activeElement.shadowRoot.activeElement;
    }
    return activeElement;
};
