import type { DependencyList, EffectCallback, MutableRefObject, SetStateAction } from "react";
import type { Func } from "./types";
/**
 * 当前组件挂载状态
 */
export declare const useIsMounted: () => {
    mounted: MutableRefObject<boolean>;
    isMounted: () => boolean;
};
/**
 * 安全地使用 useState
 * @param {S} value 状态
 * @param {MutableRefObject<boolean>} mounted 组件挂载状态
 */
export declare const useMountState: <S = undefined>(value: S, mounted: MutableRefObject<boolean>) => readonly [S, (next: SetStateAction<S>) => undefined];
/**
 * 安全地使用 useState
 * @param {S} value 状态
 */
export declare const useSafeState: <S = undefined>(value: S) => readonly [S, (next: SetStateAction<S>) => undefined];
/**
 * State 与 Ref 的使用与更新
 * @param {S} value 状态
 */
export declare const useStateRef: <S = undefined>(value: S) => readonly [S, (next: S) => undefined, MutableRefObject<S>];
/**
 * 避免挂载时触发副作用
 * @param {EffectCallback} effect 副作用依赖
 * @param {DependencyList} deps 依赖
 */
export declare const useUpdateEffect: (effect: EffectCallback, deps?: DependencyList) => void;
/**
 * 保证 re-render 时的同一函数引用
 * @param {Func.Any} fn 方法
 */
export declare const useMemoFn: <T extends Func.Any>(fn: T) => T;
/**
 * 强制更新组件
 */
export declare const useForceUpdate: () => {
    index: number;
    update: () => void;
};
/**
 * 判断首次渲染
 */
export declare const useIsFirstRender: () => {
    firstRender: MutableRefObject<boolean>;
    isFirstRender: () => boolean;
};
