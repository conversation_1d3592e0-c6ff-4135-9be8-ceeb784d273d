import { isString } from "./is";
export const TEXT_PLAIN = "text/plain";
export const TEXT_HTML = "text/html";
export class Clipboard {
    /**
     * 执行复制命令 [兼容性方案]
     * @param {CopyData} data
     */
    static execCopyCommand(data) {
        const textarea = document.createElement("textarea");
        textarea.addEventListener("copy", event => {
            for (const [key, value] of Object.entries(data)) {
                event.clipboardData && event.clipboardData.setData(key, value);
            }
            event.stopPropagation();
            event.preventDefault();
        }, true);
        textarea.style.position = "fixed";
        textarea.style.left = "-999px";
        textarea.style.top = "-999px";
        textarea.value = data[TEXT_PLAIN];
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand("copy");
        document.body.removeChild(textarea);
    }
    /**
     * 写入剪贴板
     * @param {CopyData | string} data
     */
    static write(data) {
        const params = isString(data) ? { [TEXT_PLAIN]: data } : data;
        const plainText = params[TEXT_PLAIN];
        if (!plainText)
            return false;
        if (navigator.clipboard && window.ClipboardItem) {
            const dataItems = {};
            for (const [key, value] of Object.entries(params)) {
                const blob = new Blob([value], { type: key });
                dataItems[key] = blob;
            }
            navigator.clipboard.write([new ClipboardItem(dataItems)]).catch(() => {
                Clipboard.execCopyCommand(params);
            });
        }
        else {
            Clipboard.execCopyCommand(params);
        }
        return true;
    }
}
/** 文本 */
Clipboard.TEXT_PLAIN = TEXT_PLAIN;
/** HTML */
Clipboard.TEXT_HTML = TEXT_HTML;
