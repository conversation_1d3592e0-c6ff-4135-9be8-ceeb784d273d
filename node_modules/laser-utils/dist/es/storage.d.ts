export declare const Storage: {
    /**
     * 设置 key 前缀
     * @param {string} key
     * @returns {void}
     */
    setPrefix: (key: string) => void;
    /**
     * 设置 key 后缀
     * @param {string} key
     * @returns {void}
     */
    setSuffix: (key: string) => void;
    /** local storage */
    local: {
        /**
         * 读取存储数据
         * @param {string} name
         * @returns {null | T}
         */
        get: <T = unknown>(name: string) => T | null;
        /**
         * 设置存储数据
         * @param {string} name
         * @param {T} data
         * @param {number} ttl ms
         * @returns {void}
         */
        set: <T_1 = string>(name: string, data: T_1, ttl?: number | null) => void;
        /**
         * 读取存储数据 原始值
         * @param {string} name
         * @returns {string | null}
         */
        getOrigin: (name: string) => string | null;
        /**
         * 移除存储数据
         * @param {string} name
         * @returns {void}
         */
        remove: (name: string) => void;
    };
    /** session storage */
    session: {
        /**
         * 读取存储数据
         * @param {string} name
         * @returns {null | T}
         */
        get: <T_2 = unknown>(name: string) => T_2 | null;
        /**
         * 设置存储数据
         * @param {string} name
         * @param {T} data
         * @param {number} ttl ms
         * @returns {void}
         */
        set: <T_3 = string>(name: string, data: T_3, ttl?: number | null) => void;
        /**
         * 读取存储数据 原始值
         * @param {string} name
         * @returns {string | null}
         */
        getOrigin: (name: string) => string | null;
        /**
         * 移除存储数据
         * @param {string} name
         * @returns {void}
         */
        remove: (name: string) => void;
    };
};
