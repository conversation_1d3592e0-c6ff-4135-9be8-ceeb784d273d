export declare class RegExec {
    /**
     * 获取正则匹配的第 index 个结果
     * @param {RegExp} regex 正则表达式
     * @param {string} str 字符串
     * @param {number} index 匹配的索引
     * @returns {string}
     */
    static exec(regex: RegExp, str: string, index?: number): string;
    /**
     * 获取正则匹配的所有结果
     * @param {RegExp} regex 正则表达式
     * @param {string} str 字符串
     * @param {number} index 匹配的索引
     * @returns {string[]}
     */
    static match(regex: RegExp, str: string, index?: number): string[];
    /**
     * 读数组索引值
     * @param {string[] | null} from
     * @param {number} index
     * @returns {string}
     */
    static get(from: string[] | null, index: number): string;
}
