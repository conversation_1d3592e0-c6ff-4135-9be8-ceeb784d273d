{"name": "laser-utils", "version": "0.0.5-alpha.10", "description": "", "main": "./dist/lib/index.js", "types": "./dist/es/index.d.ts", "module": "./dist/es/index.js", "sideEffects": false, "files": ["dist"], "scripts": {"build:es": "tsc -p tsconfig.build.json", "build:lib": "tsc -p tsconfig.lib.json", "build": "npm run build:es && npm run build:lib", "release": "./publish.sh", "test": "jest", "lint:ts": "tsc -p tsconfig.build.json --noEmit"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {}, "devDependencies": {"@babel/core": "7.20.12", "@babel/preset-env": "7.20.2", "@babel/preset-typescript": "7.18.6", "@types/jest": "29.4.0", "@types/react": "17.0.2", "babel-jest": "29.4.1", "jest": "29.4.1", "ts-jest": "29.1.2"}}