{"name": "embed-drawio", "version": "0.1.10", "files": ["dist", "pnpm-lock.yaml"], "main": "dist/lib/index.js", "module": "dist/es/index.js", "types": "dist/es/index.d.ts", "repository": {"type": "git", "url": "https://github.com/WindrunnerMax/FlowChartEditor"}, "scripts": {"dev": "react-app-rewired start", "build": "npm run build:dist && react-app-rewired build", "build:es": "tsc -p tsconfig.build.json", "build:lib": "tsc -p tsconfig.lib.json", "build:css": "gulp -f gulpfile.js", "build:dist": "rm -rf ./dist && npm run build:es && npm run build:lib && npm run build:css", "lint:es": "eslint --fix --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore .", "lint:style": "stylelint \"**/*.{css,scss,sass}\" --fix --ignore-path .gitignore", "lint": "npm run lint-es && npm run lint-style", "lint:circular": "madge --extensions js,jsx --circular ./dist"}, "dependencies": {"js-base64": "3.7.7", "laser-utils": "0.0.5-alpha.10", "mxgraph": "4.2.2", "pako": "1.0.6", "sanitize-html": "2.7.3"}, "devDependencies": {"@typed-mxgraph/typed-mxgraph": "1.0.7", "@types/node": "12.0.0", "@types/react": "17.0.2", "@types/react-dom": "17.0.2", "@typescript-eslint/eslint-plugin": "6.12.0", "@typescript-eslint/parser": "6.12.0", "customize-cra": "1.0.0", "eslint": "7.11.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-react": "7.27.0", "eslint-plugin-react-hooks": "4.3.0", "gulp": "5.0.0", "gulp-clean-css": "4.3.0", "gulp-concat": "2.6.1", "jsdom": "25.0.1", "prettier": "2.4.1", "react": "17.0.2", "react-app-rewired": "2.1.8", "react-dom": "17.0.2", "stylelint": "14.8.5", "stylelint-config-standard": "25.0.0", "typescript": "5.3.2"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix"], "*.{css,scss,less}": ["stylelint --fix"]}, "browser": {"fs": false, "path": false}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}