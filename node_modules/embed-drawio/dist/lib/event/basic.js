"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditorEvent = void 0;
var EditorEvent = /** @class */ (function () {
    function EditorEvent() {
        var _this = this;
        this.postMessage = function (message) {
            _this.iframe &&
                _this.iframe.contentWindow &&
                _this.iframe.contentWindow.postMessage(JSON.stringify(message), _this.url);
        };
        this.handleMessageEvent = function (event) {
            if (_this.iframe && event.source === _this.iframe.contentWindow && event.data) {
                try {
                    var msg = JSON.parse(event.data);
                    // console.log("msg", msg);
                    _this.handleMessage(msg);
                }
                catch (error) {
                    console.log("MessageEvent Error", error);
                }
            }
        };
        this.handleMessage = function (msg) {
            switch (msg.event) {
                case "init":
                    return _this.onInit(msg);
                case "load":
                    return _this.onLoad(msg);
                case "configure":
                    return _this.onConfig(msg);
                case "autosave":
                    return _this.onAutoSave(msg);
                case "save":
                    return _this.onSave(msg);
                case "export":
                    return _this.onExport(msg);
                case "exit":
                    return _this.onExit(msg);
            }
        };
    }
    return EditorEvent;
}());
exports.EditorEvent = EditorEvent;
