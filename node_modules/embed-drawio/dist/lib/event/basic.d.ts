import type { ExportMsg, InitMsg, SaveMsg } from "./interface";
export declare abstract class EditorEvent {
    protected abstract url: string;
    protected abstract iframe: HTMLIFrameElement | null;
    abstract onConfig(msg: InitMsg): void;
    abstract onInit(msg: InitMsg): void;
    abstract onLoad(msg: InitMsg): void;
    abstract onAutoSave(msg: SaveMsg): void;
    abstract onSave(msg: SaveMsg): void;
    abstract onExit(msg: SaveMsg): void;
    abstract onExport(msg: ExportMsg): void;
    protected postMessage: (message: unknown) => void;
    protected handleMessageEvent: (event: MessageEvent) => void;
    private handleMessage;
}
