"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditorBus = void 0;
var basic_1 = require("./basic");
var interface_1 = require("./interface");
var EditorBus = /** @class */ (function (_super) {
    __extends(EditorBus, _super);
    function EditorBus(config) {
        if (config === void 0) { config = { format: "xml" }; }
        var _this = _super.call(this) || this;
        _this.startEdit = function () {
            if (_this.lock || !_this.iframe)
                return void 0;
            _this.lock = true;
            var iframe = _this.iframe;
            var url = "".concat(_this.url, "?") +
                [
                    "embed=1",
                    "spin=1",
                    "proto=json",
                    "configure=1",
                    "noSaveBtn=1",
                    "stealth=1",
                    "libraries=0",
                ].join("&");
            iframe.setAttribute("src", url);
            iframe.setAttribute("frameborder", "0");
            iframe.setAttribute("style", "position:fixed;top:0;left:0;width:100%;height:100%;background-color:#fff;z-index:999999;");
            iframe.className = "drawio-iframe-container";
            document.body.style.overflow = "hidden";
            document.body.appendChild(iframe);
            window.addEventListener(interface_1.MESSAGE_EVENT, _this.handleMessageEvent);
        };
        _this.exitEdit = function () {
            _this.lock = false;
            _this.iframe && document.body.removeChild(_this.iframe);
            _this.iframe = null;
            document.body.style.overflow = "";
            window.removeEventListener(interface_1.MESSAGE_EVENT, _this.handleMessageEvent);
        };
        _this.lock = false;
        _this.config = config;
        _this.url = config.url || interface_1.DEFAULT_URL;
        _this.iframe = document.createElement("iframe");
        return _this;
    }
    EditorBus.prototype.onConfig = function () {
        var _a;
        this.config.onConfig
            ? this.config.onConfig()
            : this.postMessage({
                action: "configure",
                config: {
                    compressXml: (_a = this.config.compress) !== null && _a !== void 0 ? _a : false,
                    css: ".geTabContainer{display:none !important;}",
                },
            });
    };
    EditorBus.prototype.onInit = function () {
        this.config.onInit
            ? this.config.onInit()
            : this.postMessage({
                action: "load",
                autosave: 1,
                saveAndExit: "1",
                modified: "unsavedChanges",
                xml: this.config.data,
                title: this.config.title || "流程图",
            });
    };
    EditorBus.prototype.onLoad = function () {
        this.config.onLoad && this.config.onLoad();
    };
    EditorBus.prototype.onAutoSave = function (msg) {
        this.config.onAutoSave && this.config.onAutoSave(msg.xml);
    };
    EditorBus.prototype.onSave = function (msg) {
        this.config.onSave && this.config.onSave(msg.xml);
        if (this.config.onExport) {
            this.postMessage({
                action: "export",
                format: this.config.format,
                xml: msg.xml,
            });
        }
        else {
            if (msg.exit)
                this.exitEdit();
        }
    };
    EditorBus.prototype.onExit = function (msg) {
        this.config.onExit && this.config.onExit(msg.xml);
        this.exitEdit();
    };
    EditorBus.prototype.onExport = function (msg) {
        if (!this.config.onExport)
            return void 0;
        this.config.onExport(msg.data, this.config.format);
        this.exitEdit();
    };
    return EditorBus;
}(basic_1.EditorEvent));
exports.EditorBus = EditorBus;
// https://github.com/jgraph/drawio-integration
// https://github.com/jgraph/drawio-tools
// https://www.diagrams.net/doc/faq/supported-url-parameters
// https://www.diagrams.net/doc/faq/configure-diagram-editor
// https://desk.draw.io/support/solutions/articles/16000042544
// https://github.com/jgraph/mxgraph-js aa11697fbd5ba9f4bb
