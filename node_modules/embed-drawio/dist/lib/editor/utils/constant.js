"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_COLORS = exports.PRESENT_COLORS = exports.DIALOG = exports.EDITOR_UI = exports.EDITOR = exports.GRAPH = void 0;
var mxgraph_1 = require("../../core/mxgraph");
exports.GRAPH = {
    ARROW_FILL: "#29b6f2",
    ARROW_SPACING: 2,
};
exports.EDITOR = {
    HINT_OFFSET: 20,
    CTRL_KEY: mxgraph_1.mxClient.IS_MAC ? "Cmd" : "Ctrl",
};
exports.EDITOR_UI = {
    COMPAT_UI: true,
};
exports.DIALOG = {
    BACK_DROP_COLOR: "white",
};
exports.PRESENT_COLORS = [
    "E6D0DE",
    "CDA2BE",
    "B5739D",
    "E1D5E7",
    "C3ABD0",
    "A680B8",
    "D4E1F5",
    "A9C4EB",
    "7EA6E0",
    "D5E8D4",
    "9AC7BF",
    "67AB9F",
    "D5E8D4",
    "B9E0A5",
    "97D077",
    "FFF2CC",
    "FFE599",
    "FFD966",
    "FFF4C3",
    "FFCE9F",
    "FFB570",
    "F8CECC",
    "F19C99",
    "EA6B66",
];
exports.DEFAULT_COLORS = [
    "FFFFFF",
    "E6E6E6",
    "CCCCCC",
    "B3B3B3",
    "999999",
    "808080",
    "666666",
    "4D4D4D",
    "333333",
    "1A1A1A",
    "000000",
    "FFCCCC",
    "FFE6CC",
    "FFFFCC",
    "E6FFCC",
    "CCFFCC",
    "CCFFE6",
    "CCFFFF",
    "CCE5FF",
    "CCCCFF",
    "E5CCFF",
    "FFCCFF",
    "FFCCE6",
    "FF9999",
    "FFCC99",
    "FFFF99",
    "CCFF99",
    "99FF99",
    "99FFCC",
    "99FFFF",
    "99CCFF",
    "9999FF",
    "CC99FF",
    "FF99FF",
    "FF99CC",
    "FF6666",
    "FFB366",
    "FFFF66",
    "B3FF66",
    "66FF66",
    "66FFB3",
    "66FFFF",
    "66B2FF",
    "6666FF",
    "B266FF",
    "FF66FF",
    "FF66B3",
    "FF3333",
    "FF9933",
    "FFFF33",
    "99FF33",
    "33FF33",
    "33FF99",
    "33FFFF",
    "3399FF",
    "3333FF",
    "9933FF",
    "FF33FF",
    "FF3399",
    "FF0000",
    "FF8000",
    "FFFF00",
    "80FF00",
    "00FF00",
    "00FF80",
    "00FFFF",
    "007FFF",
    "0000FF",
    "7F00FF",
    "FF00FF",
    "FF0080",
    "CC0000",
    "CC6600",
    "CCCC00",
    "66CC00",
    "00CC00",
    "00CC66",
    "00CCCC",
    "0066CC",
    "0000CC",
    "6600CC",
    "CC00CC",
    "CC0066",
    "990000",
    "994C00",
    "999900",
    "4D9900",
    "009900",
    "00994D",
    "009999",
    "004C99",
    "000099",
    "4C0099",
    "990099",
    "99004D",
    "660000",
    "663300",
    "666600",
    "336600",
    "006600",
    "006633",
    "006666",
    "003366",
    "000066",
    "330066",
    "660066",
    "660033",
    "330000",
    "331A00",
    "333300",
    "1A3300",
    "003300",
    "00331A",
    "003333",
    "001933",
    "000033",
    "190033",
    "330033",
    "33001A",
];
