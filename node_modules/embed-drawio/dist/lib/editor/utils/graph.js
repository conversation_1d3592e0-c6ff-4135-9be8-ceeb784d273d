"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROUND_DROP_IMAGE = exports.REFRESH_TARGET_IMAGE = exports.TRIANGLE_LEFT_IMAGE = exports.TRIANGLE_DOWN_IMAGE = exports.TRIANGLE_RIGHT_IMAGE = exports.TRIANGLE_UP_IMAGE = exports.createSvgImage = void 0;
var mxgraph_1 = require("../../core/mxgraph");
var base64_1 = require("../images/base64");
var constant_1 = require("./constant");
var js_base64_1 = require("js-base64");
var createSvgImage = function (w, h, data, coordWidth, coordHeight) {
    var viewBox = coordWidth && coordHeight ? "viewBox=\"0 0 ".concat(coordWidth, " ").concat(coordHeight, "\"") : "";
    var tmp = unescape(encodeURIComponent("".concat('<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">' +
        '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="').concat(w, "px\" height=\"").concat(h, "px\" ").concat(viewBox, " version=\"1.1\">").concat(data, "</svg>")));
    return new mxgraph_1.mxImage("data:image/svg+xml;base64,".concat(js_base64_1.Base64.encode(tmp)), w, h);
};
exports.createSvgImage = createSvgImage;
exports.TRIANGLE_UP_IMAGE = (0, exports.createSvgImage)(18, 28, "<path d=\"m 6 26 L 12 26 L 12 12 L 18 12 L 9 1 L 1 12 L 6 12 z\" stroke=\"#fff\" fill=\"".concat(constant_1.GRAPH.ARROW_FILL, "\"/>"));
exports.TRIANGLE_RIGHT_IMAGE = (0, exports.createSvgImage)(26, 18, "<path d=\"m 1 6 L 14 6 L 14 1 L 26 9 L 14 18 L 14 12 L 1 12 z\" stroke=\"#fff\" fill=\"".concat(constant_1.GRAPH.ARROW_FILL, "\"/>"));
exports.TRIANGLE_DOWN_IMAGE = (0, exports.createSvgImage)(18, 26, "<path d=\"m 6 1 L 6 14 L 1 14 L 9 26 L 18 14 L 12 14 L 12 1 z\" stroke=\"#fff\" fill=\"".concat(constant_1.GRAPH.ARROW_FILL, "\"/>"));
exports.TRIANGLE_LEFT_IMAGE = (0, exports.createSvgImage)(28, 18, "<path d=\"m 1 9 L 12 1 L 12 6 L 26 6 L 26 12 L 12 12 L 12 18 z\" stroke=\"#fff\" fill=\"".concat(constant_1.GRAPH.ARROW_FILL, "\"/>"));
exports.REFRESH_TARGET_IMAGE = new mxgraph_1.mxImage(base64_1.refreshTarget, 38, 38);
exports.ROUND_DROP_IMAGE = (0, exports.createSvgImage)(26, 26, "<circle cx=\"13\" cy=\"13\" r=\"12\" stroke=\"#fff\" fill=\"".concat(constant_1.GRAPH.ARROW_FILL, "\"/>"));
