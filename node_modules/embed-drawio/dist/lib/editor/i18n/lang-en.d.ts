export declare const langEN = "alreadyConnected=Nodes already connected\ncancel=Cancel\nclose=Close\ncollapse-expand=Collapse/Expand\ncontainsValidationErrors=Contains validation errors\ndone=Done\ndoubleClickOrientation=Doubleclick to Change Orientation\nerror=Error\nerrorSavingFile=Error saving file\nok=OK\nupdatingDocument=Updating Document. Please wait...\nupdatingSelection=Updating Selection. Please wait...\n# Custom resources\nabout=About\nactualSize=Actual Size\nadd=Add\naddLayer=Add Layer\naddProperty=Add Property\naddToExistingDrawing=Add to Existing Drawing\naddWaypoint=Add Waypoint\nadvanced=Advanced\nalign=Align\nalignment=Alignment\nallChangesLost=All changes will be lost!\nangle=Angle\napply=Apply\narc=Arc\narrange=Arrange\narrow=Arrow\narrows=Arrows\nautomatic=Automatic\nautosave=Autosave\nautosize=Autosize\nback=Back\nbackground=Background\nbackgroundColor=Background Color\nbackgroundImage=Background Image\nbasic=Basic\nblock=Block\nblockquote=Blockquote\nbold=Bold\nborder=Border\nborderWidth=Borderwidth\nborderColor=Border Color\nbottom=Bottom\nbottomAlign=Bottom Align\nbottomLeft=Bottom Left\nbottomRight=Bottom Right\nbulletedList=Bulleted List\ncannotOpenFile=Cannot open file\ncenter=Center\nchange=Change\nchangeOrientation=Change Orientation\ncircle=Circle\nclassic=Classic\nclearDefaultStyle=Clear Default Style\nclearWaypoints=Clear Waypoints\nclipart=Clipart\ncollapse=Collapse\ncollapseExpand=Collapse/Expand\ncollapsible=Collapsible\ncomic=Comic\nconnect=Connect\nconnection=Connection\nconnectionPoints=Connection points\nconnectionArrows=Connection arrows\nconstrainProportions=Constrain Proportions\ncopy=Copy\ncopyConnect=Copy on Connect\ncopySize=Copy Size\ncreate=Create\ncurved=Curved\ncustom=Custom\ncut=Cut\ndashed=Dashed\ndecreaseIndent=Decrease Indent\ndefault=Default\ndelete=Delete\ndeleteColumn=Delete Column\ndeleteRow=Delete Row\ndiagram=Diagram\ndiamond=Diamond\ndiamondThin=Diamond (thin)\ndirection=Direction\ndistribute=Distribute\ndivider=Divider\ndocumentProperties=Document Properties\ndotted=Dotted\ndpi=DPI\ndrawing=Drawing{1}\ndrawingEmpty=Drawing is empty\ndrawingTooLarge=Drawing is too large\nduplicate=Duplicate\nduplicateIt=Duplicate {1}\neast=East\nedit=Edit\neditData=Edit Data\neditDiagram=Edit Diagram\neditImage=Edit Image\neditLink=Edit Link\neditStyle=Edit Style\neditTooltip=Edit Tooltip\nenterGroup=Enter Group\nenterValue=Enter Value\nenterName=Enter Name\nenterPropertyName=Enter Property Name\nentityRelation=Entity Relation\nexitGroup=Exit Group\nexpand=Expand\nexport=Export\nextras=Extras\nfile=File\nfileNotFound=File not found\nfilename=Filename\nfill=Fill\nfillColor=Fill Color\nfitPage=One Page\nfitPageWidth=Page Width\nfitTwoPages=Two Pages\nfitWindow=Fit Window\nflip=Flip\nflipH=Flip Horizontal\nflipV=Flip Vertical\nfont=Font\nfontFamily=Font Family\nfontColor=Font Color\nfontSize=Font Size\nformat=Format\nformatPanel=Format Panel\ngeneral=Allgemein\nformatPdf=PDF\nformatPng=PNG\nformatGif=GIF\nformatJpg=JPEG\nformatSvg=SVG\nformatXml=XML\nformatted=Formatted\nformattedText=Formatted Text\ngap=Gap\nglass=Glass\ngeneral=General\nglobal=Global\ngradient=Gradient\ngradientColor=Color\ngrid=Grid\ngridSize=Grid Size\ngroup=Group\nguides=Guides\nheading=Heading\nheight=Height\nhelp=Help\nhide=Hide\nhideIt=Hide {1}\nhidden=Hidden\nhome=Home\nhorizontal=Horizontal\nhorizontalFlow=Horizontal Flow\nhorizontalTree=Horizontal Tree\nhtml=HTML\nid=ID\nimage=Image\nimages=Images\nimport=Import\nincreaseIndent=Increase Indent\ninsert=Insert\ninsertColumnBefore=Insert Column Left\ninsertColumnAfter=Insert Column Right\ninsertHorizontalRule=Insert Horizontal Rule\ninsertImage=Insert Image\ninsertLink=Insert Link\ninsertRowBefore=Insert Row Above\ninsertRowAfter=Insert Row Below\ninvalidInput=Invalid input\ninvalidName=Invalid name\ninvalidOrMissingFile=Invalid or missing file\nisometric=Isometric\nitalic=Italic\nlayers=Layers\nlandscape=Landscape\nlaneColor=Lanecolor\nlayout=Layout\nleft=Left\nleftAlign=Left Align\nleftToRight=Left to Right\nline=Line\nlink=Link\nlineJumps=Line jumps\nlineend=Line End\nlineheight=Line Height\nlinestart=Line Start\nlinewidth=Linewidth\nloading=Loading\nlockUnlock=Lock/Unlock\nmanual=Manual\nmiddle=Middle\nmisc=Misc\nmore=More\nmoreResults=More Results\nmove=Move\nmoveSelectionTo=Move Selection to {1}\nnavigation=Navigation\nnew=New\nnoColor=No Color\nnoFiles=No files\nnoMoreResults=No more results\nnone=None\nnoResultsFor=No results for '{1}'\nnormal=Normal\nnorth=North\nnumberedList=Numbered List\nopacity=Opacity\nopen=Open\nopenArrow=Open Arrow\nopenFile=Open File\nopenLink=Open Link\nopenSupported=Supported format is .XML files saved from this software\nopenInNewWindow=Open in New Window\nopenInThisWindow=Open in this Window\noptions=Options\norganic=Organic\northogonal=Orthogonal\noutline=Outline\noval=Oval\npages=Pages\npageView=Page View\npageScale=Page Scale\npageSetup=Page Setup\npanTooltip=Space+Drag to Scroll\npaperSize=Paper Size\npaste=Paste\npasteHere=Paste Here\npasteSize=Paste Size\npattern=Pattern\nperimeter=Perimeter\nplaceholders=Placeholders\nplusTooltip=Click to connect and clone (ctrl+click to clone, shift+click to connect). Drag to connect (ctrl+drag to clone).\nportrait=Portrait\nposition=Position\nposterPrint=Poster Print\npreview=Preview\nprint=Print\nradialTree=Radial Tree\nredo=Redo\nremoveFormat=Clear Formatting\nremoveFromGroup=Remove from Group\nremoveIt=Remove {1}\nremoveWaypoint=Remove Waypoint\nrename=Rename\nrenameIt=Rename {1}\nreplace=Replace\nreplaceIt={1} already exists. Do you want to replace it?\nreplaceExistingDrawing=Replace existing drawing\nreset=Reset\nresetView=Reset View\nreverse=Reverse\nright=Right\nrightAlign=Right Align\nrightToLeft=Right to Left\nrotate=Rotate\nrotateTooltip=Click and drag to rotate, click to turn shape only by 90 degrees\nrotation=Rotation\nrounded=Rounded\nsave=Save\nsaveAs=Save as\nsaved=Saved\nscrollbars=Scrollbars\nsearch=Search\nsearchShapes=Search Shapes\nselectAll=Select All\nselectEdges=Select Edges\nselectFont=Select a Font\nselectNone=Select None\nselectVertices=Select Vertices\nsetAsDefaultStyle=Set as Default Style\nshadow=Shadow\nshape=Shape\nsharp=Sharp\nsidebarTooltip=Click to expand. Drag and drop shapes into the diagram. Shift+click to change selection. Alt+click to insert and connect.\nsimple=Simple\nsimpleArrow=Simple Arrow\nsize=Size\nsolid=Solid\nsourceSpacing=Source Spacing\nsouth=South\nspacing=Spacing\nstraight=Straight\nstrikethrough=Strikethrough\nstrokeColor=Line Color\nstyle=Style\nsubscript=Subscript\nsuperscript=Superscript\ntable=Table\ntargetSpacing=Target Spacing\ntext=Text\ntextAlignment=Text Alignment\ntextOpacity=Text Opacity\ntoBack=To Back\ntoFront=To Front\ntooltips=Tooltips\ntop=Top\ntopAlign=Top Align\ntopLeft=Top Left\ntopRight=Top Right\ntransparent=Transparent\nturn=Rotate shape only by 90\u00B0\numl=UML\nunderline=Underline\nundo=Undo\nungroup=Ungroup\nurl=URL\nuntitledLayer=Untitled Layer\nvertical=Vertical\nverticalFlow=Vertical Flow\nverticalTree=Vertical Tree\nview=View\nwaypoints=Waypoints\nwest=West\nwidth=Width\nwordWrap=Word Wrap\nwritingDirection=Writing Direction\nzoom=Zoom\nzoomIn=Zoom In\nzoomOut=Zoom Out\nflowchart=Flow Chart\nexit=Exit\n";
