"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Graph = exports.EditorUi = exports.Editor = void 0;
require("./js/Shapes");
var mxgraph_1 = require("../core/mxgraph");
var close_1 = __importDefault(require("./images/close"));
var normalize_1 = __importDefault(require("./images/normalize"));
var maximize_1 = __importDefault(require("./images/maximize"));
var minimize_1 = __importDefault(require("./images/minimize"));
var close_2 = __importDefault(require("./images/close"));
mxgraph_1.mxWindow.prototype.closeImage = close_1.default;
mxgraph_1.mxWindow.prototype.normalizeImage = normalize_1.default;
mxgraph_1.mxWindow.prototype.maximizeImage = maximize_1.default;
mxgraph_1.mxWindow.prototype.minimizeImage = minimize_1.default;
mxgraph_1.mxWindow.prototype.resizeImage = close_2.default;
var Editor_1 = require("./js/Editor");
Object.defineProperty(exports, "Editor", { enumerable: true, get: function () { return Editor_1.Editor; } });
var EditorUi_1 = require("./js/EditorUi");
Object.defineProperty(exports, "EditorUi", { enumerable: true, get: function () { return EditorUi_1.EditorUi; } });
var Graph_1 = require("./js/Graph");
Object.defineProperty(exports, "Graph", { enumerable: true, get: function () { return Graph_1.Graph; } });
// python3 -m http.server 9000
// https://github.com/jgraph/mxgraph-js f43fc06f72ff5153e84ebfab8eed16a7d58a3b68
// https://jgraph.github.io/mxgraph/javascript/examples/grapheditor/www/index.html
