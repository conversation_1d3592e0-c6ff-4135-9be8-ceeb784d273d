declare const _default: "<shapes name=\"mxGraph.arrows\"><shape name=\"Arrow Down\" h=\"97.5\" w=\"70\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><path><move x=\"20\" y=\"0\"/><line x=\"20\" y=\"59\"/><line x=\"0\" y=\"59\"/><line x=\"35\" y=\"97.5\"/><line x=\"70\" y=\"59\"/><line x=\"50\" y=\"59\"/><line x=\"50\" y=\"0\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Arrow Left\" h=\"70\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"97.5\" y=\"20\"/><line x=\"38.5\" y=\"20\"/><line x=\"38.5\" y=\"0\"/><line x=\"0\" y=\"35\"/><line x=\"38.5\" y=\"70\"/><line x=\"38.5\" y=\"50\"/><line x=\"97.5\" y=\"50\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Arrow Right\" h=\"70\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"0\" y=\"20\"/><line x=\"59\" y=\"20\"/><line x=\"59\" y=\"0\"/><line x=\"97.5\" y=\"35\"/><line x=\"59\" y=\"70\"/><line x=\"59\" y=\"50\"/><line x=\"0\" y=\"50\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Arrow Up\" h=\"97.5\" w=\"70\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><path><move x=\"20\" y=\"97.5\"/><line x=\"20\" y=\"38.5\"/><line x=\"0\" y=\"38.5\"/><line x=\"35\" y=\"0\"/><line x=\"70\" y=\"38.5\"/><line x=\"50\" y=\"38.5\"/><line x=\"50\" y=\"97.5\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Bent Left Arrow\" h=\"97\" w=\"97.01\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.85\" y=\"1\" perimeter=\"0\" name=\"S\"/><constraint x=\"0\" y=\"0.29\" perimeter=\"0\" name=\"W\"/></connections><background><path><move x=\"68\" y=\"97\"/><line x=\"68\" y=\"48\"/><arc rx=\"5\" ry=\"5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"63\" y=\"43\"/><line x=\"38\" y=\"43\"/><line x=\"38\" y=\"56\"/><line x=\"0\" y=\"28\"/><line x=\"38\" y=\"0\"/><line x=\"38\" y=\"13\"/><line x=\"63\" y=\"13\"/><arc rx=\"35\" ry=\"35\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"97\" y=\"48\"/><line x=\"97\" y=\"97\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Bent Right Arrow\" h=\"97\" w=\"97.01\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.15\" y=\"1\" perimeter=\"0\" name=\"S\"/><constraint x=\"1\" y=\"0.29\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"29.01\" y=\"97\"/><line x=\"29.01\" y=\"48\"/><arc rx=\"5\" ry=\"5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"34.01\" y=\"43\"/><line x=\"59.01\" y=\"43\"/><line x=\"59.01\" y=\"56\"/><line x=\"97.01\" y=\"28\"/><line x=\"59.01\" y=\"0\"/><line x=\"59.01\" y=\"13\"/><line x=\"34.01\" y=\"13\"/><arc rx=\"35\" ry=\"35\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"0.01\" y=\"48\"/><line x=\"0.01\" y=\"97\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Bent Up Arrow\" h=\"83.5\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.71\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0\" y=\"0.82\" perimeter=\"0\" name=\"W\"/></connections><background><path><move x=\"0\" y=\"53.5\"/><line x=\"54\" y=\"53.5\"/><line x=\"54\" y=\"23.5\"/><line x=\"42\" y=\"23.5\"/><line x=\"69\" y=\"0\"/><line x=\"97\" y=\"23.5\"/><line x=\"84\" y=\"23.5\"/><line x=\"84\" y=\"83.5\"/><line x=\"0\" y=\"83.5\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Callout Double Arrow\" h=\"97.5\" w=\"50\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><path><move x=\"15\" y=\"24\"/><line x=\"15\" y=\"19\"/><line x=\"6\" y=\"19\"/><line x=\"25\" y=\"0\"/><line x=\"44\" y=\"19\"/><line x=\"35\" y=\"19\"/><line x=\"35\" y=\"24\"/><line x=\"50\" y=\"24\"/><line x=\"50\" y=\"74\"/><line x=\"35\" y=\"74\"/><line x=\"35\" y=\"79\"/><line x=\"44\" y=\"79\"/><line x=\"25\" y=\"97.5\"/><line x=\"6\" y=\"79\"/><line x=\"15\" y=\"79\"/><line x=\"15\" y=\"74\"/><line x=\"0\" y=\"74\"/><line x=\"0\" y=\"24\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Callout Quad Arrow\" h=\"97\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"38.5\" y=\"23.5\"/><line x=\"38.5\" y=\"18.5\"/><line x=\"29.5\" y=\"18.5\"/><line x=\"48.5\" y=\"0\"/><line x=\"67.5\" y=\"18.5\"/><line x=\"58.5\" y=\"18.5\"/><line x=\"58.5\" y=\"23.5\"/><line x=\"73.5\" y=\"23.5\"/><line x=\"73.5\" y=\"38.5\"/><line x=\"78.5\" y=\"38.5\"/><line x=\"78.5\" y=\"29.5\"/><line x=\"97\" y=\"48.5\"/><line x=\"78.5\" y=\"67.5\"/><line x=\"78.5\" y=\"58.5\"/><line x=\"73.5\" y=\"58.5\"/><line x=\"73.5\" y=\"73.5\"/><line x=\"58.5\" y=\"73.5\"/><line x=\"58.5\" y=\"78.5\"/><line x=\"67.5\" y=\"78.5\"/><line x=\"48.5\" y=\"97\"/><line x=\"29.5\" y=\"78.5\"/><line x=\"38.5\" y=\"78.5\"/><line x=\"38.5\" y=\"73.5\"/><line x=\"23.5\" y=\"73.5\"/><line x=\"23.5\" y=\"58.5\"/><line x=\"18.5\" y=\"58.5\"/><line x=\"18.5\" y=\"67.5\"/><line x=\"0\" y=\"48.5\"/><line x=\"18.5\" y=\"29.5\"/><line x=\"18.5\" y=\"38.5\"/><line x=\"23.5\" y=\"38.5\"/><line x=\"23.5\" y=\"23.5\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Callout Up Arrow\" h=\"98\" w=\"60\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/></connections><background><path><move x=\"20\" y=\"39\"/><line x=\"20\" y=\"19\"/><line x=\"11\" y=\"19\"/><line x=\"30\" y=\"0\"/><line x=\"49\" y=\"19\"/><line x=\"40\" y=\"19\"/><line x=\"40\" y=\"39\"/><line x=\"60\" y=\"39\"/><line x=\"60\" y=\"98\"/><line x=\"0\" y=\"98\"/><line x=\"0\" y=\"39\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Chevron Arrow\" h=\"60\" w=\"96\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.31\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"30\" y=\"30\"/><line x=\"0\" y=\"0\"/><line x=\"66\" y=\"0\"/><line x=\"96\" y=\"30\"/><line x=\"66\" y=\"60\"/><line x=\"0\" y=\"60\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Circular Arrow\" h=\"69.5\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.12\" y=\"0.64\" perimeter=\"0\" name=\"SW\"/><constraint x=\"0.794\" y=\"1\" perimeter=\"0\" name=\"SE\"/></connections><background><path><move x=\"0\" y=\"44.5\"/><arc rx=\"44.5\" ry=\"44.5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"89\" y=\"44.5\"/><line x=\"97\" y=\"44.5\"/><line x=\"77\" y=\"69.5\"/><line x=\"57\" y=\"44.5\"/><line x=\"65\" y=\"44.5\"/><arc rx=\"20.5\" ry=\"20.5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"24\" y=\"44.5\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Jump-in Arrow 1\" h=\"99.41\" w=\"96\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.024\" perimeter=\"0\" name=\"NW\"/><constraint x=\"0.657\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><linejoin join=\"round\"/><path><move x=\"30\" y=\"60.41\"/><line x=\"48\" y=\"60.41\"/><arc rx=\"60\" ry=\"60\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"0\" y=\"2.41\"/><arc rx=\"75\" ry=\"75\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"78\" y=\"60.41\"/><line x=\"96\" y=\"60.41\"/><line x=\"63\" y=\"99.41\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Jump-in Arrow 2\" h=\"99.41\" w=\"96\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"1\" y=\"0.024\" perimeter=\"0\" name=\"NE\"/><constraint x=\"0.343\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><linejoin join=\"round\"/><path><move x=\"66\" y=\"60.41\"/><line x=\"48\" y=\"60.41\"/><arc rx=\"60\" ry=\"60\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"96\" y=\"2.41\"/><arc rx=\"75\" ry=\"75\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"18\" y=\"60.41\"/><line x=\"0\" y=\"60.41\"/><line x=\"33\" y=\"99.41\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Left and Up Arrow\" h=\"96.5\" w=\"96.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.71\" perimeter=\"0\" name=\"W\"/><constraint x=\"0.71\" y=\"0\" perimeter=\"0\" name=\"N\"/></connections><background><path><move x=\"23.5\" y=\"53.5\"/><line x=\"53.5\" y=\"53.5\"/><line x=\"53.5\" y=\"23.5\"/><line x=\"41.5\" y=\"23.5\"/><line x=\"68.5\" y=\"0\"/><line x=\"96.5\" y=\"23.5\"/><line x=\"83.5\" y=\"23.5\"/><line x=\"83.5\" y=\"83.5\"/><line x=\"23.5\" y=\"83.5\"/><line x=\"23.5\" y=\"96.5\"/><line x=\"0\" y=\"68.5\"/><line x=\"23.5\" y=\"41.5\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Left Sharp Edged Head Arrow\" h=\"60\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"97.5\" y=\"20\"/><line x=\"18.5\" y=\"20\"/><line x=\"30.5\" y=\"0\"/><line x=\"18.5\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"18.5\" y=\"60\"/><line x=\"30.5\" y=\"60\"/><line x=\"18.5\" y=\"40\"/><line x=\"97.5\" y=\"40\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Notched Signal-in Arrow\" h=\"30\" w=\"96.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.13\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"0\" y=\"0\"/><line x=\"83\" y=\"0\"/><line x=\"96.5\" y=\"15\"/><line x=\"83\" y=\"30\"/><line x=\"0\" y=\"30\"/><line x=\"13\" y=\"15\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Quad Arrow\" h=\"97.5\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"39\" y=\"39\"/><line x=\"39\" y=\"19\"/><line x=\"30\" y=\"19\"/><line x=\"49\" y=\"0\"/><line x=\"68\" y=\"19\"/><line x=\"59\" y=\"19\"/><line x=\"59\" y=\"39\"/><line x=\"79\" y=\"39\"/><line x=\"79\" y=\"30\"/><line x=\"97.5\" y=\"49\"/><line x=\"79\" y=\"68\"/><line x=\"79\" y=\"59\"/><line x=\"59\" y=\"59\"/><line x=\"59\" y=\"79\"/><line x=\"68\" y=\"79\"/><line x=\"49\" y=\"97.5\"/><line x=\"30\" y=\"79\"/><line x=\"39\" y=\"79\"/><line x=\"39\" y=\"59\"/><line x=\"19\" y=\"59\"/><line x=\"19\" y=\"68\"/><line x=\"0\" y=\"49\"/><line x=\"19\" y=\"30\"/><line x=\"19\" y=\"39\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Right Notched Arrow\" h=\"70\" w=\"96.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.13\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"0\" y=\"20\"/><line x=\"58\" y=\"20\"/><line x=\"58\" y=\"0\"/><line x=\"96.5\" y=\"35\"/><line x=\"58\" y=\"70\"/><line x=\"58\" y=\"50\"/><line x=\"0\" y=\"50\"/><line x=\"13\" y=\"35\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Sharp Edged Arrow\" h=\"60\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"97.5\" y=\"20\"/><line x=\"18.5\" y=\"20\"/><line x=\"27.5\" y=\"5\"/><line x=\"18.5\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"18.5\" y=\"60\"/><line x=\"27.5\" y=\"55\"/><line x=\"18.5\" y=\"40\"/><line x=\"97.5\" y=\"40\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Signal-in Arrow\" h=\"30\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"0\" y=\"0\"/><line x=\"84\" y=\"0\"/><line x=\"97.5\" y=\"15\"/><line x=\"84\" y=\"30\"/><line x=\"0\" y=\"30\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Slender Left Arrow\" h=\"60\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"97.5\" y=\"20\"/><line x=\"18.5\" y=\"20\"/><line x=\"18.5\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"18.5\" y=\"60\"/><line x=\"18.5\" y=\"40\"/><line x=\"97.5\" y=\"40\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Slender Two Way Arrow\" h=\"60\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"78.5\" y=\"20\"/><line x=\"18.5\" y=\"20\"/><line x=\"18.5\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"18.5\" y=\"60\"/><line x=\"18.5\" y=\"40\"/><line x=\"78.5\" y=\"40\"/><line x=\"78.5\" y=\"60\"/><line x=\"97.5\" y=\"30\"/><line x=\"78.5\" y=\"0\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Slender Wide Tailed Arrow\" h=\"60\" w=\"96.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"0.8\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"58.5\" y=\"20\"/><line x=\"18.5\" y=\"20\"/><line x=\"18.5\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"18.5\" y=\"60\"/><line x=\"18.5\" y=\"40\"/><line x=\"58.5\" y=\"40\"/><line x=\"73.5\" y=\"60\"/><line x=\"96.5\" y=\"60\"/><line x=\"76.5\" y=\"30\"/><line x=\"96.5\" y=\"0\"/><line x=\"73.5\" y=\"0\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Striped Arrow\" h=\"70\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"24\" y=\"20\"/><line x=\"59\" y=\"20\"/><line x=\"59\" y=\"0\"/><line x=\"97.5\" y=\"35\"/><line x=\"59\" y=\"70\"/><line x=\"59\" y=\"50\"/><line x=\"24\" y=\"50\"/><close/></path></background><foreground><fillstroke/><rect x=\"8\" y=\"20\" w=\"12\" h=\"30\"/><fillstroke/><rect x=\"0\" y=\"20\" w=\"4\" h=\"30\"/><fillstroke/></foreground></shape><shape name=\"Stylised Notched Arrow\" h=\"60\" w=\"96.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.13\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><miterlimit limit=\"8\"/><path><move x=\"0\" y=\"5\"/><line x=\"68\" y=\"20\"/><line x=\"58\" y=\"0\"/><line x=\"96.5\" y=\"30\"/><line x=\"58\" y=\"60\"/><line x=\"68\" y=\"45\"/><line x=\"0\" y=\"55\"/><line x=\"13\" y=\"30\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Triad Arrow\" h=\"68\" w=\"97.5\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.72\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.72\" perimeter=\"0\" name=\"E\"/><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/></connections><background><path><move x=\"39\" y=\"39\"/><line x=\"39\" y=\"19\"/><line x=\"30\" y=\"19\"/><line x=\"49\" y=\"0\"/><line x=\"68\" y=\"19\"/><line x=\"59\" y=\"19\"/><line x=\"59\" y=\"39\"/><line x=\"79\" y=\"39\"/><line x=\"79\" y=\"30\"/><line x=\"97.5\" y=\"49\"/><line x=\"79\" y=\"68\"/><line x=\"79\" y=\"59\"/><line x=\"39\" y=\"59\"/><line x=\"19\" y=\"59\"/><line x=\"19\" y=\"68\"/><line x=\"0\" y=\"49\"/><line x=\"19\" y=\"30\"/><line x=\"19\" y=\"39\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Two Way Arrow Horizontal\" h=\"60\" w=\"96\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.5\" perimeter=\"0\" name=\"W\"/><constraint x=\"1\" y=\"0.5\" perimeter=\"0\" name=\"E\"/></connections><background><path><move x=\"63\" y=\"15\"/><line x=\"63\" y=\"0\"/><line x=\"96\" y=\"30\"/><line x=\"63\" y=\"60\"/><line x=\"63\" y=\"45\"/><line x=\"33\" y=\"45\"/><line x=\"33\" y=\"60\"/><line x=\"0\" y=\"30\"/><line x=\"33\" y=\"0\"/><line x=\"33\" y=\"15\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"Two Way Arrow Vertical\" h=\"96\" w=\"60\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.5\" y=\"0\" perimeter=\"0\" name=\"N\"/><constraint x=\"0.5\" y=\"1\" perimeter=\"0\" name=\"S\"/></connections><background><path><move x=\"15\" y=\"63\"/><line x=\"0\" y=\"63\"/><line x=\"30\" y=\"96\"/><line x=\"60\" y=\"63\"/><line x=\"45\" y=\"63\"/><line x=\"45\" y=\"33\"/><line x=\"60\" y=\"33\"/><line x=\"30\" y=\"0\"/><line x=\"0\" y=\"33\"/><line x=\"15\" y=\"33\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"U Turn Arrow\" h=\"98\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.12\" y=\"1\" perimeter=\"0\" name=\"SW\"/><constraint x=\"0.792\" y=\"0.71\" perimeter=\"0\" name=\"SE\"/></connections><background><path><move x=\"0\" y=\"44.5\"/><arc rx=\"44.5\" ry=\"44.5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"89\" y=\"44.5\"/><line x=\"97\" y=\"44.5\"/><line x=\"77\" y=\"69.5\"/><line x=\"57\" y=\"44.5\"/><line x=\"65\" y=\"44.5\"/><arc rx=\"20.5\" ry=\"20.5\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"24\" y=\"44.83\"/><line x=\"24\" y=\"98\"/><line x=\"0\" y=\"98\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"U Turn Down Arrow\" h=\"62\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.91\" y=\"1\" perimeter=\"0\" name=\"SE\"/><constraint x=\"0.237\" y=\"1\" perimeter=\"0\" name=\"SW\"/></connections><background><path><move x=\"97\" y=\"62\"/><line x=\"97\" y=\"32\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"33\" y=\"32\"/><line x=\"46\" y=\"32\"/><line x=\"23\" y=\"62\"/><line x=\"0\" y=\"32\"/><line x=\"13\" y=\"32\"/><arc rx=\"32\" ry=\"32\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"45\" y=\"0\"/><line x=\"65\" y=\"0\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"53\" y=\"3\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"78\" y=\"32\"/><line x=\"78\" y=\"62\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"U Turn Left Arrow\" h=\"97.07\" w=\"62.23\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0\" y=\"0.76\" perimeter=\"0\" name=\"SW\"/><constraint x=\"0\" y=\"0.1\" perimeter=\"0\" name=\"NW\"/></connections><background><path><move x=\"0\" y=\"0.19\"/><line x=\"30\" y=\"0.07\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-90.22\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"30.25\" y=\"64.07\"/><line x=\"30.2\" y=\"51.07\"/><line x=\"0.29\" y=\"74.19\"/><line x=\"30.37\" y=\"97.07\"/><line x=\"30.32\" y=\"84.07\"/><arc rx=\"32\" ry=\"32\" x-axis-rotation=\"-90.22\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"62.2\" y=\"51.95\"/><line x=\"62.13\" y=\"31.95\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-90.22\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"59.17\" y=\"43.96\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-90.22\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"30.08\" y=\"19.07\"/><line x=\"0.08\" y=\"19.19\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"U Turn Right Arrow\" h=\"97.07\" w=\"62.23\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"1\" y=\"0.76\" perimeter=\"0\" name=\"SW\"/><constraint x=\"1\" y=\"0.1\" perimeter=\"0\" name=\"NW\"/></connections><background><path><move x=\"62.23\" y=\"0.19\"/><line x=\"32.23\" y=\"0.07\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-89.78\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"31.99\" y=\"64.07\"/><line x=\"32.03\" y=\"51.07\"/><line x=\"61.95\" y=\"74.19\"/><line x=\"31.86\" y=\"97.07\"/><line x=\"31.91\" y=\"84.07\"/><arc rx=\"32\" ry=\"32\" x-axis-rotation=\"-89.78\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"0.03\" y=\"51.95\"/><line x=\"0.11\" y=\"31.95\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-89.78\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"3.06\" y=\"43.96\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"-89.78\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"32.16\" y=\"19.07\"/><line x=\"62.16\" y=\"19.19\"/><close/></path></background><foreground><fillstroke/></foreground></shape><shape name=\"U Turn Up Arrow\" h=\"62\" w=\"97\" aspect=\"variable\" strokewidth=\"inherit\"><connections><constraint x=\"0.91\" y=\"0\" perimeter=\"0\" name=\"NE\"/><constraint x=\"0.237\" y=\"0\" perimeter=\"0\" name=\"NW\"/></connections><background><path><move x=\"97\" y=\"0\"/><line x=\"97\" y=\"30\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"33\" y=\"30\"/><line x=\"46\" y=\"30\"/><line x=\"23\" y=\"0\"/><line x=\"0\" y=\"30\"/><line x=\"13\" y=\"30\"/><arc rx=\"32\" ry=\"32\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"45\" y=\"62\"/><line x=\"65\" y=\"62\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"1\" x=\"53\" y=\"59\"/><arc rx=\"30\" ry=\"30\" x-axis-rotation=\"0\" large-arc-flag=\"0\" sweep-flag=\"0\" x=\"78\" y=\"30\"/><line x=\"78\" y=\"0\"/><close/></path></background><foreground><fillstroke/></foreground></shape></shapes>";
export default _default;
