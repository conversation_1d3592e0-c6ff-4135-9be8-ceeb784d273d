"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStencil = void 0;
var arrows_1 = __importDefault(require("./arrows"));
var basic_1 = __importDefault(require("./basic"));
var flowchart_1 = __importDefault(require("./flowchart"));
var bpmn_1 = __importDefault(require("./bpmn"));
var Credit_Card_128x128_1 = __importDefault(require("./clipart/Credit_Card_128x128"));
var Database_128x128_1 = __importDefault(require("./clipart/Database_128x128"));
var Doctor1_128x128_1 = __importDefault(require("./clipart/Doctor1_128x128"));
var Earth_globe_128x128_1 = __importDefault(require("./clipart/Earth_globe_128x128"));
var Email_128x128_1 = __importDefault(require("./clipart/Email_128x128"));
var Empty_Folder_128x128_1 = __importDefault(require("./clipart/Empty_Folder_128x128"));
var Firewall_02_128x128_1 = __importDefault(require("./clipart/Firewall_02_128x128"));
var Full_Folder_128x128_1 = __importDefault(require("./clipart/Full_Folder_128x128"));
var Gear_128x128_1 = __importDefault(require("./clipart/Gear_128x128"));
var Graph_128x128_1 = __importDefault(require("./clipart/Graph_128x128"));
var iMac_128x128_1 = __importDefault(require("./clipart/iMac_128x128"));
var iPad_128x128_1 = __importDefault(require("./clipart/iPad_128x128"));
var Laptop_128x128_1 = __importDefault(require("./clipart/Laptop_128x128"));
var Lock_128x128_1 = __importDefault(require("./clipart/Lock_128x128"));
var MacBook_128x128_1 = __importDefault(require("./clipart/MacBook_128x128"));
var Monitor_Tower_128x128_1 = __importDefault(require("./clipart/Monitor_Tower_128x128"));
var Piggy_Bank_128x128_1 = __importDefault(require("./clipart/Piggy_Bank_128x128"));
var Pilot1_128x128_1 = __importDefault(require("./clipart/Pilot1_128x128"));
var Printer_128x128_1 = __importDefault(require("./clipart/Printer_128x128"));
var Router_Icon_128x128_1 = __importDefault(require("./clipart/Router_Icon_128x128"));
var Safe_128x128_1 = __importDefault(require("./clipart/Safe_128x128"));
var Security1_128x128_1 = __importDefault(require("./clipart/Security1_128x128"));
var Server_Tower_128x128_1 = __importDefault(require("./clipart/Server_Tower_128x128"));
var Shopping_Cart_128x128_1 = __importDefault(require("./clipart/Shopping_Cart_128x128"));
var Software_128x128_1 = __importDefault(require("./clipart/Software_128x128"));
var Soldier1_128x128_1 = __importDefault(require("./clipart/Soldier1_128x128"));
var Suit1_128x128_1 = __importDefault(require("./clipart/Suit1_128x128"));
var Suit2_128x128_1 = __importDefault(require("./clipart/Suit2_128x128"));
var Suit3_128x128_1 = __importDefault(require("./clipart/Suit3_128x128"));
var Tech1_128x128_1 = __importDefault(require("./clipart/Tech1_128x128"));
var Telesales1_128x128_1 = __importDefault(require("./clipart/Telesales1_128x128"));
var Virtual_Machine_128x128_1 = __importDefault(require("./clipart/Virtual_Machine_128x128"));
var Virus_128x128_1 = __importDefault(require("./clipart/Virus_128x128"));
var Wireless_Router_N_128x128_1 = __importDefault(require("./clipart/Wireless_Router_N_128x128"));
var Worker1_128x128_1 = __importDefault(require("./clipart/Worker1_128x128"));
var Workstation_128x128_1 = __importDefault(require("./clipart/Workstation_128x128"));
var XML = ".xml";
var PNG = ".png";
var dict = {};
dict["arrows" + XML] = arrows_1.default;
dict["basic" + XML] = basic_1.default;
dict["flowchart" + XML] = flowchart_1.default;
dict["bpmn" + XML] = bpmn_1.default;
dict["Credit_Card_128x128" + PNG] = Credit_Card_128x128_1.default;
dict["Database_128x128" + PNG] = Database_128x128_1.default;
dict["Doctor1_128x128" + PNG] = Doctor1_128x128_1.default;
dict["Earth_globe_128x128" + PNG] = Earth_globe_128x128_1.default;
dict["Email_128x128" + PNG] = Email_128x128_1.default;
dict["Empty_Folder_128x128" + PNG] = Empty_Folder_128x128_1.default;
dict["Firewall_02_128x128" + PNG] = Firewall_02_128x128_1.default;
dict["Full_Folder_128x128" + PNG] = Full_Folder_128x128_1.default;
dict["Gear_128x128" + PNG] = Gear_128x128_1.default;
dict["Graph_128x128" + PNG] = Graph_128x128_1.default;
dict["iMac_128x128" + PNG] = iMac_128x128_1.default;
dict["iPad_128x128" + PNG] = iPad_128x128_1.default;
dict["Laptop_128x128" + PNG] = Laptop_128x128_1.default;
dict["Lock_128x128" + PNG] = Lock_128x128_1.default;
dict["MacBook_128x128" + PNG] = MacBook_128x128_1.default;
dict["Monitor_Tower_128x128" + PNG] = Monitor_Tower_128x128_1.default;
dict["Piggy_Bank_128x128" + PNG] = Piggy_Bank_128x128_1.default;
dict["Pilot1_128x128" + PNG] = Pilot1_128x128_1.default;
dict["Printer_128x128" + PNG] = Printer_128x128_1.default;
dict["Router_Icon_128x128" + PNG] = Router_Icon_128x128_1.default;
dict["Safe_128x128" + PNG] = Safe_128x128_1.default;
dict["Security1_128x128" + PNG] = Security1_128x128_1.default;
dict["Server_Tower_128x128" + PNG] = Server_Tower_128x128_1.default;
dict["Shopping_Cart_128x128" + PNG] = Shopping_Cart_128x128_1.default;
dict["Software_128x128" + PNG] = Software_128x128_1.default;
dict["Soldier1_128x128" + PNG] = Soldier1_128x128_1.default;
dict["Suit1_128x128" + PNG] = Suit1_128x128_1.default;
dict["Suit2_128x128" + PNG] = Suit2_128x128_1.default;
dict["Suit3_128x128" + PNG] = Suit3_128x128_1.default;
dict["Tech1_128x128" + PNG] = Tech1_128x128_1.default;
dict["Telesales1_128x128" + PNG] = Telesales1_128x128_1.default;
dict["Virtual_Machine_128x128" + PNG] = Virtual_Machine_128x128_1.default;
dict["Virus_128x128" + PNG] = Virus_128x128_1.default;
dict["Wireless_Router_N_128x128" + PNG] = Wireless_Router_N_128x128_1.default;
dict["Worker1_128x128" + PNG] = Worker1_128x128_1.default;
dict["Workstation_128x128" + PNG] = Workstation_128x128_1.default;
var getStencil = function (name) { return dict[name]; };
exports.getStencil = getStencil;
