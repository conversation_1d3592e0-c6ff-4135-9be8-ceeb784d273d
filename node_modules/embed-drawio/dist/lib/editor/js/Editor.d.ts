/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
/**
 * Editor constructor executed on page load.
 */
export function Editor(chromeless: any, themes: any, model: any, graph: any, editable: any): void;
export class Editor {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    /**
     * Editor constructor executed on page load.
     */
    constructor(chromeless: any, themes: any, model: any, graph: any, editable: any);
    chromeless: any;
    graph: any;
    editable: any;
    undoManager: import("mxgraph").mxUndoManager;
    status: string;
    getOrCreateFilename: () => any;
    getFilename: () => any;
    setStatus: (value: any) => void;
    getStatus: () => string;
    graphChangeListener: (sender: any, eventObject: any) => void;
    /**
     * Stores initial state of mxClient.NO_FO.
     */
    originalNoForeignObject: boolean;
    /**
     * Specifies the image URL to be used for the transparent background.
     */
    transparentImage: string;
    /**
     * Specifies if the canvas should be extended in all directions. Default is true.
     */
    extendCanvas: boolean;
    /**
     * Specifies the order of OK/Cancel buttons in dialogs. Default is true.
     * Cancel first is used on Macs, Windows/Confluence uses cancel last.
     */
    cancelFirst: boolean;
    /**
     * Specifies if the editor is enabled. Default is true.
     */
    enabled: boolean;
    /**
     * Contains the name which was used for the last save. Default value is null.
     */
    filename: any;
    /**
     * Contains the current modified state of the diagram. This is false for
     * new diagrams and after the diagram was saved.
     */
    modified: any;
    /**
     * Specifies if the diagram should be saved automatically if possible. Default
     * is true.
     */
    autosave: any;
    /**
     * Specifies the top spacing for the initial page view. Default is 0.
     */
    initialTopSpacing: number;
    /**
     * Specifies the app name. Default is document.title.
     */
    appName: string;
    /**
     *
     */
    editBlankUrl: string;
    /**
     * Default value for the graph container overflow style.
     */
    defaultGraphOverflow: string;
    /**
     * Initializes the environment.
     */
    init(): void;
    /**
     * Sets the XML node for the current diagram.
     */
    isChromelessView(): any;
    /**
     * Sets the XML node for the current diagram.
     */
    setAutosave(value: any): void;
    /**
     *
     */
    getEditBlankUrl(params: any): string;
    /**
     *
     */
    editAsNew(xml: any, title: any): void;
    /**
     * Sets the XML node for the current diagram.
     */
    createGraph(themes: any, model: any): Graph;
    /**
     * Sets the XML node for the current diagram.
     */
    resetGraph(): void;
    /**
     * Sets the XML node for the current diagram.
     */
    readGraphState(node: any): void;
    /**
     * Sets the XML node for the current diagram.
     */
    setGraphXml(node: any): void;
    /**
     * Returns the XML node that represents the current diagram.
     */
    getGraphXml(ignoreSelection: any): any;
    /**
     * Keeps the graph container in sync with the persistent graph state
     */
    updateGraphComponents(): void;
    /**
     * Sets the modified flag.
     */
    setModified(value: any): void;
    /**
     * Sets the filename.
     */
    setFilename(value: any): void;
    /**
     * Creates and returns a new undo manager.
     */
    createUndoManager(): import("mxgraph").mxUndoManager;
    undoListener: ((sender: any, evt: any) => void) | undefined;
    /**
     * Adds basic stencil set (no namespace).
     */
    initStencilRegistry(): void;
    /**
     * Creates and returns a new undo manager.
     */
    destroy(): void;
}
export namespace Editor {
    export let pageCounter: number;
    export let useLocalStorage: boolean;
    export { moveImage };
    export { rowMoveImage };
    export { helpImage };
    export { checkmarkImage };
    export let ctrlKey: string;
    export let hintOffset: number;
    export let popupsAllowed: boolean;
}
/**
 *
 */
export function ErrorDialog(editorUi: any, title: any, message: any, buttonText: any, fn: any, retry: any, buttonText2: any, fn2: any, hide: any, buttonText3: any, fn3: any): void;
export class ErrorDialog {
    /**
     *
     */
    constructor(editorUi: any, title: any, message: any, buttonText: any, fn: any, retry: any, buttonText2: any, fn2: any, hide: any, buttonText3: any, fn3: any);
    init: () => void;
    container: HTMLDivElement;
}
/**
 * Basic dialogs that are available in the viewer (print dialog).
 */
export function Dialog(editorUi: any, elt: any, w: any, h: any, modal: any, closable: any, onClose: any, noScroll: any, transparent: any, onResize: any, ignoreBgClick: any): void;
export class Dialog {
    /**
     * Basic dialogs that are available in the viewer (print dialog).
     */
    constructor(editorUi: any, elt: any, w: any, h: any, modal: any, closable: any, onClose: any, noScroll: any, transparent: any, onResize: any, ignoreBgClick: any);
    bg: any;
    dialogImg: HTMLImageElement | undefined;
    resizeListener: () => void;
    onDialogClose: any;
    container: any;
    /**
     *
     */
    zIndex: number;
    /**
     *
     */
    noColorImage: string;
    /**
     *
     */
    closeImage: string;
    /**
     *
     */
    clearImage: string;
    /**
     *
     */
    lockedImage: string;
    /**
     *
     */
    unlockedImage: string;
    /**
     * Removes the dialog from the DOM.
     */
    bgOpacity: number;
    /**
     * Removes the dialog from the DOM.
     */
    getPosition(left: any, top: any): import("mxgraph").mxPoint;
    /**
     * Removes the dialog from the DOM.
     */
    close(cancel: any, isEsc: any): false | undefined;
}
export namespace Dialog {
    let backdropColor: string;
}
/**
 * Constructs a new page setup dialog.
 */
export function PageSetupDialog(editorUi: any): void;
export class PageSetupDialog {
    /**
     * Constructs a new page setup dialog.
     */
    constructor(editorUi: any);
    container: HTMLTableElement;
}
export namespace PageSetupDialog {
    /**
     *
     */
    function addPageFormatPanel(div: any, namePostfix: any, pageFormat: any, pageFormatListener: any): {
        set: (value: any) => void;
        get: () => any;
        widthInput: HTMLInputElement;
        heightInput: HTMLInputElement;
    };
    /**
     *
     */
    function getFormats(): ({
        key: string;
        title: string;
        format: import("mxgraph").mxRectangle;
    } | {
        key: string;
        title: string;
        format: null;
    })[];
}
import { Graph } from "./Graph";
import { moveImage } from "../images/base64";
import { rowMoveImage } from "../images/base64";
import { helpImage } from "../images/base64";
import { checkmarkImage } from "../images/base64";
