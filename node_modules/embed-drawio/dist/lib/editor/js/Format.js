"use strict";
/* eslint-disable */
/* eslint-enable no-undef, prettier/prettier, no-unused-vars */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagramFormatPanel = exports.BaseFormatPanel = exports.ArrangePanel = exports.Format = exports.TextFormatPanel = exports.StyleFormatPanel = void 0;
var mxgraph_1 = require("../../core/mxgraph");
var Dialogs_1 = require("./Dialogs");
var Editor_1 = require("./Editor");
var Menus_1 = require("./Menus");
var Graph_1 = require("./Graph");
var base64_1 = require("../images/base64");
var PageSetup_1 = require("./PageSetup");
var base64_2 = require("../images/base64");
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
function Format(editorUi, container) {
    this.editorUi = editorUi;
    this.container = container;
}
exports.Format = Format;
/**
 * Returns information about the current selection.
 */
Format.prototype.labelIndex = 0;
/**
 * Returns information about the current selection.
 */
Format.prototype.currentIndex = 0;
/**
 * Returns information about the current selection.
 */
Format.prototype.showCloseButton = true;
/**
 * Background color for inactive tabs.
 */
Format.prototype.inactiveTabBackgroundColor = "#f1f3f4";
/**
 * Background color for inactive tabs.
 */
Format.prototype.roundableShapes = [
    "label",
    "rectangle",
    "internalStorage",
    "corner",
    "parallelogram",
    "swimlane",
    "triangle",
    "trapezoid",
    "ext",
    "step",
    "tee",
    "process",
    "link",
    "rhombus",
    "offPageConnector",
    "loopLimit",
    "hexagon",
    "manualInput",
    "curlyBracket",
    "singleArrow",
    "callout",
    "doubleArrow",
    "flexArrow",
    "card",
    "umlLifeline",
];
/**
 * Adds the label menu items to the given menu and parent.
 */
Format.prototype.init = function () {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    this.update = mxgraph_1.mxUtils.bind(this, function () {
        this.clearSelectionState();
        this.refresh();
    });
    graph.getSelectionModel().addListener(mxgraph_1.mxEvent.CHANGE, this.update);
    graph.addListener(mxgraph_1.mxEvent.EDITING_STARTED, this.update);
    graph.addListener(mxgraph_1.mxEvent.EDITING_STOPPED, this.update);
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, this.update);
    graph.addListener(mxgraph_1.mxEvent.ROOT, mxgraph_1.mxUtils.bind(this, function () {
        this.refresh();
    }));
    this.refresh();
};
/**
 * Returns information about the current selection.
 */
Format.prototype.clearSelectionState = function () {
    this.selectionState = null;
};
/**
 * Returns information about the current selection.
 */
Format.prototype.getSelectionState = function () {
    if (this.selectionState == null) {
        this.selectionState = this.createSelectionState();
    }
    return this.selectionState;
};
/**
 * Returns information about the current selection.
 */
Format.prototype.createSelectionState = function () {
    var cells = this.editorUi.editor.graph.getSelectionCells();
    var result = this.initSelectionState();
    for (var i = 0; i < cells.length; i++) {
        this.updateSelectionStateForCell(result, cells[i], cells);
    }
    return result;
};
/**
 * Returns information about the current selection.
 */
Format.prototype.initSelectionState = function () {
    return {
        vertices: [],
        edges: [],
        x: null,
        y: null,
        width: null,
        height: null,
        style: {},
        containsImage: false,
        containsLabel: false,
        fill: true,
        glass: true,
        rounded: true,
        comic: true,
        autoSize: false,
        image: true,
        shadow: true,
        lineJumps: true,
        resizable: true,
        table: false,
        cell: false,
        row: false,
        movable: true,
        rotatable: true,
    };
};
/**
 * Returns information about the current selection.
 */
Format.prototype.updateSelectionStateForCell = function (result, cell) {
    var graph = this.editorUi.editor.graph;
    if (graph.getModel().isVertex(cell)) {
        result.resizable = result.resizable && graph.isCellResizable(cell);
        result.rotatable = result.rotatable && graph.isCellRotatable(cell);
        result.movable =
            result.movable &&
                graph.isCellMovable(cell) &&
                !graph.isTableRow(cell) &&
                !graph.isTableCell(cell);
        result.table = result.table || graph.isTable(cell);
        result.cell = result.cell || graph.isTableCell(cell);
        result.row = result.row || graph.isTableRow(cell);
        result.vertices.push(cell);
        var geo = graph.getCellGeometry(cell);
        if (geo != null) {
            if (geo.width > 0) {
                if (result.width == null) {
                    result.width = geo.width;
                }
                else if (result.width != geo.width) {
                    result.width = "";
                }
            }
            else {
                result.containsLabel = true;
            }
            if (geo.height > 0) {
                if (result.height == null) {
                    result.height = geo.height;
                }
                else if (result.height != geo.height) {
                    result.height = "";
                }
            }
            else {
                result.containsLabel = true;
            }
            if (!geo.relative || geo.offset != null) {
                var x = geo.relative ? geo.offset.x : geo.x;
                var y = geo.relative ? geo.offset.y : geo.y;
                if (result.x == null) {
                    result.x = x;
                }
                else if (result.x != x) {
                    result.x = "";
                }
                if (result.y == null) {
                    result.y = y;
                }
                else if (result.y != y) {
                    result.y = "";
                }
            }
        }
    }
    else if (graph.getModel().isEdge(cell)) {
        result.edges.push(cell);
        result.resizable = false;
        result.rotatable = false;
        result.movable = false;
    }
    var state = graph.view.getState(cell);
    if (state != null) {
        result.autoSize = result.autoSize || this.isAutoSizeState(state);
        result.glass = result.glass && this.isGlassState(state);
        result.rounded = result.rounded && this.isRoundedState(state);
        result.lineJumps = result.lineJumps && this.isLineJumpState(state);
        result.comic = result.comic && this.isComicState(state);
        result.image = result.image && this.isImageState(state);
        result.shadow = result.shadow && this.isShadowState(state);
        result.fill = result.fill && this.isFillState(state);
        var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
        result.containsImage = result.containsImage || shape == "image";
        for (var key in state.style) {
            var value = state.style[key];
            if (value != null) {
                if (result.style[key] == null) {
                    result.style[key] = value;
                }
                else if (result.style[key] != value) {
                    result.style[key] = "";
                }
            }
        }
    }
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isFillState = function (state) {
    return (state.view.graph.model.isVertex(state.cell) ||
        mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null) == "arrow" ||
        mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null) == "filledEdge" ||
        mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null) == "flexArrow");
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isGlassState = function (state) {
    var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
    return (shape == "label" ||
        shape == "rectangle" ||
        shape == "internalStorage" ||
        shape == "ext" ||
        shape == "umlLifeline" ||
        shape == "swimlane" ||
        shape == "process");
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isRoundedState = function (state) {
    return state.shape != null
        ? state.shape.isRoundable()
        : mxgraph_1.mxUtils.indexOf(this.roundableShapes, mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null)) >= 0;
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isLineJumpState = function (state) {
    var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
    var curved = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_CURVED, false);
    return !curved && (shape == "connector" || shape == "filledEdge");
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isComicState = function (state) {
    var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
    return (mxgraph_1.mxUtils.indexOf([
        "label",
        "rectangle",
        "internalStorage",
        "corner",
        "parallelogram",
        "note",
        "collate",
        "swimlane",
        "triangle",
        "trapezoid",
        "ext",
        "step",
        "tee",
        "process",
        "link",
        "rhombus",
        "offPageConnector",
        "loopLimit",
        "hexagon",
        "manualInput",
        "singleArrow",
        "doubleArrow",
        "flexArrow",
        "filledEdge",
        "card",
        "umlLifeline",
        "connector",
        "folder",
        "component",
        "sortShape",
        "cross",
        "umlFrame",
        "cube",
        "isoCube",
        "isoRectangle",
        "partialRectangle",
    ], shape) >= 0);
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isAutoSizeState = function (state) {
    return mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_AUTOSIZE, null) == "1";
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isImageState = function (state) {
    var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
    return shape == "label" || shape == "image";
};
/**
 * Returns information about the current selection.
 */
Format.prototype.isShadowState = function (state) {
    var shape = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
    return shape != "image";
};
/**
 * Adds the label menu items to the given menu and parent.
 */
Format.prototype.clear = function () {
    this.container.innerHTML = "";
    // Destroy existing panels
    if (this.panels != null) {
        for (var i = 0; i < this.panels.length; i++) {
            this.panels[i].destroy();
        }
    }
    this.panels = [];
};
/**
 * Adds the label menu items to the given menu and parent.
 */
Format.prototype.refresh = function () {
    // Performance tweak: No refresh needed if not visible
    if (this.container.style.width == "0px") {
        return;
    }
    this.clear();
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var div = document.createElement("div");
    div.style.whiteSpace = "nowrap";
    div.style.color = "rgb(112, 112, 112)";
    div.style.textAlign = "left";
    div.style.cursor = "default";
    var label = document.createElement("div");
    label.className = "geFormatSection";
    label.style.textAlign = "center";
    label.style.fontWeight = "bold";
    label.style.paddingTop = "8px";
    label.style.fontSize = "13px";
    label.style.borderWidth = "0px 0px 1px 1px";
    label.style.borderStyle = "solid";
    label.style.display = mxgraph_1.mxClient.IS_QUIRKS ? "inline" : "inline-block";
    label.style.height = mxgraph_1.mxClient.IS_QUIRKS ? "34px" : "25px";
    label.style.overflow = "hidden";
    label.style.width = "100%";
    this.container.appendChild(div);
    // Prevents text selection
    mxgraph_1.mxEvent.addListener(label, mxgraph_1.mxClient.IS_POINTER ? "pointerdown" : "mousedown", mxgraph_1.mxUtils.bind(this, function (evt) {
        evt.preventDefault();
    }));
    if (graph.isSelectionEmpty()) {
        mxgraph_1.mxUtils.write(label, mxgraph_1.mxResources.get("diagram"));
        label.style.borderLeftWidth = "0px";
        // Adds button to hide the format panel since
        // people don't seem to find the toolbar button
        // and the menu item in the format menu
        if (this.showCloseButton) {
            var img = document.createElement("img");
            img.setAttribute("border", "0");
            img.setAttribute("src", Editor_1.Dialog.prototype.closeImage);
            img.setAttribute("title", mxgraph_1.mxResources.get("hide"));
            img.style.position = "absolute";
            img.style.display = "block";
            img.style.right = "0px";
            img.style.top = "8px";
            img.style.cursor = "pointer";
            img.style.marginTop = "1px";
            img.style.marginRight = "17px";
            img.style.border = "1px solid transparent";
            img.style.padding = "1px";
            img.style.opacity = 0.5;
            label.appendChild(img);
            mxgraph_1.mxEvent.addListener(img, "click", function () {
                ui.actions.get("formatPanel").funct();
            });
        }
        div.appendChild(label);
        this.panels.push(new DiagramFormatPanel(this, ui, div));
    }
    else if (graph.isEditing()) {
        mxgraph_1.mxUtils.write(label, mxgraph_1.mxResources.get("text"));
        div.appendChild(label);
        this.panels.push(new TextFormatPanel(this, ui, div));
    }
    else {
        var containsLabel_1 = this.getSelectionState().containsLabel;
        var currentLabel_1 = null;
        var currentPanel_1 = null;
        var addClickHandler = mxgraph_1.mxUtils.bind(this, function (elt, panel, index) {
            var clickHandler = mxgraph_1.mxUtils.bind(this, function () {
                if (currentLabel_1 != elt) {
                    if (containsLabel_1) {
                        this.labelIndex = index;
                    }
                    else {
                        this.currentIndex = index;
                    }
                    if (currentLabel_1 != null) {
                        currentLabel_1.style.backgroundColor = this.inactiveTabBackgroundColor;
                        currentLabel_1.style.borderBottomWidth = "1px";
                    }
                    currentLabel_1 = elt;
                    currentLabel_1.style.backgroundColor = "";
                    currentLabel_1.style.borderBottomWidth = "0px";
                    if (currentPanel_1 != panel) {
                        if (currentPanel_1 != null) {
                            currentPanel_1.style.display = "none";
                        }
                        currentPanel_1 = panel;
                        currentPanel_1.style.display = "";
                    }
                }
            });
            mxgraph_1.mxEvent.addListener(elt, "click", clickHandler);
            // Prevents text selection
            mxgraph_1.mxEvent.addListener(elt, mxgraph_1.mxClient.IS_POINTER ? "pointerdown" : "mousedown", mxgraph_1.mxUtils.bind(this, function (evt) {
                evt.preventDefault();
            }));
            if (index == (containsLabel_1 ? this.labelIndex : this.currentIndex)) {
                // Invokes handler directly as a workaround for no click on DIV in KHTML.
                clickHandler();
            }
        });
        var idx = 0;
        label.style.backgroundColor = this.inactiveTabBackgroundColor;
        label.style.borderLeftWidth = "1px";
        label.style.cursor = "pointer";
        label.style.width = containsLabel_1 ? "50%" : "33.3%";
        label.style.width = containsLabel_1 ? "50%" : "33.3%";
        var label2 = label.cloneNode(false);
        var label3 = label2.cloneNode(false);
        // Workaround for ignored background in IE
        label2.style.backgroundColor = this.inactiveTabBackgroundColor;
        label3.style.backgroundColor = this.inactiveTabBackgroundColor;
        // Style
        if (containsLabel_1) {
            label2.style.borderLeftWidth = "0px";
        }
        else {
            label.style.borderLeftWidth = "0px";
            mxgraph_1.mxUtils.write(label, mxgraph_1.mxResources.get("style"));
            div.appendChild(label);
            var stylePanel = div.cloneNode(false);
            stylePanel.style.display = "none";
            this.panels.push(new StyleFormatPanel(this, ui, stylePanel));
            this.container.appendChild(stylePanel);
            addClickHandler(label, stylePanel, idx++);
        }
        // Text
        mxgraph_1.mxUtils.write(label2, mxgraph_1.mxResources.get("text"));
        div.appendChild(label2);
        var textPanel = div.cloneNode(false);
        textPanel.style.display = "none";
        this.panels.push(new TextFormatPanel(this, ui, textPanel));
        this.container.appendChild(textPanel);
        // Arrange
        mxgraph_1.mxUtils.write(label3, mxgraph_1.mxResources.get("arrange"));
        div.appendChild(label3);
        var arrangePanel = div.cloneNode(false);
        arrangePanel.style.display = "none";
        this.panels.push(new ArrangePanel(this, ui, arrangePanel));
        this.container.appendChild(arrangePanel);
        addClickHandler(label2, textPanel, idx++);
        addClickHandler(label3, arrangePanel, idx++);
    }
};
/**
 * Base class for format panels.
 */
function BaseFormatPanel(format, editorUi, container) {
    this.format = format;
    this.editorUi = editorUi;
    this.container = container;
    this.listeners = [];
}
exports.BaseFormatPanel = BaseFormatPanel;
/**
 *
 */
BaseFormatPanel.prototype.buttonBackgroundColor = "white";
/**
 * Adds the given color option.
 */
BaseFormatPanel.prototype.getSelectionState = function () {
    var graph = this.editorUi.editor.graph;
    var cells = graph.getSelectionCells();
    var shape = null;
    for (var i = 0; i < cells.length; i++) {
        var state = graph.view.getState(cells[i]);
        if (state != null) {
            var tmp = mxgraph_1.mxUtils.getValue(state.style, mxgraph_1.mxConstants.STYLE_SHAPE, null);
            if (tmp != null) {
                if (shape == null) {
                    shape = tmp;
                }
                else if (shape != tmp) {
                    return null;
                }
            }
        }
    }
    return shape;
};
/**
 * Install input handler.
 */
BaseFormatPanel.prototype.installInputHandler = function (input, key, defaultValue, min, max, unit, textEditFallback, isFloat) {
    unit = unit != null ? unit : "";
    isFloat = isFloat != null ? isFloat : false;
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    min = min != null ? min : 1;
    max = max != null ? max : 999;
    var selState = null;
    var updating = false;
    var update = mxgraph_1.mxUtils.bind(this, function (evt) {
        var value = isFloat ? parseFloat(input.value) : parseInt(input.value);
        // Special case: angle mod 360
        if (!isNaN(value) && key == mxgraph_1.mxConstants.STYLE_ROTATION) {
            // Workaround for decimal rounding errors in floats is to
            // use integer and round all numbers to two decimal point
            value = mxgraph_1.mxUtils.mod(Math.round(value * 100), 36000) / 100;
        }
        value = Math.min(max, Math.max(min, isNaN(value) ? defaultValue : value));
        if (graph.cellEditor.isContentEditing() && textEditFallback) {
            if (!updating) {
                updating = true;
                if (selState != null) {
                    graph.cellEditor.restoreSelection(selState);
                    selState = null;
                }
                textEditFallback(value);
                input.value = value + unit;
                // Restore focus and selection in input
                updating = false;
            }
        }
        else if (value != mxgraph_1.mxUtils.getValue(this.format.getSelectionState().style, key, defaultValue)) {
            if (graph.isEditing()) {
                graph.stopEditing(true);
            }
            graph.getModel().beginUpdate();
            try {
                var cells = graph.getSelectionCells();
                graph.setCellStyles(key, value, cells);
                // Handles special case for fontSize where HTML labels are parsed and updated
                if (key == mxgraph_1.mxConstants.STYLE_FONTSIZE) {
                    graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                        elt.style.fontSize = value + "px";
                        elt.removeAttribute("size");
                    });
                }
                for (var i = 0; i < cells.length; i++) {
                    if (graph.model.getChildCount(cells[i]) == 0) {
                        graph.autoSizeCell(cells[i], false);
                    }
                }
                ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [key], "values", [value], "cells", cells));
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
        input.value = value + unit;
        mxgraph_1.mxEvent.consume(evt);
    });
    if (textEditFallback && graph.cellEditor.isContentEditing()) {
        // KNOWN: Arrow up/down clear selection text in quirks/IE 8
        // Text size via arrow button limits to 16 in IE11. Why?
        mxgraph_1.mxEvent.addListener(input, "mousedown", function () {
            if (document.activeElement == graph.cellEditor.textarea) {
                selState = graph.cellEditor.saveSelection();
            }
        });
        mxgraph_1.mxEvent.addListener(input, "touchstart", function () {
            if (document.activeElement == graph.cellEditor.textarea) {
                selState = graph.cellEditor.saveSelection();
            }
        });
    }
    mxgraph_1.mxEvent.addListener(input, "change", update);
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    return update;
};
/**
 * Adds the given option.
 */
BaseFormatPanel.prototype.createPanel = function () {
    var div = document.createElement("div");
    div.className = "geFormatSection";
    div.style.padding = "12px 0px 12px 18px";
    return div;
};
/**
 * Adds the given option.
 */
BaseFormatPanel.prototype.createTitle = function (title) {
    var div = document.createElement("div");
    div.style.padding = "0px 0px 6px 0px";
    div.style.whiteSpace = "nowrap";
    div.style.overflow = "hidden";
    div.style.width = "200px";
    div.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(div, title);
    return div;
};
/**
 *
 */
BaseFormatPanel.prototype.createStepper = function (input, update, step, height, disableFocus, defaultValue, isFloat) {
    step = step != null ? step : 1;
    height = height != null ? height : 8;
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        height = height - 2;
    }
    else if (mxgraph_1.mxClient.IS_MT || document.documentMode >= 8) {
        height = height + 1;
    }
    var stepper = document.createElement("div");
    mxgraph_1.mxUtils.setPrefixedStyle(stepper.style, "borderRadius", "3px");
    stepper.style.border = "1px solid rgb(192, 192, 192)";
    stepper.style.position = "absolute";
    var up = document.createElement("div");
    up.style.borderBottom = "1px solid rgb(192, 192, 192)";
    up.style.position = "relative";
    up.style.height = height + "px";
    up.style.width = "10px";
    up.className = "geBtnUp";
    stepper.appendChild(up);
    var down = up.cloneNode(false);
    down.style.border = "none";
    down.style.height = height + "px";
    down.className = "geBtnDown";
    stepper.appendChild(down);
    mxgraph_1.mxEvent.addListener(down, "click", function (evt) {
        if (input.value == "") {
            input.value = defaultValue || "2";
        }
        var val = isFloat ? parseFloat(input.value) : parseInt(input.value);
        if (!isNaN(val)) {
            input.value = val - step;
            if (update != null) {
                update(evt);
            }
        }
        mxgraph_1.mxEvent.consume(evt);
    });
    mxgraph_1.mxEvent.addListener(up, "click", function (evt) {
        if (input.value == "") {
            input.value = defaultValue || "0";
        }
        var val = isFloat ? parseFloat(input.value) : parseInt(input.value);
        if (!isNaN(val)) {
            input.value = val + step;
            if (update != null) {
                update(evt);
            }
        }
        mxgraph_1.mxEvent.consume(evt);
    });
    // Disables transfer of focus to DIV but also :active CSS
    // so it's only used for fontSize where the focus should
    // stay on the selected text, but not for any other input.
    if (disableFocus) {
        var currentSelection_1 = null;
        mxgraph_1.mxEvent.addGestureListeners(stepper, function (evt) {
            // Workaround for lost current selection in page because of focus in IE
            if (mxgraph_1.mxClient.IS_QUIRKS || document.documentMode == 8) {
                currentSelection_1 = document.selection.createRange();
            }
            mxgraph_1.mxEvent.consume(evt);
        }, null, function (evt) {
            // Workaround for lost current selection in page because of focus in IE
            if (currentSelection_1 != null) {
                try {
                    currentSelection_1.select();
                }
                catch (e) {
                    // ignore
                }
                currentSelection_1 = null;
                mxgraph_1.mxEvent.consume(evt);
            }
        });
    }
    return stepper;
};
/**
 * Adds the given option.
 */
BaseFormatPanel.prototype.createOption = function (label, isCheckedFn, setCheckedFn, listener) {
    var div = document.createElement("div");
    div.style.padding = "6px 0px 1px 0px";
    div.style.whiteSpace = "nowrap";
    div.style.overflow = "hidden";
    div.style.width = "200px";
    div.style.height = mxgraph_1.mxClient.IS_QUIRKS ? "27px" : "18px";
    div.className = "form-options-line";
    var cb = document.createElement("input");
    cb.setAttribute("type", "checkbox");
    cb.style.margin = "0px 6px 0px 0px";
    div.appendChild(cb);
    var span = document.createElement("span");
    mxgraph_1.mxUtils.write(span, label);
    div.appendChild(span);
    var applying = false;
    var value = isCheckedFn();
    var apply = function (newValue) {
        if (!applying) {
            applying = true;
            if (newValue) {
                cb.setAttribute("checked", "checked");
                cb.defaultChecked = true;
                cb.checked = true;
            }
            else {
                cb.removeAttribute("checked");
                cb.defaultChecked = false;
                cb.checked = false;
            }
            if (value != newValue) {
                value = newValue;
                // Checks if the color value needs to be updated in the model
                if (isCheckedFn() != value) {
                    setCheckedFn(value);
                }
            }
            applying = false;
        }
    };
    mxgraph_1.mxEvent.addListener(div, "click", function (evt) {
        if (cb.getAttribute("disabled") != "disabled") {
            // Toggles checkbox state for click on label
            var source = mxgraph_1.mxEvent.getSource(evt);
            if (source == div || source == span) {
                cb.checked = !cb.checked;
            }
            apply(cb.checked);
        }
    });
    apply(value);
    if (listener != null) {
        listener.install(apply);
        this.listeners.push(listener);
    }
    return div;
};
/**
 * The string 'null' means use null in values.
 */
BaseFormatPanel.prototype.createCellOption = function (label, key, defaultValue, enabledValue, disabledValue, fn, action, stopEditing) {
    enabledValue = enabledValue != null ? (enabledValue == "null" ? null : enabledValue) : "1";
    disabledValue = disabledValue != null ? (disabledValue == "null" ? null : disabledValue) : "0";
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    return this.createOption(label, function () {
        // Seems to be null sometimes, not sure why...
        var state = graph.view.getState(graph.getSelectionCell());
        if (state != null) {
            return mxgraph_1.mxUtils.getValue(state.style, key, defaultValue) != disabledValue;
        }
        return null;
    }, function (checked) {
        if (stopEditing) {
            graph.stopEditing();
        }
        if (action != null) {
            action.funct();
        }
        else {
            graph.getModel().beginUpdate();
            try {
                var value = checked ? enabledValue : disabledValue;
                graph.setCellStyles(key, value, graph.getSelectionCells());
                if (fn != null) {
                    fn(graph.getSelectionCells(), value);
                }
                ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [key], "values", [value], "cells", graph.getSelectionCells()));
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    }, {
        install: function (apply) {
            this.listener = function () {
                // Seems to be null sometimes, not sure why...
                var state = graph.view.getState(graph.getSelectionCell());
                if (state != null) {
                    apply(mxgraph_1.mxUtils.getValue(state.style, key, defaultValue) != disabledValue);
                }
            };
            graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, this.listener);
        },
        destroy: function () {
            graph.getModel().removeListener(this.listener);
        },
    });
};
/**
 * Adds the given color option.
 */
BaseFormatPanel.prototype.createColorOption = function (label, getColorFn, setColorFn, defaultColor, listener, callbackFn, hideCheckbox) {
    var div = document.createElement("div");
    div.style.padding = "6px 0px 1px 0px";
    div.style.whiteSpace = "nowrap";
    div.style.overflow = "hidden";
    div.style.width = "200px";
    div.style.height = mxgraph_1.mxClient.IS_QUIRKS ? "27px" : "18px";
    var cb = document.createElement("input");
    cb.setAttribute("type", "checkbox");
    cb.style.margin = "0px 6px 0px 0px";
    div.className = "form-options-line";
    if (!hideCheckbox) {
        div.appendChild(cb);
    }
    var span = document.createElement("span");
    mxgraph_1.mxUtils.write(span, label);
    div.appendChild(span);
    var value = getColorFn();
    var applying = false;
    var btn = null;
    var apply = function (color, disableUpdate, forceUpdate) {
        if (!applying) {
            applying = true;
            color = /(^#?[a-zA-Z0-9]*$)/.test(color) ? color : defaultColor;
            btn.innerHTML =
                '<div style="width:' +
                    (mxgraph_1.mxClient.IS_QUIRKS ? "30" : "36") +
                    "px;height:12px;margin:3px;border:1px solid black;background-color:" +
                    mxgraph_1.mxUtils.htmlEntities(color != null && color != mxgraph_1.mxConstants.NONE ? color : defaultColor) +
                    ';"></div>';
            // Fine-tuning in Firefox, quirks mode and IE8 standards
            if (mxgraph_1.mxClient.IS_QUIRKS || document.documentMode == 8) {
                btn.firstChild.style.margin = "0px";
            }
            if (color != null && color != mxgraph_1.mxConstants.NONE) {
                cb.setAttribute("checked", "checked");
                cb.defaultChecked = true;
                cb.checked = true;
            }
            else {
                cb.removeAttribute("checked");
                cb.defaultChecked = false;
                cb.checked = false;
            }
            btn.style.display = cb.checked || hideCheckbox ? "" : "none";
            if (callbackFn != null) {
                callbackFn(color);
            }
            if (!disableUpdate) {
                value = color;
                // Checks if the color value needs to be updated in the model
                if (forceUpdate || hideCheckbox || getColorFn() != value) {
                    setColorFn(value);
                }
            }
            applying = false;
        }
    };
    btn = mxgraph_1.mxUtils.button("", mxgraph_1.mxUtils.bind(this, function (evt) {
        this.editorUi.pickColor(value, function (color) {
            apply(color, null, true);
        });
        mxgraph_1.mxEvent.consume(evt);
    }));
    btn.style.position = "absolute";
    btn.style.marginTop = "-4px";
    btn.style.right = mxgraph_1.mxClient.IS_QUIRKS ? "0px" : "20px";
    btn.style.height = "22px";
    btn.className = "geColorBtn";
    btn.style.display = cb.checked || hideCheckbox ? "" : "none";
    div.appendChild(btn);
    mxgraph_1.mxEvent.addListener(div, "click", function (evt) {
        var source = mxgraph_1.mxEvent.getSource(evt);
        if (source == cb || source.nodeName != "INPUT") {
            // Toggles checkbox state for click on label
            if (source != cb) {
                cb.checked = !cb.checked;
            }
            // Overrides default value with current value to make it easier
            // to restore previous value if the checkbox is clicked twice
            if (!cb.checked &&
                value != null &&
                value != mxgraph_1.mxConstants.NONE &&
                defaultColor != mxgraph_1.mxConstants.NONE) {
                defaultColor = value;
            }
            apply(cb.checked ? defaultColor : mxgraph_1.mxConstants.NONE);
        }
    });
    apply(value, true);
    if (listener != null) {
        listener.install(apply);
        this.listeners.push(listener);
    }
    return div;
};
/**
 *
 */
BaseFormatPanel.prototype.createCellColorOption = function (label, colorKey, defaultColor, callbackFn, setStyleFn) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    return this.createColorOption(label, function () {
        // Seems to be null sometimes, not sure why...
        var state = graph.view.getState(graph.getSelectionCell());
        if (state != null) {
            return mxgraph_1.mxUtils.getValue(state.style, colorKey, null);
        }
        return null;
    }, function (color) {
        graph.getModel().beginUpdate();
        try {
            if (setStyleFn != null) {
                setStyleFn(color);
            }
            graph.setCellStyles(colorKey, color, graph.getSelectionCells());
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [colorKey], "values", [color], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    }, defaultColor || mxgraph_1.mxConstants.NONE, {
        install: function (apply) {
            this.listener = function () {
                // Seems to be null sometimes, not sure why...
                var state = graph.view.getState(graph.getSelectionCell());
                if (state != null) {
                    apply(mxgraph_1.mxUtils.getValue(state.style, colorKey, null));
                }
            };
            graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, this.listener);
        },
        destroy: function () {
            graph.getModel().removeListener(this.listener);
        },
    }, callbackFn);
};
/**
 *
 */
BaseFormatPanel.prototype.addArrow = function (elt, height) {
    height = height != null ? height : 10;
    var arrow = document.createElement("div");
    arrow.style.display = mxgraph_1.mxClient.IS_QUIRKS ? "inline" : "inline-block";
    arrow.style.padding = "6px";
    arrow.style.paddingRight = "4px";
    var m = 10 - height;
    if (m == 2) {
        arrow.style.paddingTop = 6 + "px";
    }
    else if (m > 0) {
        arrow.style.paddingTop = 6 - m + "px";
    }
    else {
        arrow.style.marginTop = "-2px";
    }
    arrow.style.height = height + "px";
    arrow.style.borderLeft = "1px solid #a0a0a0";
    arrow.innerHTML = '<img border="0" src="' + base64_1.arrowSVG + '" style="margin-bottom:4px;">';
    mxgraph_1.mxUtils.setOpacity(arrow, 70);
    var symbol = elt.getElementsByTagName("div")[0];
    if (symbol != null) {
        symbol.style.paddingRight = "6px";
        symbol.style.marginLeft = "4px";
        symbol.style.marginTop = "-1px";
        symbol.style.display = mxgraph_1.mxClient.IS_QUIRKS ? "inline" : "inline-block";
        mxgraph_1.mxUtils.setOpacity(symbol, 60);
    }
    mxgraph_1.mxUtils.setOpacity(elt, 100);
    elt.style.border = "1px solid #a0a0a0";
    elt.style.backgroundColor = this.buttonBackgroundColor;
    elt.style.backgroundImage = "none";
    elt.style.width = "auto";
    elt.className += " geColorBtn";
    mxgraph_1.mxUtils.setPrefixedStyle(elt.style, "borderRadius", "3px");
    elt.appendChild(arrow);
    return symbol;
};
/**
 *
 */
BaseFormatPanel.prototype.addUnitInput = function (container, unit, right, width, update, step, marginTop, disableFocus, isFloat) {
    marginTop = marginTop != null ? marginTop : 0;
    var input = document.createElement("input");
    input.style.position = "absolute";
    input.style.textAlign = "right";
    input.style.marginTop = "-2px";
    input.style.right = right + 12 + "px";
    input.style.width = width + "px";
    container.appendChild(input);
    var stepper = this.createStepper(input, update, step, null, disableFocus, null, isFloat);
    stepper.style.marginTop = marginTop - 2 + "px";
    stepper.style.right = right + "px";
    container.appendChild(stepper);
    return input;
};
/**
 *
 */
BaseFormatPanel.prototype.createRelativeOption = function (label, key, width, handler, init) {
    width = width != null ? width : 44;
    var graph = this.editorUi.editor.graph;
    var div = this.createPanel();
    div.style.paddingTop = "10px";
    div.style.paddingBottom = "10px";
    mxgraph_1.mxUtils.write(div, label);
    div.style.fontWeight = "bold";
    var update = mxgraph_1.mxUtils.bind(this, function (evt) {
        if (handler != null) {
            handler(input);
        }
        else {
            var value = parseInt(input.value);
            value = Math.min(100, Math.max(0, isNaN(value) ? 100 : value));
            var state = graph.view.getState(graph.getSelectionCell());
            if (state != null && value != mxgraph_1.mxUtils.getValue(state.style, key, 100)) {
                // Removes entry in style (assumes 100 is default for relative values)
                if (value == 100) {
                    value = null;
                }
                graph.setCellStyles(key, value, graph.getSelectionCells());
                this.editorUi.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [key], "values", [value], "cells", graph.getSelectionCells()));
            }
            input.value = (value != null ? value : "100") + " %";
        }
        mxgraph_1.mxEvent.consume(evt);
    });
    var input = this.addUnitInput(div, "%", 20, width, update, 10, -15, handler != null);
    if (key != null) {
        var listener_1 = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
            if (force || input != document.activeElement) {
                var ss = this.format.getSelectionState();
                var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, key, 100));
                input.value = isNaN(tmp) ? "" : tmp + " %";
            }
        });
        mxgraph_1.mxEvent.addListener(input, "keydown", function (e) {
            if (e.keyCode == 13) {
                graph.container.focus();
                mxgraph_1.mxEvent.consume(e);
            }
            else if (e.keyCode == 27) {
                listener_1(null, null, true);
                graph.container.focus();
                mxgraph_1.mxEvent.consume(e);
            }
        });
        graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener_1);
        this.listeners.push({
            destroy: function () {
                graph.getModel().removeListener(listener_1);
            },
        });
        listener_1();
    }
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    mxgraph_1.mxEvent.addListener(input, "change", update);
    if (init != null) {
        init(input);
    }
    return div;
};
/**
 *
 */
BaseFormatPanel.prototype.addLabel = function (div, title, right, width) {
    width = width != null ? width : 61;
    var label = document.createElement("div");
    mxgraph_1.mxUtils.write(label, title);
    label.style.position = "absolute";
    label.style.right = right + "px";
    label.style.width = width + "px";
    label.style.marginTop = "6px";
    label.style.textAlign = "center";
    div.appendChild(label);
};
/**
 *
 */
BaseFormatPanel.prototype.addKeyHandler = function (input, listener) {
    mxgraph_1.mxEvent.addListener(input, "keydown", mxgraph_1.mxUtils.bind(this, function (e) {
        if (e.keyCode == 13) {
            this.editorUi.editor.graph.container.focus();
            mxgraph_1.mxEvent.consume(e);
        }
        else if (e.keyCode == 27) {
            if (listener != null) {
                listener(null, null, true);
            }
            this.editorUi.editor.graph.container.focus();
            mxgraph_1.mxEvent.consume(e);
        }
    }));
};
/**
 *
 */
BaseFormatPanel.prototype.styleButtons = function (elts) {
    for (var i = 0; i < elts.length; i++) {
        if (!elts[i])
            continue;
        mxgraph_1.mxUtils.setPrefixedStyle(elts[i].style, "borderRadius", "3px");
        mxgraph_1.mxUtils.setOpacity(elts[i], 100);
        elts[i].style.border = "1px solid #a0a0a0";
        elts[i].style.padding = "4px";
        elts[i].style.paddingTop = "3px";
        elts[i].style.paddingRight = "1px";
        elts[i].style.margin = "1px";
        elts[i].style.width = "24px";
        elts[i].style.height = "20px";
        elts[i].className += " geColorBtn";
    }
};
/**
 * Adds the label menu items to the given menu and parent.
 */
BaseFormatPanel.prototype.destroy = function () {
    if (this.listeners != null) {
        for (var i = 0; i < this.listeners.length; i++) {
            this.listeners[i].destroy();
        }
        this.listeners = null;
    }
};
/**
 * Adds the label menu items to the given menu and parent.
 */
function ArrangePanel(format, editorUi, container) {
    BaseFormatPanel.call(this, format, editorUi, container);
    this.init();
}
exports.ArrangePanel = ArrangePanel;
mxgraph_1.mxUtils.extend(ArrangePanel, BaseFormatPanel);
/**
 * Adds the label menu items to the given menu and parent.
 */
ArrangePanel.prototype.init = function () {
    var graph = this.editorUi.editor.graph;
    var ss = this.format.getSelectionState();
    this.container.appendChild(this.addLayerOps(this.createPanel()));
    // Special case that adds two panels
    this.addGeometry(this.container);
    this.addEdgeGeometry(this.container);
    if (!ss.containsLabel || ss.edges.length == 0) {
        this.container.appendChild(this.addAngle(this.createPanel()));
    }
    if (!ss.containsLabel &&
        ss.edges.length == 0 &&
        ss.style.shape != "rectangle" &&
        ss.style.shape != "label") {
        this.container.appendChild(this.addFlip(this.createPanel()));
    }
    if (ss.vertices.length > 1) {
        this.container.appendChild(this.addAlign(this.createPanel()));
        this.container.appendChild(this.addDistribute(this.createPanel()));
    }
    if (graph.isTable(ss.vertices[0]) ||
        graph.isTableRow(ss.vertices[0]) ||
        graph.isTableCell(ss.vertices[0])) {
        this.container.appendChild(this.addTable(this.createPanel()));
    }
    this.container.appendChild(this.addGroupOps(this.createPanel()));
    if (ss.containsLabel) {
        // Adds functions from hidden style format panel
        var span = document.createElement("div");
        span.style.width = "100%";
        span.style.marginTop = "0px";
        span.style.fontWeight = "bold";
        span.style.padding = "10px 0 0 18px";
        mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("style"));
        this.container.appendChild(span);
        new StyleFormatPanel(this.format, this.editorUi, this.container);
    }
};
/**
 *
 */
ArrangePanel.prototype.addTable = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    var ss = this.format.getSelectionState();
    div.style.paddingTop = "6px";
    div.style.paddingBottom = "10px";
    var span = document.createElement("div");
    span.style.marginTop = "2px";
    span.style.marginBottom = "8px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("table"));
    div.appendChild(span);
    var panel = document.createElement("div");
    panel.style.position = "relative";
    panel.style.paddingLeft = "0px";
    panel.style.borderWidth = "0px";
    panel.className = "geToolbarContainer";
    var btns = [
        ui.toolbar.addButton("geSprite-insertcolumnbefore", mxgraph_1.mxResources.get("insertColumnBefore"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.insertTableColumn(ss.vertices[0], true);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
        ui.toolbar.addButton("geSprite-insertcolumnafter", mxgraph_1.mxResources.get("insertColumnAfter"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.insertTableColumn(ss.vertices[0], false);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
        ui.toolbar.addButton("geSprite-deletecolumn", mxgraph_1.mxResources.get("deleteColumn"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.deleteTableColumn(ss.vertices[0]);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
        ui.toolbar.addButton("geSprite-insertrowbefore", mxgraph_1.mxResources.get("insertRowBefore"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.insertTableRow(ss.vertices[0], true);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
        ui.toolbar.addButton("geSprite-insertrowafter", mxgraph_1.mxResources.get("insertRowAfter"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.insertTableRow(ss.vertices[0], false);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
        ui.toolbar.addButton("geSprite-deleterow", mxgraph_1.mxResources.get("deleteRow"), mxgraph_1.mxUtils.bind(this, function () {
            try {
                graph.deleteTableRow(ss.vertices[0]);
            }
            catch (e) {
                ui.handleError(e);
            }
        }), panel),
    ];
    this.styleButtons(btns);
    div.appendChild(panel);
    btns[2].style.marginRight = "9px";
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addLayerOps = function (div) {
    var ui = this.editorUi;
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("toFront"), function () {
        ui.actions.get("toFront").funct();
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("toFront") + " (" + this.editorUi.actions.get("toFront").shortcut + ")");
    btn.style.width = "100px";
    btn.style.marginRight = "2px";
    div.appendChild(btn);
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("toBack"), function () {
        ui.actions.get("toBack").funct();
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("toBack") + " (" + this.editorUi.actions.get("toBack").shortcut + ")");
    btn.style.width = "100px";
    div.appendChild(btn);
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addGroupOps = function (div) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var cell = graph.getSelectionCell();
    var ss = this.format.getSelectionState();
    var count = 0;
    var btn = null;
    div.style.paddingTop = "8px";
    div.style.paddingBottom = "6px";
    if (graph.getSelectionCount() > 1) {
        btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("group"), function () {
            ui.actions.get("group").funct();
        });
        btn.setAttribute("title", mxgraph_1.mxResources.get("group") + " (" + this.editorUi.actions.get("group").shortcut + ")");
        btn.style.width = "202px";
        btn.style.marginBottom = "2px";
        div.appendChild(btn);
        count++;
    }
    else if (graph.getSelectionCount() == 1 &&
        !graph.getModel().isEdge(cell) &&
        !graph.isSwimlane(cell) &&
        !graph.isTable(cell) &&
        !ss.row &&
        !ss.cell &&
        graph.getModel().getChildCount(cell) > 0) {
        btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("ungroup"), function () {
            ui.actions.get("ungroup").funct();
        });
        btn.setAttribute("title", mxgraph_1.mxResources.get("ungroup") + " (" + this.editorUi.actions.get("ungroup").shortcut + ")");
        btn.style.width = "202px";
        btn.style.marginBottom = "2px";
        div.appendChild(btn);
        count++;
    }
    if (ss.vertices.length > 0) {
        if (count > 0) {
            mxgraph_1.mxUtils.br(div);
            count = 0;
        }
        var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("copySize"), function () {
            ui.actions.get("copySize").funct();
        });
        btn.setAttribute("title", mxgraph_1.mxResources.get("copySize") + " (" + this.editorUi.actions.get("copySize").shortcut + ")");
        btn.style.width = "202px";
        btn.style.marginBottom = "2px";
        div.appendChild(btn);
        count++;
        if (ui.copiedSize != null) {
            var btn2 = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("pasteSize"), function () {
                ui.actions.get("pasteSize").funct();
            });
            btn2.setAttribute("title", mxgraph_1.mxResources.get("pasteSize") + " (" + this.editorUi.actions.get("pasteSize").shortcut + ")");
            div.appendChild(btn2);
            count++;
            btn.style.width = "100px";
            btn.style.marginBottom = "2px";
            btn2.style.width = "100px";
            btn2.style.marginBottom = "2px";
        }
    }
    if (graph.getSelectionCount() == 1 &&
        graph.getModel().isVertex(cell) &&
        !ss.row &&
        !ss.cell &&
        graph.getModel().isVertex(graph.getModel().getParent(cell))) {
        if (count > 0) {
            mxgraph_1.mxUtils.br(div);
        }
        btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("removeFromGroup"), function () {
            ui.actions.get("removeFromGroup").funct();
        });
        btn.setAttribute("title", mxgraph_1.mxResources.get("removeFromGroup"));
        btn.style.width = "202px";
        btn.style.marginBottom = "2px";
        div.appendChild(btn);
        count++;
    }
    else if (graph.getSelectionCount() > 0) {
        if (count > 0) {
            mxgraph_1.mxUtils.br(div);
        }
        btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("clearWaypoints"), mxgraph_1.mxUtils.bind(this, function () {
            this.editorUi.actions.get("clearWaypoints").funct();
        }));
        btn.setAttribute("title", mxgraph_1.mxResources.get("clearWaypoints") +
            " (" +
            this.editorUi.actions.get("clearWaypoints").shortcut +
            ")");
        btn.style.width = "202px";
        btn.style.marginBottom = "2px";
        div.appendChild(btn);
        count++;
    }
    if (graph.getSelectionCount() == 1) {
        if (count > 0) {
            mxgraph_1.mxUtils.br(div);
        }
        // - Edit Image Button
        // - Edit Link Button
    }
    if (count == 0) {
        div.style.display = "none";
    }
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addAlign = function (div) {
    var graph = this.editorUi.editor.graph;
    div.style.paddingTop = "6px";
    div.style.paddingBottom = "12px";
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("align")));
    var stylePanel = document.createElement("div");
    stylePanel.style.position = "relative";
    stylePanel.style.paddingLeft = "0px";
    stylePanel.style.borderWidth = "0px";
    stylePanel.className = "geToolbarContainer";
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        div.style.height = "60px";
    }
    var left = this.editorUi.toolbar.addButton("geSprite-alignleft", mxgraph_1.mxResources.get("left"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_LEFT);
    }, stylePanel);
    var center = this.editorUi.toolbar.addButton("geSprite-aligncenter", mxgraph_1.mxResources.get("center"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_CENTER);
    }, stylePanel);
    var right = this.editorUi.toolbar.addButton("geSprite-alignright", mxgraph_1.mxResources.get("right"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_RIGHT);
    }, stylePanel);
    var top = this.editorUi.toolbar.addButton("geSprite-aligntop", mxgraph_1.mxResources.get("top"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_TOP);
    }, stylePanel);
    var middle = this.editorUi.toolbar.addButton("geSprite-alignmiddle", mxgraph_1.mxResources.get("middle"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_MIDDLE);
    }, stylePanel);
    var bottom = this.editorUi.toolbar.addButton("geSprite-alignbottom", mxgraph_1.mxResources.get("bottom"), function () {
        graph.alignCells(mxgraph_1.mxConstants.ALIGN_BOTTOM);
    }, stylePanel);
    this.styleButtons([left, center, right, top, middle, bottom]);
    right.style.marginRight = "6px";
    div.appendChild(stylePanel);
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addFlip = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    div.style.paddingTop = "6px";
    div.style.paddingBottom = "10px";
    var span = document.createElement("div");
    span.style.marginTop = "2px";
    span.style.marginBottom = "8px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("flip"));
    div.appendChild(span);
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("horizontal"), function () {
        graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_FLIPH, false);
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("horizontal"));
    btn.style.width = "100px";
    btn.style.marginRight = "2px";
    div.appendChild(btn);
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("vertical"), function () {
        graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_FLIPV, false);
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("vertical"));
    btn.style.width = "100px";
    div.appendChild(btn);
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addDistribute = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    div.style.paddingTop = "6px";
    div.style.paddingBottom = "12px";
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("distribute")));
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("horizontal"), function () {
        graph.distributeCells(true);
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("horizontal"));
    btn.style.width = "100px";
    btn.style.marginRight = "2px";
    div.appendChild(btn);
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("vertical"), function () {
        graph.distributeCells(false);
    });
    btn.setAttribute("title", mxgraph_1.mxResources.get("vertical"));
    btn.style.width = "100px";
    div.appendChild(btn);
    return div;
};
/**
 *
 */
ArrangePanel.prototype.addAngle = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    var ss = this.format.getSelectionState();
    div.style.paddingBottom = "8px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    var input = null;
    var update = null;
    var btn = null;
    if (ss.rotatable && !ss.table && !ss.row && !ss.cell) {
        mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("angle"));
        div.appendChild(span);
        input = this.addUnitInput(div, "°", 20, 44, function () {
            update.apply(this, arguments);
        });
        mxgraph_1.mxUtils.br(div);
        div.style.paddingTop = "10px";
    }
    else {
        div.style.paddingTop = "8px";
    }
    if (!ss.containsLabel) {
        var label = mxgraph_1.mxResources.get("reverse");
        if (ss.vertices.length > 0 && ss.edges.length > 0) {
            label = mxgraph_1.mxResources.get("turn") + " / " + label;
        }
        else if (ss.vertices.length > 0) {
            label = mxgraph_1.mxResources.get("turn");
        }
        btn = mxgraph_1.mxUtils.button(label, function (evt) {
            ui.actions.get("turn").funct(evt);
        });
        btn.setAttribute("title", label + " (" + this.editorUi.actions.get("turn").shortcut + ")");
        btn.style.width = "202px";
        div.appendChild(btn);
        if (input != null) {
            btn.style.marginTop = "8px";
        }
    }
    if (input != null) {
        var listener_2 = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
            if (force || document.activeElement != input) {
                ss = this.format.getSelectionState();
                var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ROTATION, 0));
                input.value = isNaN(tmp) ? "" : tmp + "°";
            }
        });
        update = this.installInputHandler(input, mxgraph_1.mxConstants.STYLE_ROTATION, 0, 0, 360, "°", null, true);
        this.addKeyHandler(input, listener_2);
        graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener_2);
        this.listeners.push({
            destroy: function () {
                graph.getModel().removeListener(listener_2);
            },
        });
        listener_2();
    }
    return div;
};
BaseFormatPanel.prototype.getUnit = function () {
    var unit = this.editorUi.editor.graph.view.unit;
    switch (unit) {
        case mxgraph_1.mxConstants.POINTS:
            return "pt";
        case mxgraph_1.mxConstants.INCHES:
            return '"';
        case mxgraph_1.mxConstants.MILLIMETERS:
            return "mm";
    }
};
BaseFormatPanel.prototype.inUnit = function (pixels) {
    return this.editorUi.editor.graph.view.formatUnitText(pixels);
};
BaseFormatPanel.prototype.fromUnit = function (value) {
    var unit = this.editorUi.editor.graph.view.unit;
    switch (unit) {
        case mxgraph_1.mxConstants.POINTS:
            return value;
        case mxgraph_1.mxConstants.INCHES:
            return value * mxgraph_1.mxConstants.PIXELS_PER_INCH;
        case mxgraph_1.mxConstants.MILLIMETERS:
            return value * mxgraph_1.mxConstants.PIXELS_PER_MM;
    }
};
BaseFormatPanel.prototype.isFloatUnit = function () {
    return this.editorUi.editor.graph.view.unit != mxgraph_1.mxConstants.POINTS;
};
BaseFormatPanel.prototype.getUnitStep = function () {
    var unit = this.editorUi.editor.graph.view.unit;
    switch (unit) {
        case mxgraph_1.mxConstants.POINTS:
            return 1;
        case mxgraph_1.mxConstants.INCHES:
            return 0.1;
        case mxgraph_1.mxConstants.MILLIMETERS:
            return 0.5;
    }
};
/**
 *
 */
ArrangePanel.prototype.addGeometry = function (container) {
    var panel = this;
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var rect = this.format.getSelectionState();
    var div = this.createPanel();
    div.style.paddingBottom = "8px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "50px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("size"));
    div.appendChild(span);
    var widthUpdate, heightUpdate, leftUpdate, topUpdate;
    var width = this.addUnitInput(div, this.getUnit(), 84, 44, function () {
        widthUpdate.apply(this, arguments);
    }, this.getUnitStep(), null, null, this.isFloatUnit());
    var height = this.addUnitInput(div, this.getUnit(), 20, 44, function () {
        heightUpdate.apply(this, arguments);
    }, this.getUnitStep(), null, null, this.isFloatUnit());
    var autosizeBtn = document.createElement("div");
    autosizeBtn.className = "geSprite geSprite-fit";
    autosizeBtn.setAttribute("title", mxgraph_1.mxResources.get("autosize") + " (" + this.editorUi.actions.get("autosize").shortcut + ")");
    autosizeBtn.style.position = "relative";
    autosizeBtn.style.cursor = "pointer";
    autosizeBtn.style.marginTop = "-3px";
    autosizeBtn.style.border = "0px";
    autosizeBtn.style.left = "52px";
    mxgraph_1.mxUtils.setOpacity(autosizeBtn, 50);
    mxgraph_1.mxEvent.addListener(autosizeBtn, "mouseenter", function () {
        mxgraph_1.mxUtils.setOpacity(autosizeBtn, 100);
    });
    mxgraph_1.mxEvent.addListener(autosizeBtn, "mouseleave", function () {
        mxgraph_1.mxUtils.setOpacity(autosizeBtn, 50);
    });
    mxgraph_1.mxEvent.addListener(autosizeBtn, "click", function () {
        ui.actions.get("autosize").funct();
    });
    div.appendChild(autosizeBtn);
    if (rect.row) {
        width.style.visibility = "hidden";
        width.nextSibling.style.visibility = "hidden";
    }
    else {
        this.addLabel(div, mxgraph_1.mxResources.get("width"), 84);
    }
    this.addLabel(div, mxgraph_1.mxResources.get("height"), 20);
    mxgraph_1.mxUtils.br(div);
    var wrapper = document.createElement("div");
    wrapper.style.paddingTop = "8px";
    wrapper.style.paddingRight = "20px";
    wrapper.style.whiteSpace = "nowrap";
    wrapper.style.textAlign = "right";
    var opt = this.createCellOption(mxgraph_1.mxResources.get("constrainProportions"), mxgraph_1.mxConstants.STYLE_ASPECT, null, "fixed", "null");
    opt.style.width = "100%";
    wrapper.appendChild(opt);
    if (!rect.cell && !rect.row) {
        div.appendChild(wrapper);
    }
    else {
        autosizeBtn.style.visibility = "hidden";
    }
    var constrainCheckbox = opt.getElementsByTagName("input")[0];
    this.addKeyHandler(width, listener);
    this.addKeyHandler(height, listener);
    widthUpdate = this.addGeometryHandler(width, function (geo, value, cell) {
        if (graph.isTableCell(cell)) {
            graph.setTableColumnWidth(cell, value - geo.width, true);
            // Blocks processing in caller
            return true;
        }
        else if (geo.width > 0) {
            var value = Math.max(1, panel.fromUnit(value));
            if (constrainCheckbox.checked) {
                geo.height = Math.round((geo.height * value * 100) / geo.width) / 100;
            }
            geo.width = value;
        }
    });
    heightUpdate = this.addGeometryHandler(height, function (geo, value, cell) {
        if (graph.isTableCell(cell)) {
            cell = graph.model.getParent(cell);
        }
        if (graph.isTableRow(cell)) {
            graph.setTableRowHeight(cell, value - geo.height);
            // Blocks processing in caller
            return true;
        }
        else if (geo.height > 0) {
            var value = Math.max(1, panel.fromUnit(value));
            if (constrainCheckbox.checked) {
                geo.width = Math.round((geo.width * value * 100) / geo.height) / 100;
            }
            geo.height = value;
        }
    });
    if (rect.resizable || rect.row || rect.cell) {
        container.appendChild(div);
    }
    var div2 = this.createPanel();
    div2.style.paddingBottom = "30px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("position"));
    div2.appendChild(span);
    var left = this.addUnitInput(div2, this.getUnit(), 84, 44, function () {
        leftUpdate.apply(this, arguments);
    }, this.getUnitStep(), null, null, this.isFloatUnit());
    var top = this.addUnitInput(div2, this.getUnit(), 20, 44, function () {
        topUpdate.apply(this, arguments);
    }, this.getUnitStep(), null, null, this.isFloatUnit());
    mxgraph_1.mxUtils.br(div2);
    this.addLabel(div2, mxgraph_1.mxResources.get("left"), 84);
    this.addLabel(div2, mxgraph_1.mxResources.get("top"), 20);
    var listener = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
        rect = this.format.getSelectionState();
        if (!rect.containsLabel &&
            rect.vertices.length == graph.getSelectionCount() &&
            rect.width != null &&
            rect.height != null) {
            div.style.display = "";
            if (force || document.activeElement != width) {
                width.value = this.inUnit(rect.width) + (rect.width == "" ? "" : " " + this.getUnit());
            }
            if (force || document.activeElement != height) {
                height.value = this.inUnit(rect.height) + (rect.height == "" ? "" : " " + this.getUnit());
            }
        }
        else {
            div.style.display = "none";
        }
        if (rect.vertices.length == graph.getSelectionCount() && rect.x != null && rect.y != null) {
            div2.style.display = "";
            if (force || document.activeElement != left) {
                left.value = this.inUnit(rect.x) + (rect.x == "" ? "" : " " + this.getUnit());
            }
            if (force || document.activeElement != top) {
                top.value = this.inUnit(rect.y) + (rect.y == "" ? "" : " " + this.getUnit());
            }
        }
        else {
            div2.style.display = "none";
        }
    });
    this.addKeyHandler(left, listener);
    this.addKeyHandler(top, listener);
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
    leftUpdate = this.addGeometryHandler(left, function (geo, value) {
        value = panel.fromUnit(value);
        if (geo.relative) {
            geo.offset.x = value;
        }
        else {
            geo.x = value;
        }
    });
    topUpdate = this.addGeometryHandler(top, function (geo, value) {
        value = panel.fromUnit(value);
        if (geo.relative) {
            geo.offset.y = value;
        }
        else {
            geo.y = value;
        }
    });
    if (rect.movable) {
        container.appendChild(div2);
    }
};
/**
 *
 */
ArrangePanel.prototype.addGeometryHandler = function (input, fn) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var initialValue = null;
    var panel = this;
    function update(evt) {
        if (input.value != "") {
            var value = parseFloat(input.value);
            if (isNaN(value)) {
                input.value = initialValue + " " + panel.getUnit();
            }
            else if (value != initialValue) {
                graph.getModel().beginUpdate();
                try {
                    var cells = graph.getSelectionCells();
                    for (var i = 0; i < cells.length; i++) {
                        if (graph.getModel().isVertex(cells[i])) {
                            var geo = graph.getCellGeometry(cells[i]);
                            if (geo != null) {
                                geo = geo.clone();
                                if (!fn(geo, value, cells[i])) {
                                    var state = graph.view.getState(cells[i]);
                                    if (state != null && graph.isRecursiveVertexResize(state)) {
                                        graph.resizeChildCells(cells[i], geo);
                                    }
                                    graph.getModel().setGeometry(cells[i], geo);
                                    graph.constrainChildCells(cells[i]);
                                }
                            }
                        }
                    }
                }
                finally {
                    graph.getModel().endUpdate();
                }
                initialValue = value;
                input.value = value + " " + panel.getUnit();
            }
        }
        mxgraph_1.mxEvent.consume(evt);
    }
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    mxgraph_1.mxEvent.addListener(input, "change", update);
    mxgraph_1.mxEvent.addListener(input, "focus", function () {
        initialValue = input.value;
    });
    return update;
};
ArrangePanel.prototype.addEdgeGeometryHandler = function (input, fn) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var initialValue = null;
    function update(evt) {
        if (input.value != "") {
            var value = parseFloat(input.value);
            if (isNaN(value)) {
                input.value = initialValue + " pt";
            }
            else if (value != initialValue) {
                graph.getModel().beginUpdate();
                try {
                    var cells = graph.getSelectionCells();
                    for (var i = 0; i < cells.length; i++) {
                        if (graph.getModel().isEdge(cells[i])) {
                            var geo = graph.getCellGeometry(cells[i]);
                            if (geo != null) {
                                geo = geo.clone();
                                fn(geo, value);
                                graph.getModel().setGeometry(cells[i], geo);
                            }
                        }
                    }
                }
                finally {
                    graph.getModel().endUpdate();
                }
                initialValue = value;
                input.value = value + " pt";
            }
        }
        mxgraph_1.mxEvent.consume(evt);
    }
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    mxgraph_1.mxEvent.addListener(input, "change", update);
    mxgraph_1.mxEvent.addListener(input, "focus", function () {
        initialValue = input.value;
    });
    return update;
};
/**
 *
 */
ArrangePanel.prototype.addEdgeGeometry = function (container) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var rect = this.format.getSelectionState();
    var div = this.createPanel();
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("width"));
    div.appendChild(span);
    var widthUpdate, xtUpdate, ytUpdate, xsUpdate, ysUpdate;
    var width = this.addUnitInput(div, "pt", 20, 44, function () {
        widthUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(div);
    this.addKeyHandler(width, listener);
    function widthUpdate(evt) {
        // Maximum stroke width is 999
        var value = parseInt(width.value);
        value = Math.min(999, Math.max(1, isNaN(value) ? 1 : value));
        if (value !=
            mxgraph_1.mxUtils.getValue(rect.style, "width", mxgraph_1.mxCellRenderer.defaultShapes["flexArrow"].prototype.defaultWidth)) {
            graph.setCellStyles("width", value, graph.getSelectionCells());
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", ["width"], "values", [value], "cells", graph.getSelectionCells()));
        }
        width.value = value + " pt";
        mxgraph_1.mxEvent.consume(evt);
    }
    mxgraph_1.mxEvent.addListener(width, "blur", widthUpdate);
    mxgraph_1.mxEvent.addListener(width, "change", widthUpdate);
    container.appendChild(div);
    var divs = this.createPanel();
    divs.style.paddingBottom = "30px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, "Start");
    divs.appendChild(span);
    var xs = this.addUnitInput(divs, "pt", 84, 44, function () {
        xsUpdate.apply(this, arguments);
    });
    var ys = this.addUnitInput(divs, "pt", 20, 44, function () {
        ysUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(divs);
    this.addLabel(divs, mxgraph_1.mxResources.get("left"), 84);
    this.addLabel(divs, mxgraph_1.mxResources.get("top"), 20);
    container.appendChild(divs);
    this.addKeyHandler(xs, listener);
    this.addKeyHandler(ys, listener);
    var divt = this.createPanel();
    divt.style.paddingBottom = "30px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, "End");
    divt.appendChild(span);
    var xt = this.addUnitInput(divt, "pt", 84, 44, function () {
        xtUpdate.apply(this, arguments);
    });
    var yt = this.addUnitInput(divt, "pt", 20, 44, function () {
        ytUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(divt);
    this.addLabel(divt, mxgraph_1.mxResources.get("left"), 84);
    this.addLabel(divt, mxgraph_1.mxResources.get("top"), 20);
    container.appendChild(divt);
    this.addKeyHandler(xt, listener);
    this.addKeyHandler(yt, listener);
    var listener = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
        rect = this.format.getSelectionState();
        var cell = graph.getSelectionCell();
        if (rect.style.shape == "link" || rect.style.shape == "flexArrow") {
            div.style.display = "";
            if (force || document.activeElement != width) {
                var value = mxgraph_1.mxUtils.getValue(rect.style, "width", mxgraph_1.mxCellRenderer.defaultShapes["flexArrow"].prototype.defaultWidth);
                width.value = value + " pt";
            }
        }
        else {
            div.style.display = "none";
        }
        if (graph.getSelectionCount() == 1 && graph.model.isEdge(cell)) {
            var geo = graph.model.getGeometry(cell);
            if (geo.sourcePoint != null && graph.model.getTerminal(cell, true) == null) {
                xs.value = geo.sourcePoint.x;
                ys.value = geo.sourcePoint.y;
            }
            else {
                divs.style.display = "none";
            }
            if (geo.targetPoint != null && graph.model.getTerminal(cell, false) == null) {
                xt.value = geo.targetPoint.x;
                yt.value = geo.targetPoint.y;
            }
            else {
                divt.style.display = "none";
            }
        }
        else {
            divs.style.display = "none";
            divt.style.display = "none";
        }
    });
    xsUpdate = this.addEdgeGeometryHandler(xs, function (geo, value) {
        geo.sourcePoint.x = value;
    });
    ysUpdate = this.addEdgeGeometryHandler(ys, function (geo, value) {
        geo.sourcePoint.y = value;
    });
    xtUpdate = this.addEdgeGeometryHandler(xt, function (geo, value) {
        geo.targetPoint.x = value;
    });
    ytUpdate = this.addEdgeGeometryHandler(yt, function (geo, value) {
        geo.targetPoint.y = value;
    });
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
};
/**
 * Adds the label menu items to the given menu and parent.
 */
function TextFormatPanel(format, editorUi, container) {
    BaseFormatPanel.call(this, format, editorUi, container);
    this.init();
}
exports.TextFormatPanel = TextFormatPanel;
mxgraph_1.mxUtils.extend(TextFormatPanel, BaseFormatPanel);
/**
 * Adds the label menu items to the given menu and parent.
 */
TextFormatPanel.prototype.init = function () {
    this.container.style.borderBottom = "none";
    this.addFont(this.container);
};
/**
 * Adds the label menu items to the given menu and parent.
 */
TextFormatPanel.prototype.addFont = function (container) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    var ss = this.format.getSelectionState();
    var title = this.createTitle(mxgraph_1.mxResources.get("font"));
    title.style.paddingLeft = "18px";
    title.style.paddingTop = "10px";
    title.style.paddingBottom = "6px";
    container.appendChild(title);
    var stylePanel = this.createPanel();
    stylePanel.style.paddingTop = "2px";
    stylePanel.style.paddingBottom = "2px";
    stylePanel.style.position = "relative";
    stylePanel.style.marginLeft = "-2px";
    stylePanel.style.borderWidth = "0px";
    stylePanel.className = "geToolbarContainer";
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        stylePanel.style.display = "block";
    }
    if (graph.cellEditor.isContentEditing()) {
        var cssPanel = stylePanel.cloneNode();
        var cssMenu = this.editorUi.toolbar.addMenu(mxgraph_1.mxResources.get("style"), mxgraph_1.mxResources.get("style"), true, "formatBlock", cssPanel, null, true);
        cssMenu.style.color = "rgb(112, 112, 112)";
        cssMenu.style.whiteSpace = "nowrap";
        cssMenu.style.overflow = "hidden";
        cssMenu.style.margin = "0px";
        this.addArrow(cssMenu);
        cssMenu.style.width = "192px";
        cssMenu.style.height = "15px";
        var arrow = cssMenu.getElementsByTagName("div")[0];
        arrow.style.cssFloat = "right";
        container.appendChild(cssPanel);
    }
    container.appendChild(stylePanel);
    var colorPanel = this.createPanel();
    colorPanel.style.marginTop = "8px";
    colorPanel.style.borderTop = "1px solid #c0c0c0";
    colorPanel.style.paddingTop = "6px";
    colorPanel.style.paddingBottom = "6px";
    var fontMenu = this.editorUi.toolbar.addMenu("Helvetica", mxgraph_1.mxResources.get("fontFamily"), true, "fontFamily", stylePanel, null, true);
    fontMenu.style.color = "rgb(112, 112, 112)";
    fontMenu.style.whiteSpace = "nowrap";
    fontMenu.style.overflow = "hidden";
    fontMenu.style.margin = "0px";
    this.addArrow(fontMenu);
    fontMenu.style.width = "192px";
    fontMenu.style.height = "15px";
    var stylePanel2 = stylePanel.cloneNode(false);
    stylePanel2.style.marginLeft = "-3px";
    var fontStyleItems = this.editorUi.toolbar.addItems(["bold", "italic", "underline"], stylePanel2, true);
    fontStyleItems[0].setAttribute("title", mxgraph_1.mxResources.get("bold") + " (" + this.editorUi.actions.get("bold").shortcut + ")");
    fontStyleItems[1].setAttribute("title", mxgraph_1.mxResources.get("italic") + " (" + this.editorUi.actions.get("italic").shortcut + ")");
    fontStyleItems[2].setAttribute("title", mxgraph_1.mxResources.get("underline") + " (" + this.editorUi.actions.get("underline").shortcut + ")");
    var verticalItem = this.editorUi.toolbar.addItems(["vertical"], stylePanel2, true)[0];
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        mxgraph_1.mxUtils.br(container);
    }
    container.appendChild(stylePanel2);
    this.styleButtons(fontStyleItems);
    this.styleButtons([verticalItem]);
    var stylePanel3 = stylePanel.cloneNode(false);
    stylePanel3.style.marginLeft = "-3px";
    stylePanel3.style.paddingBottom = "0px";
    // Helper function to return a wrapper function does not pass any arguments
    var callFn = function (fn) {
        return function () {
            return fn();
        };
    };
    var left = this.editorUi.toolbar.addButton("geSprite-left", mxgraph_1.mxResources.get("left"), graph.cellEditor.isContentEditing()
        ? function (evt) {
            graph.cellEditor.alignText(mxgraph_1.mxConstants.ALIGN_LEFT, evt);
        }
        : callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_ALIGN], [mxgraph_1.mxConstants.ALIGN_LEFT])), stylePanel3);
    var center = this.editorUi.toolbar.addButton("geSprite-center", mxgraph_1.mxResources.get("center"), graph.cellEditor.isContentEditing()
        ? function (evt) {
            graph.cellEditor.alignText(mxgraph_1.mxConstants.ALIGN_CENTER, evt);
        }
        : callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_ALIGN], [mxgraph_1.mxConstants.ALIGN_CENTER])), stylePanel3);
    var right = this.editorUi.toolbar.addButton("geSprite-right", mxgraph_1.mxResources.get("right"), graph.cellEditor.isContentEditing()
        ? function (evt) {
            graph.cellEditor.alignText(mxgraph_1.mxConstants.ALIGN_RIGHT, evt);
        }
        : callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_ALIGN], [mxgraph_1.mxConstants.ALIGN_RIGHT])), stylePanel3);
    this.styleButtons([left, center, right]);
    // Quick hack for strikethrough
    // TODO: Add translations and toggle state
    if (graph.cellEditor.isContentEditing()) {
        var strike = this.editorUi.toolbar.addButton("geSprite-removeformat", mxgraph_1.mxResources.get("strikethrough"), function () {
            document.execCommand("strikeThrough", false, null);
        }, stylePanel2);
        this.styleButtons([strike]);
        strike.firstChild.style.background = "url(" + base64_2.background + ")";
        strike.firstChild.style.backgroundPosition = "2px 2px";
        strike.firstChild.style.backgroundSize = "18px 18px";
        this.styleButtons([strike]);
    }
    var top = this.editorUi.toolbar.addButton("geSprite-top", mxgraph_1.mxResources.get("top"), callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_VERTICAL_ALIGN], [mxgraph_1.mxConstants.ALIGN_TOP])), stylePanel3);
    var middle = this.editorUi.toolbar.addButton("geSprite-middle", mxgraph_1.mxResources.get("middle"), callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_VERTICAL_ALIGN], [mxgraph_1.mxConstants.ALIGN_MIDDLE])), stylePanel3);
    var bottom = this.editorUi.toolbar.addButton("geSprite-bottom", mxgraph_1.mxResources.get("bottom"), callFn(this.editorUi.menus.createStyleChangeFunction([mxgraph_1.mxConstants.STYLE_VERTICAL_ALIGN], [mxgraph_1.mxConstants.ALIGN_BOTTOM])), stylePanel3);
    this.styleButtons([top, middle, bottom]);
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        mxgraph_1.mxUtils.br(container);
    }
    container.appendChild(stylePanel3);
    // Hack for updating UI state below based on current text selection
    // currentTable is the current selected DOM table updated below
    var sub, sup, full, tableWrapper, currentTable, tableCell, tableRow;
    if (graph.cellEditor.isContentEditing()) {
        top.style.display = "none";
        middle.style.display = "none";
        bottom.style.display = "none";
        verticalItem.style.display = "none";
        full = this.editorUi.toolbar.addButton("geSprite-justifyfull", mxgraph_1.mxResources.get("block"), function () {
            if (full.style.opacity == 1) {
                document.execCommand("justifyfull", false, null);
            }
        }, stylePanel3);
        full.style.marginRight = "9px";
        full.style.opacity = 1;
        this.styleButtons([
            full,
            (sub = this.editorUi.toolbar.addButton("geSprite-subscript", mxgraph_1.mxResources.get("subscript") + " (" + Editor_1.Editor.ctrlKey + "+,)", function () {
                document.execCommand("subscript", false, null);
            }, stylePanel3)),
            (sup = this.editorUi.toolbar.addButton("geSprite-superscript", mxgraph_1.mxResources.get("superscript") + " (" + Editor_1.Editor.ctrlKey + "+.)", function () {
                document.execCommand("superscript", false, null);
            }, stylePanel3)),
        ]);
        sub.style.marginLeft = "9px";
        var tmp = stylePanel3.cloneNode(false);
        tmp.style.paddingTop = "4px";
        var btns = [
            this.editorUi.toolbar.addButton("geSprite-orderedlist", mxgraph_1.mxResources.get("numberedList"), function () {
                document.execCommand("insertorderedlist", false, null);
            }, tmp),
            this.editorUi.toolbar.addButton("geSprite-unorderedlist", mxgraph_1.mxResources.get("bulletedList"), function () {
                document.execCommand("insertunorderedlist", false, null);
            }, tmp),
            this.editorUi.toolbar.addButton("geSprite-outdent", mxgraph_1.mxResources.get("decreaseIndent"), function () {
                document.execCommand("outdent", false, null);
            }, tmp),
            this.editorUi.toolbar.addButton("geSprite-indent", mxgraph_1.mxResources.get("increaseIndent"), function () {
                document.execCommand("indent", false, null);
            }, tmp),
            this.editorUi.toolbar.addButton("geSprite-removeformat", mxgraph_1.mxResources.get("removeFormat"), function () {
                document.execCommand("removeformat", false, null);
            }, tmp),
            this.editorUi.toolbar.addButton("geSprite-code", mxgraph_1.mxResources.get("html"), function () {
                graph.cellEditor.toggleViewMode();
            }, tmp),
        ];
        this.styleButtons(btns);
        btns[btns.length - 2].style.marginLeft = "9px";
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            mxgraph_1.mxUtils.br(container);
            tmp.style.height = "40";
        }
        container.appendChild(tmp);
    }
    else {
        fontStyleItems[2].style.marginRight = "9px";
        right.style.marginRight = "9px";
    }
    // Label position
    var stylePanel4 = stylePanel.cloneNode(false);
    stylePanel4.style.marginLeft = "0px";
    stylePanel4.style.paddingTop = "8px";
    stylePanel4.style.paddingBottom = "4px";
    stylePanel4.style.fontWeight = "normal";
    mxgraph_1.mxUtils.write(stylePanel4, mxgraph_1.mxResources.get("position"));
    // Adds label position options
    var positionSelect = document.createElement("select");
    positionSelect.style.position = "absolute";
    positionSelect.style.right = "20px";
    positionSelect.style.width = "97px";
    positionSelect.style.marginTop = "-2px";
    var directions = [
        "topLeft",
        "top",
        "topRight",
        "left",
        "center",
        "right",
        "bottomLeft",
        "bottom",
        "bottomRight",
    ];
    var lset = {
        topLeft: [
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_TOP,
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
        ],
        top: [
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_TOP,
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
        ],
        topRight: [
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_TOP,
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
        ],
        left: [
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
        ],
        center: [
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
        ],
        right: [
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_MIDDLE,
        ],
        bottomLeft: [
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_TOP,
        ],
        bottom: [
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
            mxgraph_1.mxConstants.ALIGN_CENTER,
            mxgraph_1.mxConstants.ALIGN_TOP,
        ],
        bottomRight: [
            mxgraph_1.mxConstants.ALIGN_RIGHT,
            mxgraph_1.mxConstants.ALIGN_BOTTOM,
            mxgraph_1.mxConstants.ALIGN_LEFT,
            mxgraph_1.mxConstants.ALIGN_TOP,
        ],
    };
    for (var i = 0; i < directions.length; i++) {
        var positionOption = document.createElement("option");
        positionOption.setAttribute("value", directions[i]);
        mxgraph_1.mxUtils.write(positionOption, mxgraph_1.mxResources.get(directions[i]));
        positionSelect.appendChild(positionOption);
    }
    stylePanel4.appendChild(positionSelect);
    // Writing direction
    var stylePanel5 = stylePanel.cloneNode(false);
    stylePanel5.style.marginLeft = "0px";
    stylePanel5.style.paddingTop = "4px";
    stylePanel5.style.paddingBottom = "4px";
    stylePanel5.style.fontWeight = "normal";
    mxgraph_1.mxUtils.write(stylePanel5, mxgraph_1.mxResources.get("writingDirection"));
    // Adds writing direction options
    // LATER: Handle reselect of same option in all selects (change event
    // is not fired for same option so have opened state on click) and
    // handle multiple different styles for current selection
    var dirSelect = document.createElement("select");
    dirSelect.style.position = "absolute";
    dirSelect.style.right = "20px";
    dirSelect.style.width = "97px";
    dirSelect.style.marginTop = "-2px";
    // NOTE: For automatic we use the value null since automatic
    // requires the text to be non formatted and non-wrapped
    var dirs = ["automatic", "leftToRight", "rightToLeft"];
    var dirSet = {
        automatic: null,
        leftToRight: mxgraph_1.mxConstants.TEXT_DIRECTION_LTR,
        rightToLeft: mxgraph_1.mxConstants.TEXT_DIRECTION_RTL,
    };
    for (var i = 0; i < dirs.length; i++) {
        var dirOption = document.createElement("option");
        dirOption.setAttribute("value", dirs[i]);
        mxgraph_1.mxUtils.write(dirOption, mxgraph_1.mxResources.get(dirs[i]));
        dirSelect.appendChild(dirOption);
    }
    stylePanel5.appendChild(dirSelect);
    if (!graph.isEditing()) {
        container.appendChild(stylePanel4);
        mxgraph_1.mxEvent.addListener(positionSelect, "change", function (evt) {
            graph.getModel().beginUpdate();
            try {
                var vals = lset[positionSelect.value];
                if (vals != null) {
                    graph.setCellStyles(mxgraph_1.mxConstants.STYLE_LABEL_POSITION, vals[0], graph.getSelectionCells());
                    graph.setCellStyles(mxgraph_1.mxConstants.STYLE_VERTICAL_LABEL_POSITION, vals[1], graph.getSelectionCells());
                    graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ALIGN, vals[2], graph.getSelectionCells());
                    graph.setCellStyles(mxgraph_1.mxConstants.STYLE_VERTICAL_ALIGN, vals[3], graph.getSelectionCells());
                }
            }
            finally {
                graph.getModel().endUpdate();
            }
            mxgraph_1.mxEvent.consume(evt);
        });
        // LATER: Update dir in text editor while editing and update style with label
        // NOTE: The tricky part is handling and passing on the auto value
        container.appendChild(stylePanel5);
        mxgraph_1.mxEvent.addListener(dirSelect, "change", function (evt) {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_TEXT_DIRECTION, dirSet[dirSelect.value], graph.getSelectionCells());
            mxgraph_1.mxEvent.consume(evt);
        });
    }
    // Font size
    var input = document.createElement("input");
    input.style.textAlign = "right";
    input.style.marginTop = "4px";
    if (!mxgraph_1.mxClient.IS_QUIRKS) {
        input.style.position = "absolute";
        input.style.right = "32px";
    }
    input.style.width = "46px";
    input.style.height = mxgraph_1.mxClient.IS_QUIRKS ? "21px" : "17px";
    stylePanel2.appendChild(input);
    // Workaround for font size 4 if no text is selected is update font size below
    // after first character was entered (as the font element is lazy created)
    var pendingFontSize = null;
    var inputUpdate = this.installInputHandler(input, mxgraph_1.mxConstants.STYLE_FONTSIZE, Menus_1.Menus.prototype.defaultFontSize, 1, 999, " pt", function (fontSize) {
        // IE does not support containsNode
        // KNOWN: Fixes font size issues but bypasses undo
        if (window.getSelection && !mxgraph_1.mxClient.IS_IE && !mxgraph_1.mxClient.IS_IE11) {
            var selection = window.getSelection();
            var container_1 = selection.rangeCount > 0
                ? selection.getRangeAt(0).commonAncestorContainer
                : graph.cellEditor.textarea;
            function updateSize(elt, ignoreContains) {
                if (graph.cellEditor.textarea != null &&
                    elt != graph.cellEditor.textarea &&
                    graph.cellEditor.textarea.contains(elt) &&
                    (ignoreContains || selection.containsNode(elt, true))) {
                    if (elt.nodeName == "FONT") {
                        elt.removeAttribute("size");
                        elt.style.fontSize = fontSize + "px";
                    }
                    else {
                        var css = mxgraph_1.mxUtils.getCurrentStyle(elt);
                        if (css.fontSize != fontSize + "px") {
                            if (mxgraph_1.mxUtils.getCurrentStyle(elt.parentNode).fontSize != fontSize + "px") {
                                elt.style.fontSize = fontSize + "px";
                            }
                            else {
                                elt.style.fontSize = "";
                            }
                        }
                    }
                }
            }
            // Wraps text node or mixed selection with leading text in a font element
            if (container_1 == graph.cellEditor.textarea ||
                container_1.nodeType != mxgraph_1.mxConstants.NODETYPE_ELEMENT) {
                document.execCommand("fontSize", false, "1");
            }
            if (container_1 != graph.cellEditor.textarea) {
                container_1 = container_1.parentNode;
            }
            if (container_1 != null && container_1.nodeType == mxgraph_1.mxConstants.NODETYPE_ELEMENT) {
                var elts = container_1.getElementsByTagName("*");
                updateSize(container_1);
                for (var i = 0; i < elts.length; i++) {
                    updateSize(elts[i]);
                }
            }
            input.value = fontSize + " pt";
        }
        else if (window.getSelection || document.selection) {
            // Checks selection
            var par = null;
            if (document.selection) {
                par = document.selection.createRange().parentElement();
            }
            else {
                var selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    par = selection.getRangeAt(0).commonAncestorContainer;
                }
            }
            // Node.contains does not work for text nodes in IE11
            function isOrContains(container, node) {
                while (node != null) {
                    if (node === container) {
                        return true;
                    }
                    node = node.parentNode;
                }
                return false;
            }
            if (par != null && isOrContains(graph.cellEditor.textarea, par)) {
                pendingFontSize = fontSize;
                // Workaround for can't set font size in px is to change font size afterwards
                document.execCommand("fontSize", false, "4");
                var elts = graph.cellEditor.textarea.getElementsByTagName("font");
                for (var i = 0; i < elts.length; i++) {
                    if (elts[i].getAttribute("size") == "4") {
                        elts[i].removeAttribute("size");
                        elts[i].style.fontSize = pendingFontSize + "px";
                        // Overrides fontSize in input with the one just assigned as a workaround
                        // for potential fontSize values of parent elements that don't match
                        window.setTimeout(function () {
                            input.value = pendingFontSize + " pt";
                            pendingFontSize = null;
                        }, 0);
                        break;
                    }
                }
            }
        }
    }, true);
    var stepper = this.createStepper(input, inputUpdate, 1, 10, true, Menus_1.Menus.prototype.defaultFontSize);
    stepper.style.display = input.style.display;
    stepper.style.marginTop = "4px";
    if (!mxgraph_1.mxClient.IS_QUIRKS) {
        stepper.style.right = "20px";
    }
    stylePanel2.appendChild(stepper);
    var arrow = fontMenu.getElementsByTagName("div")[0];
    arrow.style.cssFloat = "right";
    var bgColorApply = null;
    var currentBgColor = "#ffffff";
    var fontColorApply = null;
    var currentFontColor = "#000000";
    var bgPanel = graph.cellEditor.isContentEditing()
        ? this.createColorOption(mxgraph_1.mxResources.get("backgroundColor"), function () {
            return currentBgColor;
        }, function (color) {
            document.execCommand("backcolor", false, color != mxgraph_1.mxConstants.NONE ? color : "transparent");
        }, "#ffffff", {
            install: function (apply) {
                bgColorApply = apply;
            },
            destroy: function () {
                bgColorApply = null;
            },
        }, null, true)
        : this.createCellColorOption(mxgraph_1.mxResources.get("backgroundColor"), mxgraph_1.mxConstants.STYLE_LABEL_BACKGROUNDCOLOR, "#ffffff", null, function () {
            graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                elt.style.backgroundColor = null;
            });
        });
    bgPanel.style.fontWeight = "bold";
    var borderPanel = this.createCellColorOption(mxgraph_1.mxResources.get("borderColor"), mxgraph_1.mxConstants.STYLE_LABEL_BORDERCOLOR, "#000000");
    borderPanel.style.fontWeight = "bold";
    var panel = graph.cellEditor.isContentEditing()
        ? this.createColorOption(mxgraph_1.mxResources.get("fontColor"), function () {
            return currentFontColor;
        }, function (color) {
            if (mxgraph_1.mxClient.IS_FF) {
                // Workaround for Firefox that adds the font element around
                // anchor elements which ignore inherited colors is to move
                // the font element inside anchor elements
                var tmp = graph.cellEditor.textarea.getElementsByTagName("font");
                var oldFonts = [];
                for (var i = 0; i < tmp.length; i++) {
                    oldFonts.push({
                        node: tmp[i],
                        color: tmp[i].getAttribute("color"),
                    });
                }
                document.execCommand("forecolor", false, color != mxgraph_1.mxConstants.NONE ? color : "transparent");
                // Finds the new or changed font element
                var newFonts = graph.cellEditor.textarea.getElementsByTagName("font");
                for (var i = 0; i < newFonts.length; i++) {
                    if (i >= oldFonts.length ||
                        newFonts[i] != oldFonts[i].node ||
                        (newFonts[i] == oldFonts[i].node &&
                            newFonts[i].getAttribute("color") != oldFonts[i].color)) {
                        var child = newFonts[i].firstChild;
                        // Moves the font element to inside the anchor element and adopts all children
                        if (child != null &&
                            child.nodeName == "A" &&
                            child.nextSibling == null &&
                            child.firstChild != null) {
                            var parent_1 = newFonts[i].parentNode;
                            parent_1.insertBefore(child, newFonts[i]);
                            var tmp = child.firstChild;
                            while (tmp != null) {
                                var next = tmp.nextSibling;
                                newFonts[i].appendChild(tmp);
                                tmp = next;
                            }
                            child.appendChild(newFonts[i]);
                        }
                        break;
                    }
                }
            }
            else {
                document.execCommand("forecolor", false, color != mxgraph_1.mxConstants.NONE ? color : "transparent");
            }
        }, "#000000", {
            install: function (apply) {
                fontColorApply = apply;
            },
            destroy: function () {
                fontColorApply = null;
            },
        }, null, true)
        : this.createCellColorOption(mxgraph_1.mxResources.get("fontColor"), mxgraph_1.mxConstants.STYLE_FONTCOLOR, "#000000", function (color) {
            if (color == null || color == mxgraph_1.mxConstants.NONE) {
                bgPanel.style.display = "none";
            }
            else {
                bgPanel.style.display = "";
            }
            borderPanel.style.display = bgPanel.style.display;
        }, function (color) {
            if (color == null || color == mxgraph_1.mxConstants.NONE) {
                graph.setCellStyles(mxgraph_1.mxConstants.STYLE_NOLABEL, "1", graph.getSelectionCells());
            }
            else {
                graph.setCellStyles(mxgraph_1.mxConstants.STYLE_NOLABEL, null, graph.getSelectionCells());
            }
            graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                elt.removeAttribute("color");
                elt.style.color = null;
            });
        });
    panel.style.fontWeight = "bold";
    colorPanel.appendChild(panel);
    colorPanel.appendChild(bgPanel);
    if (!graph.cellEditor.isContentEditing()) {
        colorPanel.appendChild(borderPanel);
    }
    container.appendChild(colorPanel);
    var extraPanel = this.createPanel();
    extraPanel.style.paddingTop = "2px";
    extraPanel.style.paddingBottom = "4px";
    // LATER: Fix toggle using '' instead of 'null'
    var wwOpt = this.createCellOption(mxgraph_1.mxResources.get("wordWrap"), mxgraph_1.mxConstants.STYLE_WHITE_SPACE, null, "wrap", "null", null, null, true);
    wwOpt.style.fontWeight = "bold";
    // Word wrap in edge labels only supported via labelWidth style
    if (!ss.containsLabel && !ss.autoSize && ss.edges.length == 0) {
        extraPanel.appendChild(wwOpt);
    }
    // Delegates switch of style to formattedText action as it also convertes newlines
    var htmlOpt = this.createCellOption(mxgraph_1.mxResources.get("formattedText"), "html", "0", null, null, null, ui.actions.get("formattedText"));
    htmlOpt.style.fontWeight = "bold";
    extraPanel.appendChild(htmlOpt);
    var spacingPanel = this.createPanel();
    spacingPanel.style.paddingTop = "10px";
    spacingPanel.style.paddingBottom = "28px";
    spacingPanel.style.fontWeight = "normal";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.width = "70px";
    span.style.marginTop = "0px";
    span.style.fontWeight = "bold";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("spacing"));
    spacingPanel.appendChild(span);
    var topUpdate, globalUpdate, leftUpdate, bottomUpdate, rightUpdate;
    var topSpacing = this.addUnitInput(spacingPanel, "pt", 91, 44, function () {
        topUpdate.apply(this, arguments);
    });
    var globalSpacing = this.addUnitInput(spacingPanel, "pt", 20, 44, function () {
        globalUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(spacingPanel);
    this.addLabel(spacingPanel, mxgraph_1.mxResources.get("top"), 91);
    this.addLabel(spacingPanel, mxgraph_1.mxResources.get("global"), 20);
    mxgraph_1.mxUtils.br(spacingPanel);
    mxgraph_1.mxUtils.br(spacingPanel);
    var leftSpacing = this.addUnitInput(spacingPanel, "pt", 162, 44, function () {
        leftUpdate.apply(this, arguments);
    });
    var bottomSpacing = this.addUnitInput(spacingPanel, "pt", 91, 44, function () {
        bottomUpdate.apply(this, arguments);
    });
    var rightSpacing = this.addUnitInput(spacingPanel, "pt", 20, 44, function () {
        rightUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(spacingPanel);
    this.addLabel(spacingPanel, mxgraph_1.mxResources.get("left"), 162);
    this.addLabel(spacingPanel, mxgraph_1.mxResources.get("bottom"), 91);
    this.addLabel(spacingPanel, mxgraph_1.mxResources.get("right"), 20);
    if (!graph.cellEditor.isContentEditing()) {
        container.appendChild(extraPanel);
        container.appendChild(this.createRelativeOption(mxgraph_1.mxResources.get("opacity"), mxgraph_1.mxConstants.STYLE_TEXT_OPACITY));
        container.appendChild(spacingPanel);
    }
    else {
        var selState_1 = null;
        var lineHeightInput = null;
        container.appendChild(this.createRelativeOption(mxgraph_1.mxResources.get("lineheight"), null, null, function (input) {
            var value = input.value == "" ? 120 : parseInt(input.value);
            value = Math.max(0, isNaN(value) ? 120 : value);
            if (selState_1 != null) {
                graph.cellEditor.restoreSelection(selState_1);
                selState_1 = null;
            }
            var selectedElement = graph.getSelectedElement();
            var node = selectedElement;
            while (node != null && node.nodeType != mxgraph_1.mxConstants.NODETYPE_ELEMENT) {
                node = node.parentNode;
            }
            if (node != null &&
                node == graph.cellEditor.textarea &&
                graph.cellEditor.textarea.firstChild != null) {
                if (graph.cellEditor.textarea.firstChild.nodeName != "P") {
                    graph.cellEditor.textarea.innerHTML =
                        "<p>" + graph.cellEditor.textarea.innerHTML + "</p>";
                }
                node = graph.cellEditor.textarea.firstChild;
            }
            if (node != null &&
                graph.cellEditor.textarea != null &&
                node != graph.cellEditor.textarea &&
                graph.cellEditor.textarea.contains(node)) {
                node.style.lineHeight = value + "%";
            }
            input.value = value + " %";
        }, function (input) {
            // Used in CSS handler to update current value
            lineHeightInput = input;
            // KNOWN: Arrow up/down clear selection text in quirks/IE 8
            // Text size via arrow button limits to 16 in IE11. Why?
            mxgraph_1.mxEvent.addListener(input, "mousedown", function () {
                if (document.activeElement == graph.cellEditor.textarea) {
                    selState_1 = graph.cellEditor.saveSelection();
                }
            });
            mxgraph_1.mxEvent.addListener(input, "touchstart", function () {
                if (document.activeElement == graph.cellEditor.textarea) {
                    selState_1 = graph.cellEditor.saveSelection();
                }
            });
            input.value = "120 %";
        }));
        var insertPanel = stylePanel.cloneNode(false);
        insertPanel.style.paddingLeft = "0px";
        var btns = [
            this.editorUi.toolbar.addButton("geSprite-horizontalrule", mxgraph_1.mxResources.get("insertHorizontalRule"), function () {
                document.execCommand("inserthorizontalrule", false);
            }, insertPanel),
            this.editorUi.toolbar.addMenuFunctionInContainer(insertPanel, "geSprite-table", mxgraph_1.mxResources.get("table"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
                this.editorUi.menus.addInsertTableItem(menu);
            })),
        ];
        this.styleButtons(btns);
        var wrapper2 = this.createPanel();
        wrapper2.style.paddingTop = "10px";
        wrapper2.style.paddingBottom = "10px";
        wrapper2.appendChild(this.createTitle(mxgraph_1.mxResources.get("insert")));
        wrapper2.appendChild(insertPanel);
        container.appendChild(wrapper2);
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            wrapper2.style.height = "70";
        }
        var tablePanel = stylePanel.cloneNode(false);
        tablePanel.style.paddingLeft = "0px";
        var btns = [
            this.editorUi.toolbar.addButton("geSprite-insertcolumnbefore", mxgraph_1.mxResources.get("insertColumnBefore"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null) {
                        graph.insertColumn(currentTable, tableCell != null ? tableCell.cellIndex : 0);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
            this.editorUi.toolbar.addButton("geSprite-insertcolumnafter", mxgraph_1.mxResources.get("insertColumnAfter"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null) {
                        graph.insertColumn(currentTable, tableCell != null ? tableCell.cellIndex + 1 : -1);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
            this.editorUi.toolbar.addButton("geSprite-deletecolumn", mxgraph_1.mxResources.get("deleteColumn"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null && tableCell != null) {
                        graph.deleteColumn(currentTable, tableCell.cellIndex);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
            this.editorUi.toolbar.addButton("geSprite-insertrowbefore", mxgraph_1.mxResources.get("insertRowBefore"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null && tableRow != null) {
                        graph.insertRow(currentTable, tableRow.sectionRowIndex);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
            this.editorUi.toolbar.addButton("geSprite-insertrowafter", mxgraph_1.mxResources.get("insertRowAfter"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null && tableRow != null) {
                        graph.insertRow(currentTable, tableRow.sectionRowIndex + 1);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
            this.editorUi.toolbar.addButton("geSprite-deleterow", mxgraph_1.mxResources.get("deleteRow"), mxgraph_1.mxUtils.bind(this, function () {
                try {
                    if (currentTable != null && tableRow != null) {
                        graph.deleteRow(currentTable, tableRow.sectionRowIndex);
                    }
                }
                catch (e) {
                    this.editorUi.handleError(e);
                }
            }), tablePanel),
        ];
        this.styleButtons(btns);
        btns[2].style.marginRight = "9px";
        var wrapper3 = this.createPanel();
        wrapper3.style.paddingTop = "10px";
        wrapper3.style.paddingBottom = "10px";
        wrapper3.appendChild(this.createTitle(mxgraph_1.mxResources.get("table")));
        wrapper3.appendChild(tablePanel);
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            mxgraph_1.mxUtils.br(container);
            wrapper3.style.height = "70";
        }
        var tablePanel2 = stylePanel.cloneNode(false);
        tablePanel2.style.paddingLeft = "0px";
        var btns = [
            this.editorUi.toolbar.addButton("geSprite-strokecolor", mxgraph_1.mxResources.get("borderColor"), mxgraph_1.mxUtils.bind(this, function (evt) {
                if (currentTable != null) {
                    // Converts rgb(r,g,b) values
                    var color = currentTable.style.borderColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g, function ($0, $1, $2, $3) {
                        return ("#" +
                            ("0" + Number($1).toString(16)).substr(-2) +
                            ("0" + Number($2).toString(16)).substr(-2) +
                            ("0" + Number($3).toString(16)).substr(-2));
                    });
                    this.editorUi.pickColor(color, function (newColor) {
                        var targetElt = tableCell != null && (evt == null || !mxgraph_1.mxEvent.isShiftDown(evt))
                            ? tableCell
                            : currentTable;
                        graph.processElements(targetElt, function (elt) {
                            elt.style.border = null;
                        });
                        if (newColor == null || newColor == mxgraph_1.mxConstants.NONE) {
                            targetElt.removeAttribute("border");
                            targetElt.style.border = "";
                            targetElt.style.borderCollapse = "";
                        }
                        else {
                            targetElt.setAttribute("border", "1");
                            targetElt.style.border = "1px solid " + newColor;
                            targetElt.style.borderCollapse = "collapse";
                        }
                    });
                }
            }), tablePanel2),
            this.editorUi.toolbar.addButton("geSprite-fillcolor", mxgraph_1.mxResources.get("backgroundColor"), mxgraph_1.mxUtils.bind(this, function (evt) {
                // Converts rgb(r,g,b) values
                if (currentTable != null) {
                    var color = currentTable.style.backgroundColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g, function ($0, $1, $2, $3) {
                        return ("#" +
                            ("0" + Number($1).toString(16)).substr(-2) +
                            ("0" + Number($2).toString(16)).substr(-2) +
                            ("0" + Number($3).toString(16)).substr(-2));
                    });
                    this.editorUi.pickColor(color, function (newColor) {
                        var targetElt = tableCell != null && (evt == null || !mxgraph_1.mxEvent.isShiftDown(evt))
                            ? tableCell
                            : currentTable;
                        graph.processElements(targetElt, function (elt) {
                            elt.style.backgroundColor = null;
                        });
                        if (newColor == null || newColor == mxgraph_1.mxConstants.NONE) {
                            targetElt.style.backgroundColor = "";
                        }
                        else {
                            targetElt.style.backgroundColor = newColor;
                        }
                    });
                }
            }), tablePanel2),
            this.editorUi.toolbar.addButton("geSprite-fit", mxgraph_1.mxResources.get("spacing"), function () {
                if (currentTable != null) {
                    var value = currentTable.getAttribute("cellPadding") || 0;
                    var dlg = new Dialogs_1.FilenameDialog(ui, value, mxgraph_1.mxResources.get("apply"), mxgraph_1.mxUtils.bind(this, function (newValue) {
                        if (newValue != null && newValue.length > 0) {
                            currentTable.setAttribute("cellPadding", newValue);
                        }
                        else {
                            currentTable.removeAttribute("cellPadding");
                        }
                    }), mxgraph_1.mxResources.get("spacing"));
                    ui.showDialog(dlg.container, 300, 80, true, true);
                    dlg.init();
                }
            }, tablePanel2),
            this.editorUi.toolbar.addButton("geSprite-left", mxgraph_1.mxResources.get("left"), function () {
                if (currentTable != null) {
                    currentTable.setAttribute("align", "left");
                }
            }, tablePanel2),
            this.editorUi.toolbar.addButton("geSprite-center", mxgraph_1.mxResources.get("center"), function () {
                if (currentTable != null) {
                    currentTable.setAttribute("align", "center");
                }
            }, tablePanel2),
            this.editorUi.toolbar.addButton("geSprite-right", mxgraph_1.mxResources.get("right"), function () {
                if (currentTable != null) {
                    currentTable.setAttribute("align", "right");
                }
            }, tablePanel2),
        ];
        this.styleButtons(btns);
        btns[2].style.marginRight = "9px";
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            mxgraph_1.mxUtils.br(wrapper3);
            mxgraph_1.mxUtils.br(wrapper3);
        }
        wrapper3.appendChild(tablePanel2);
        container.appendChild(wrapper3);
        tableWrapper = wrapper3;
    }
    function setSelected(elt, selected) {
        if (mxgraph_1.mxClient.IS_IE && (mxgraph_1.mxClient.IS_QUIRKS || document.documentMode < 10)) {
            elt.style.filter = selected
                ? "progid:DXImageTransform.Microsoft.Gradient(" +
                    "StartColorStr='#c5ecff', EndColorStr='#87d4fb', GradientType=0)"
                : "";
        }
        else {
            elt.style.backgroundImage = selected ? "linear-gradient(#c5ecff 0px,#87d4fb 100%)" : "";
        }
    }
    var listener = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
        ss = this.format.getSelectionState();
        var fontStyle = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_FONTSTYLE, 0);
        setSelected(fontStyleItems[0], (fontStyle & mxgraph_1.mxConstants.FONT_BOLD) == mxgraph_1.mxConstants.FONT_BOLD);
        setSelected(fontStyleItems[1], (fontStyle & mxgraph_1.mxConstants.FONT_ITALIC) == mxgraph_1.mxConstants.FONT_ITALIC);
        setSelected(fontStyleItems[2], (fontStyle & mxgraph_1.mxConstants.FONT_UNDERLINE) == mxgraph_1.mxConstants.FONT_UNDERLINE);
        fontMenu.firstChild.nodeValue = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_FONTFAMILY, Menus_1.Menus.prototype.defaultFont);
        setSelected(verticalItem, mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_HORIZONTAL, "1") == "0");
        if (force || document.activeElement != input) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_FONTSIZE, Menus_1.Menus.prototype.defaultFontSize));
            input.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        var align = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ALIGN, mxgraph_1.mxConstants.ALIGN_CENTER);
        setSelected(left, align == mxgraph_1.mxConstants.ALIGN_LEFT);
        setSelected(center, align == mxgraph_1.mxConstants.ALIGN_CENTER);
        setSelected(right, align == mxgraph_1.mxConstants.ALIGN_RIGHT);
        var valign = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_VERTICAL_ALIGN, mxgraph_1.mxConstants.ALIGN_MIDDLE);
        setSelected(top, valign == mxgraph_1.mxConstants.ALIGN_TOP);
        setSelected(middle, valign == mxgraph_1.mxConstants.ALIGN_MIDDLE);
        setSelected(bottom, valign == mxgraph_1.mxConstants.ALIGN_BOTTOM);
        var pos = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_LABEL_POSITION, mxgraph_1.mxConstants.ALIGN_CENTER);
        var vpos = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_VERTICAL_LABEL_POSITION, mxgraph_1.mxConstants.ALIGN_MIDDLE);
        if (pos == mxgraph_1.mxConstants.ALIGN_LEFT && vpos == mxgraph_1.mxConstants.ALIGN_TOP) {
            positionSelect.value = "topLeft";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_CENTER && vpos == mxgraph_1.mxConstants.ALIGN_TOP) {
            positionSelect.value = "top";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_RIGHT && vpos == mxgraph_1.mxConstants.ALIGN_TOP) {
            positionSelect.value = "topRight";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_LEFT && vpos == mxgraph_1.mxConstants.ALIGN_BOTTOM) {
            positionSelect.value = "bottomLeft";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_CENTER && vpos == mxgraph_1.mxConstants.ALIGN_BOTTOM) {
            positionSelect.value = "bottom";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_RIGHT && vpos == mxgraph_1.mxConstants.ALIGN_BOTTOM) {
            positionSelect.value = "bottomRight";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_LEFT) {
            positionSelect.value = "left";
        }
        else if (pos == mxgraph_1.mxConstants.ALIGN_RIGHT) {
            positionSelect.value = "right";
        }
        else {
            positionSelect.value = "center";
        }
        var dir = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_TEXT_DIRECTION, mxgraph_1.mxConstants.DEFAULT_TEXT_DIRECTION);
        if (dir == mxgraph_1.mxConstants.TEXT_DIRECTION_RTL) {
            dirSelect.value = "rightToLeft";
        }
        else if (dir == mxgraph_1.mxConstants.TEXT_DIRECTION_LTR) {
            dirSelect.value = "leftToRight";
        }
        else if (dir == mxgraph_1.mxConstants.TEXT_DIRECTION_AUTO) {
            dirSelect.value = "automatic";
        }
        if (force || document.activeElement != globalSpacing) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SPACING, 2));
            globalSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != topSpacing) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SPACING_TOP, 0));
            topSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != rightSpacing) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SPACING_RIGHT, 0));
            rightSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != bottomSpacing) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SPACING_BOTTOM, 0));
            bottomSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != leftSpacing) {
            var tmp = parseFloat(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SPACING_LEFT, 0));
            leftSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
    });
    globalUpdate = this.installInputHandler(globalSpacing, mxgraph_1.mxConstants.STYLE_SPACING, 2, -999, 999, " pt");
    topUpdate = this.installInputHandler(topSpacing, mxgraph_1.mxConstants.STYLE_SPACING_TOP, 0, -999, 999, " pt");
    rightUpdate = this.installInputHandler(rightSpacing, mxgraph_1.mxConstants.STYLE_SPACING_RIGHT, 0, -999, 999, " pt");
    bottomUpdate = this.installInputHandler(bottomSpacing, mxgraph_1.mxConstants.STYLE_SPACING_BOTTOM, 0, -999, 999, " pt");
    leftUpdate = this.installInputHandler(leftSpacing, mxgraph_1.mxConstants.STYLE_SPACING_LEFT, 0, -999, 999, " pt");
    this.addKeyHandler(input, listener);
    this.addKeyHandler(globalSpacing, listener);
    this.addKeyHandler(topSpacing, listener);
    this.addKeyHandler(rightSpacing, listener);
    this.addKeyHandler(bottomSpacing, listener);
    this.addKeyHandler(leftSpacing, listener);
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
    if (graph.cellEditor.isContentEditing()) {
        var updating_1 = false;
        var updateCssHandler = function () {
            if (!updating_1) {
                updating_1 = true;
                window.setTimeout(function () {
                    var selectedElement = graph.getSelectedElement();
                    var node = selectedElement;
                    while (node != null && node.nodeType != mxgraph_1.mxConstants.NODETYPE_ELEMENT) {
                        node = node.parentNode;
                    }
                    if (node != null) {
                        // Workaround for commonAncestor on range in IE11 returning parent of common ancestor
                        if (node == graph.cellEditor.textarea &&
                            graph.cellEditor.textarea.children.length == 1 &&
                            graph.cellEditor.textarea.firstChild.nodeType == mxgraph_1.mxConstants.NODETYPE_ELEMENT) {
                            node = graph.cellEditor.textarea.firstChild;
                        }
                        function getRelativeLineHeight(fontSize, css, elt) {
                            if (elt.style != null && css != null) {
                                var lineHeight_1 = css.lineHeight;
                                if (elt.style.lineHeight != null &&
                                    elt.style.lineHeight.substring(elt.style.lineHeight.length - 1) == "%") {
                                    return parseInt(elt.style.lineHeight) / 100;
                                }
                                else {
                                    return lineHeight_1.substring(lineHeight_1.length - 2) == "px"
                                        ? parseFloat(lineHeight_1) / fontSize
                                        : parseInt(lineHeight_1);
                                }
                            }
                            else {
                                return "";
                            }
                        }
                        function getAbsoluteFontSize(css) {
                            var fontSize = css != null ? css.fontSize : null;
                            if (fontSize != null && fontSize.substring(fontSize.length - 2) == "px") {
                                return parseFloat(fontSize);
                            }
                            else {
                                return mxgraph_1.mxConstants.DEFAULT_FONTSIZE;
                            }
                        }
                        var css = mxgraph_1.mxUtils.getCurrentStyle(node);
                        var fontSize = getAbsoluteFontSize(css);
                        var lineHeight = getRelativeLineHeight(fontSize, css, node);
                        // Finds common font size
                        var elts = node.getElementsByTagName("*");
                        // IE does not support containsNode
                        if (elts.length > 0 && window.getSelection && !mxgraph_1.mxClient.IS_IE && !mxgraph_1.mxClient.IS_IE11) {
                            var selection = window.getSelection();
                            for (var i_1 = 0; i_1 < elts.length; i_1++) {
                                if (selection.containsNode(elts[i_1], true)) {
                                    var temp = mxgraph_1.mxUtils.getCurrentStyle(elts[i_1]);
                                    fontSize = Math.max(getAbsoluteFontSize(temp), fontSize);
                                    var lh = getRelativeLineHeight(fontSize, temp, elts[i_1]);
                                    if (lh != lineHeight || isNaN(lh)) {
                                        lineHeight = "";
                                    }
                                }
                            }
                        }
                        function hasParentOrOnlyChild(name) {
                            if (graph.getParentByName(node, name, graph.cellEditor.textarea) != null) {
                                return true;
                            }
                            else {
                                var child = node;
                                while (child != null && child.childNodes.length == 1) {
                                    child = child.childNodes[0];
                                    if (child.nodeName == name) {
                                        return true;
                                    }
                                }
                            }
                            return false;
                        }
                        function isEqualOrPrefixed(str, value) {
                            if (str != null && value != null) {
                                if (str == value) {
                                    return true;
                                }
                                else if (str.length > value.length + 1) {
                                    return str.substring(str.length - value.length - 1, str.length) == "-" + value;
                                }
                            }
                            return false;
                        }
                        if (css != null) {
                            setSelected(fontStyleItems[0], css.fontWeight == "bold" ||
                                css.fontWeight > 400 ||
                                hasParentOrOnlyChild("B") ||
                                hasParentOrOnlyChild("STRONG"));
                            setSelected(fontStyleItems[1], css.fontStyle == "italic" || hasParentOrOnlyChild("I") || hasParentOrOnlyChild("EM"));
                            setSelected(fontStyleItems[2], hasParentOrOnlyChild("U"));
                            setSelected(sup, hasParentOrOnlyChild("SUP"));
                            setSelected(sub, hasParentOrOnlyChild("SUB"));
                            if (!graph.cellEditor.isTableSelected()) {
                                var align = graph.cellEditor.align ||
                                    mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ALIGN, mxgraph_1.mxConstants.ALIGN_CENTER);
                                if (isEqualOrPrefixed(css.textAlign, "justify")) {
                                    setSelected(full, isEqualOrPrefixed(css.textAlign, "justify"));
                                    setSelected(left, false);
                                    setSelected(center, false);
                                    setSelected(right, false);
                                }
                                else {
                                    setSelected(full, false);
                                    setSelected(left, align == mxgraph_1.mxConstants.ALIGN_LEFT);
                                    setSelected(center, align == mxgraph_1.mxConstants.ALIGN_CENTER);
                                    setSelected(right, align == mxgraph_1.mxConstants.ALIGN_RIGHT);
                                }
                            }
                            else {
                                setSelected(full, isEqualOrPrefixed(css.textAlign, "justify"));
                                setSelected(left, isEqualOrPrefixed(css.textAlign, "left"));
                                setSelected(center, isEqualOrPrefixed(css.textAlign, "center"));
                                setSelected(right, isEqualOrPrefixed(css.textAlign, "right"));
                            }
                            currentTable = graph.getParentByName(node, "TABLE", graph.cellEditor.textarea);
                            tableRow =
                                currentTable == null ? null : graph.getParentByName(node, "TR", currentTable);
                            tableCell =
                                currentTable == null
                                    ? null
                                    : graph.getParentByNames(node, ["TD", "TH"], currentTable);
                            tableWrapper.style.display = currentTable != null ? "" : "none";
                            if (document.activeElement != input) {
                                if (node.nodeName == "FONT" &&
                                    node.getAttribute("size") == "4" &&
                                    pendingFontSize != null) {
                                    node.removeAttribute("size");
                                    node.style.fontSize = pendingFontSize + " pt";
                                    pendingFontSize = null;
                                }
                                else {
                                    input.value = isNaN(fontSize) ? "" : fontSize + " pt";
                                }
                                var lh = parseFloat(lineHeight);
                                if (!isNaN(lh)) {
                                    lineHeightInput.value = Math.round(lh * 100) + " %";
                                }
                                else {
                                    lineHeightInput.value = "100 %";
                                }
                            }
                            // Converts rgb(r,g,b) values
                            var color = css.color.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g, function ($0, $1, $2, $3) {
                                return ("#" +
                                    ("0" + Number($1).toString(16)).substr(-2) +
                                    ("0" + Number($2).toString(16)).substr(-2) +
                                    ("0" + Number($3).toString(16)).substr(-2));
                            });
                            var color2 = css.backgroundColor.replace(/\brgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g, function ($0, $1, $2, $3) {
                                return ("#" +
                                    ("0" + Number($1).toString(16)).substr(-2) +
                                    ("0" + Number($2).toString(16)).substr(-2) +
                                    ("0" + Number($3).toString(16)).substr(-2));
                            });
                            // Updates the color picker for the current font
                            if (fontColorApply != null) {
                                if (color.charAt(0) == "#") {
                                    currentFontColor = color;
                                }
                                else {
                                    currentFontColor = "#000000";
                                }
                                fontColorApply(currentFontColor, true);
                            }
                            if (bgColorApply != null) {
                                if (color2.charAt(0) == "#") {
                                    currentBgColor = color2;
                                }
                                else {
                                    currentBgColor = null;
                                }
                                bgColorApply(currentBgColor, true);
                            }
                            // Workaround for firstChild is null or not an object
                            // in the log which seems to be IE8- only / 29.01.15
                            if (fontMenu.firstChild != null) {
                                // Strips leading and trailing quotes
                                var ff = css.fontFamily;
                                if (ff.charAt(0) == "'") {
                                    ff = ff.substring(1);
                                }
                                if (ff.charAt(ff.length - 1) == "'") {
                                    ff = ff.substring(0, ff.length - 1);
                                }
                                if (ff.charAt(0) == '"') {
                                    ff = ff.substring(1);
                                }
                                if (ff.charAt(ff.length - 1) == '"') {
                                    ff = ff.substring(0, ff.length - 1);
                                }
                                fontMenu.firstChild.nodeValue = ff;
                            }
                        }
                    }
                    updating_1 = false;
                }, 0);
            }
        };
        if (mxgraph_1.mxClient.IS_FF || mxgraph_1.mxClient.IS_EDGE || mxgraph_1.mxClient.IS_IE || mxgraph_1.mxClient.IS_IE11) {
            mxgraph_1.mxEvent.addListener(graph.cellEditor.textarea, "DOMSubtreeModified", updateCssHandler);
        }
        mxgraph_1.mxEvent.addListener(graph.cellEditor.textarea, "input", updateCssHandler);
        mxgraph_1.mxEvent.addListener(graph.cellEditor.textarea, "touchend", updateCssHandler);
        mxgraph_1.mxEvent.addListener(graph.cellEditor.textarea, "mouseup", updateCssHandler);
        mxgraph_1.mxEvent.addListener(graph.cellEditor.textarea, "keyup", updateCssHandler);
        this.listeners.push({
            destroy: function () {
                // No need to remove listener since textarea is destroyed after edit
            },
        });
        updateCssHandler();
    }
    return container;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
function StyleFormatPanel(format, editorUi, container) {
    BaseFormatPanel.call(this, format, editorUi, container);
    this.init();
}
exports.StyleFormatPanel = StyleFormatPanel;
mxgraph_1.mxUtils.extend(StyleFormatPanel, BaseFormatPanel);
/**
 *
 */
StyleFormatPanel.prototype.defaultStrokeColor = "black";
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.init = function () {
    var ss = this.format.getSelectionState();
    if (!ss.containsLabel) {
        if (ss.containsImage &&
            ss.vertices.length == 1 &&
            ss.style.shape == "image" &&
            ss.style.image != null &&
            ss.style.image.substring(0, 19) == "data:image/svg+xml;") {
            this.container.appendChild(this.addSvgStyles(this.createPanel()));
        }
        if (!ss.containsImage || ss.style.shape == "image") {
            this.container.appendChild(this.addFill(this.createPanel()));
        }
        this.container.appendChild(this.addStroke(this.createPanel()));
        this.container.appendChild(this.addLineJumps(this.createPanel()));
        var opacityPanel = this.createRelativeOption(mxgraph_1.mxResources.get("opacity"), mxgraph_1.mxConstants.STYLE_OPACITY, 41);
        opacityPanel.style.paddingTop = "8px";
        opacityPanel.style.paddingBottom = "8px";
        this.container.appendChild(opacityPanel);
        this.container.appendChild(this.addEffects(this.createPanel()));
    }
    var opsPanel = this.addEditOps(this.createPanel());
    if (opsPanel.firstChild != null) {
        mxgraph_1.mxUtils.br(opsPanel);
    }
    this.container.appendChild(this.addStyleOps(opsPanel));
};
/**
 * Use browser for parsing CSS.
 */
StyleFormatPanel.prototype.getCssRules = function (css) {
    var doc = document.implementation.createHTMLDocument("");
    var styleElement = document.createElement("style");
    mxgraph_1.mxUtils.setTextContent(styleElement, css);
    doc.body.appendChild(styleElement);
    return styleElement.sheet.cssRules;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addSvgStyles = function (container) {
    var ss = this.format.getSelectionState();
    container.style.paddingTop = "6px";
    container.style.paddingBottom = "6px";
    container.style.fontWeight = "bold";
    container.style.display = "none";
    try {
        var exp = ss.style.editableCssRules;
        if (exp != null) {
            var regex = new RegExp(exp);
            var data = ss.style.image.substring(ss.style.image.indexOf(",") + 1);
            var xml = atob(data);
            var svg = mxgraph_1.mxUtils.parseXml(xml);
            if (svg != null) {
                var styles = svg.getElementsByTagName("style");
                for (var i = 0; i < styles.length; i++) {
                    var rules = this.getCssRules(mxgraph_1.mxUtils.getTextContent(styles[i]));
                    for (var j = 0; j < rules.length; j++) {
                        this.addSvgRule(container, rules[j], svg, styles[i], rules, j, regex);
                    }
                }
            }
        }
    }
    catch (e) {
        // ignore
    }
    return container;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addSvgRule = function (container, rule, svg, styleElem, rules, ruleIndex, regex) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    if (regex.test(rule.selectorText)) {
        function rgb2hex(rgb) {
            rgb = rgb.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);
            return rgb && rgb.length === 4
                ? "#" +
                    ("0" + parseInt(rgb[1], 10).toString(16)).slice(-2) +
                    ("0" + parseInt(rgb[2], 10).toString(16)).slice(-2) +
                    ("0" + parseInt(rgb[3], 10).toString(16)).slice(-2)
                : "";
        }
        var addStyleRule = mxgraph_1.mxUtils.bind(this, function (rule, key, label) {
            if (rule.style[key] != "") {
                var option = this.createColorOption(label + " " + rule.selectorText, function () {
                    return rgb2hex(rule.style[key]);
                }, function (color) {
                    rules[ruleIndex].style[key] = color;
                    var cssTxt = "";
                    for (var i = 0; i < rules.length; i++) {
                        cssTxt += rules[i].cssText + " ";
                    }
                    styleElem.textContent = cssTxt;
                    var xml = mxgraph_1.mxUtils.getXml(svg.documentElement);
                    graph.setCellStyles(mxgraph_1.mxConstants.STYLE_IMAGE, "data:image/svg+xml," + btoa(xml), graph.getSelectionCells());
                }, "#ffffff", {
                    install: function () {
                        // ignore
                    },
                    destroy: function () {
                        // ignore
                    },
                });
                container.appendChild(option);
                // Shows container if rules are added
                container.style.display = "";
            }
        });
        addStyleRule(rule, "fill", mxgraph_1.mxResources.get("fill"));
        addStyleRule(rule, "stroke", mxgraph_1.mxResources.get("line"));
    }
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addEditOps = function (div) {
    // - Edit Style Option
    // - Edit Image Option
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addFill = function (container) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var ss = this.format.getSelectionState();
    container.style.paddingTop = "6px";
    container.style.paddingBottom = "6px";
    // Adds gradient direction option
    var gradientSelect = document.createElement("select");
    gradientSelect.style.position = "absolute";
    gradientSelect.style.marginTop = "-2px";
    gradientSelect.style.right = mxgraph_1.mxClient.IS_QUIRKS ? "52px" : "72px";
    gradientSelect.style.width = "70px";
    // Stops events from bubbling to color option event handler
    mxgraph_1.mxEvent.addListener(gradientSelect, "click", function (evt) {
        mxgraph_1.mxEvent.consume(evt);
    });
    var gradientPanel = this.createCellColorOption(mxgraph_1.mxResources.get("gradient"), mxgraph_1.mxConstants.STYLE_GRADIENTCOLOR, "#ffffff", function (color) {
        if (color == null || color == mxgraph_1.mxConstants.NONE) {
            gradientSelect.style.display = "none";
        }
        else {
            gradientSelect.style.display = "";
        }
    });
    var fillKey = ss.style.shape == "image" ? mxgraph_1.mxConstants.STYLE_IMAGE_BACKGROUND : mxgraph_1.mxConstants.STYLE_FILLCOLOR;
    var label = ss.style.shape == "image" ? mxgraph_1.mxResources.get("background") : mxgraph_1.mxResources.get("fill");
    var fillPanel = this.createCellColorOption(label, fillKey, "#ffffff");
    fillPanel.style.fontWeight = "bold";
    var tmpColor = mxgraph_1.mxUtils.getValue(ss.style, fillKey, null);
    gradientPanel.style.display =
        tmpColor != null && tmpColor != mxgraph_1.mxConstants.NONE && ss.fill && ss.style.shape != "image"
            ? ""
            : "none";
    var directions = [
        mxgraph_1.mxConstants.DIRECTION_NORTH,
        mxgraph_1.mxConstants.DIRECTION_EAST,
        mxgraph_1.mxConstants.DIRECTION_SOUTH,
        mxgraph_1.mxConstants.DIRECTION_WEST,
    ];
    for (var i = 0; i < directions.length; i++) {
        var gradientOption = document.createElement("option");
        gradientOption.setAttribute("value", directions[i]);
        mxgraph_1.mxUtils.write(gradientOption, mxgraph_1.mxResources.get(directions[i]));
        gradientSelect.appendChild(gradientOption);
    }
    gradientPanel.appendChild(gradientSelect);
    var listener = mxgraph_1.mxUtils.bind(this, function () {
        ss = this.format.getSelectionState();
        var value = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_GRADIENT_DIRECTION, mxgraph_1.mxConstants.DIRECTION_SOUTH);
        // Handles empty string which is not allowed as a value
        if (value == "") {
            value = mxgraph_1.mxConstants.DIRECTION_SOUTH;
        }
        gradientSelect.value = value;
        container.style.display = ss.fill ? "" : "none";
        var fillColor = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_FILLCOLOR, null);
        if (!ss.fill ||
            ss.containsImage ||
            fillColor == null ||
            fillColor == mxgraph_1.mxConstants.NONE ||
            ss.style.shape == "filledEdge") {
            gradientPanel.style.display = "none";
        }
        else {
            gradientPanel.style.display = "";
        }
    });
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
    mxgraph_1.mxEvent.addListener(gradientSelect, "change", function (evt) {
        graph.setCellStyles(mxgraph_1.mxConstants.STYLE_GRADIENT_DIRECTION, gradientSelect.value, graph.getSelectionCells());
        mxgraph_1.mxEvent.consume(evt);
    });
    container.appendChild(fillPanel);
    container.appendChild(gradientPanel);
    // Adds custom colors
    var custom = this.getCustomColors();
    for (var i = 0; i < custom.length; i++) {
        container.appendChild(this.createCellColorOption(custom[i].title, custom[i].key, custom[i].defaultValue));
    }
    return container;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.getCustomColors = function () {
    var ss = this.format.getSelectionState();
    var result = [];
    if (ss.style.shape == "swimlane" || ss.style.shape == "table") {
        result.push({
            title: mxgraph_1.mxResources.get("laneColor"),
            key: "swimlaneFillColor",
            defaultValue: "#ffffff",
        });
    }
    return result;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addStroke = function (container) {
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var ss = this.format.getSelectionState();
    container.style.paddingTop = "4px";
    container.style.paddingBottom = "4px";
    container.style.whiteSpace = "normal";
    var colorPanel = document.createElement("div");
    colorPanel.style.fontWeight = "bold";
    // Adds gradient direction option
    var styleSelect = document.createElement("select");
    styleSelect.style.position = "absolute";
    styleSelect.style.marginTop = "-2px";
    styleSelect.style.right = "72px";
    styleSelect.style.width = "80px";
    var styles = ["sharp", "rounded", "curved"];
    for (var i = 0; i < styles.length; i++) {
        var styleOption = document.createElement("option");
        styleOption.setAttribute("value", styles[i]);
        mxgraph_1.mxUtils.write(styleOption, mxgraph_1.mxResources.get(styles[i]));
        styleSelect.appendChild(styleOption);
    }
    mxgraph_1.mxEvent.addListener(styleSelect, "change", function (evt) {
        graph.getModel().beginUpdate();
        try {
            var keys = [mxgraph_1.mxConstants.STYLE_ROUNDED, mxgraph_1.mxConstants.STYLE_CURVED];
            // Default for rounded is 1
            var values = ["0", null];
            if (styleSelect.value == "rounded") {
                values = ["1", null];
            }
            else if (styleSelect.value == "curved") {
                values = [null, "1"];
            }
            for (var i = 0; i < keys.length; i++) {
                graph.setCellStyles(keys[i], values[i], graph.getSelectionCells());
            }
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", keys, "values", values, "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
        mxgraph_1.mxEvent.consume(evt);
    });
    // Stops events from bubbling to color option event handler
    mxgraph_1.mxEvent.addListener(styleSelect, "click", function (evt) {
        mxgraph_1.mxEvent.consume(evt);
    });
    var strokeKey = ss.style.shape == "image" ? mxgraph_1.mxConstants.STYLE_IMAGE_BORDER : mxgraph_1.mxConstants.STYLE_STROKECOLOR;
    var label = ss.style.shape == "image" ? mxgraph_1.mxResources.get("border") : mxgraph_1.mxResources.get("line");
    var lineColor = this.createCellColorOption(label, strokeKey, "#000000");
    lineColor.appendChild(styleSelect);
    colorPanel.appendChild(lineColor);
    // Used if only edges selected
    var stylePanel = colorPanel.cloneNode(false);
    stylePanel.style.fontWeight = "normal";
    stylePanel.style.whiteSpace = "nowrap";
    stylePanel.style.position = "relative";
    stylePanel.style.paddingLeft = "16px";
    stylePanel.style.marginBottom = "2px";
    stylePanel.style.marginTop = "2px";
    stylePanel.className = "geToolbarContainer";
    var addItem = mxgraph_1.mxUtils.bind(this, function (menu, width, cssName, keys, values) {
        var item = this.editorUi.menus.styleChange(menu, "", keys, values, "geIcon", null);
        var pat = document.createElement("div");
        pat.style.width = width + "px";
        pat.style.height = "1px";
        pat.style.borderBottom = "1px " + cssName + " " + this.defaultStrokeColor;
        pat.style.paddingTop = "6px";
        item.firstChild.firstChild.style.padding = "0px 4px 0px 4px";
        item.firstChild.firstChild.style.width = width + "px";
        item.firstChild.firstChild.appendChild(pat);
        return item;
    });
    var pattern = this.editorUi.toolbar.addMenuFunctionInContainer(stylePanel, "geSprite-orthogonal", mxgraph_1.mxResources.get("pattern"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        addItem(menu, 75, "solid", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], [null, null]).setAttribute("title", mxgraph_1.mxResources.get("solid"));
        addItem(menu, 75, "dashed", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", null]).setAttribute("title", mxgraph_1.mxResources.get("dashed"));
        addItem(menu, 75, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 1"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (1)");
        addItem(menu, 75, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 2"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (2)");
        addItem(menu, 75, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 4"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (3)");
    }));
    // Used for mixed selection (vertices and edges)
    var altStylePanel = stylePanel.cloneNode(false);
    var edgeShape = this.editorUi.toolbar.addMenuFunctionInContainer(altStylePanel, "geSprite-connection", mxgraph_1.mxResources.get("connection"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        this.editorUi.menus
            .styleChange(menu, "", [
            mxgraph_1.mxConstants.STYLE_SHAPE,
            mxgraph_1.mxConstants.STYLE_STARTSIZE,
            mxgraph_1.mxConstants.STYLE_ENDSIZE,
            "width",
        ], [null, null, null, null], "geIcon geSprite geSprite-connection", null, true)
            .setAttribute("title", mxgraph_1.mxResources.get("line"));
        this.editorUi.menus
            .styleChange(menu, "", [
            mxgraph_1.mxConstants.STYLE_SHAPE,
            mxgraph_1.mxConstants.STYLE_STARTSIZE,
            mxgraph_1.mxConstants.STYLE_ENDSIZE,
            "width",
        ], ["link", null, null, null], "geIcon geSprite geSprite-linkedge", null, true)
            .setAttribute("title", mxgraph_1.mxResources.get("link"));
        this.editorUi.menus
            .styleChange(menu, "", [
            mxgraph_1.mxConstants.STYLE_SHAPE,
            mxgraph_1.mxConstants.STYLE_STARTSIZE,
            mxgraph_1.mxConstants.STYLE_ENDSIZE,
            "width",
        ], ["flexArrow", null, null, null], "geIcon geSprite geSprite-arrow", null, true)
            .setAttribute("title", mxgraph_1.mxResources.get("arrow"));
        this.editorUi.menus
            .styleChange(menu, "", [
            mxgraph_1.mxConstants.STYLE_SHAPE,
            mxgraph_1.mxConstants.STYLE_STARTSIZE,
            mxgraph_1.mxConstants.STYLE_ENDSIZE,
            "width",
        ], ["arrow", null, null, null], "geIcon geSprite geSprite-simplearrow", null, true)
            .setAttribute("title", mxgraph_1.mxResources.get("simpleArrow"));
    }));
    var altPattern = this.editorUi.toolbar.addMenuFunctionInContainer(altStylePanel, "geSprite-orthogonal", mxgraph_1.mxResources.get("pattern"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        addItem(menu, 33, "solid", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], [null, null]).setAttribute("title", mxgraph_1.mxResources.get("solid"));
        addItem(menu, 33, "dashed", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", null]).setAttribute("title", mxgraph_1.mxResources.get("dashed"));
        addItem(menu, 33, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 1"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (1)");
        addItem(menu, 33, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 2"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (2)");
        addItem(menu, 33, "dotted", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], ["1", "1 4"]).setAttribute("title", mxgraph_1.mxResources.get("dotted") + " (3)");
    }));
    var stylePanel2 = stylePanel.cloneNode(false);
    // Stroke width
    var input = document.createElement("input");
    input.style.textAlign = "right";
    input.style.marginTop = "2px";
    input.style.width = "41px";
    input.setAttribute("title", mxgraph_1.mxResources.get("linewidth"));
    stylePanel.appendChild(input);
    var altInput = input.cloneNode(true);
    altStylePanel.appendChild(altInput);
    function update(evt) {
        // Maximum stroke width is 999
        var value = parseInt(input.value);
        value = Math.min(999, Math.max(1, isNaN(value) ? 1 : value));
        if (value != mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STROKEWIDTH, 1)) {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_STROKEWIDTH, value, graph.getSelectionCells());
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_STROKEWIDTH], "values", [value], "cells", graph.getSelectionCells()));
        }
        input.value = value + " pt";
        mxgraph_1.mxEvent.consume(evt);
    }
    function altUpdate(evt) {
        // Maximum stroke width is 999
        var value = parseInt(altInput.value);
        value = Math.min(999, Math.max(1, isNaN(value) ? 1 : value));
        if (value != mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STROKEWIDTH, 1)) {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_STROKEWIDTH, value, graph.getSelectionCells());
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_STROKEWIDTH], "values", [value], "cells", graph.getSelectionCells()));
        }
        altInput.value = value + " pt";
        mxgraph_1.mxEvent.consume(evt);
    }
    var stepper = this.createStepper(input, update, 1, 9);
    stepper.style.display = input.style.display;
    stepper.style.marginTop = "2px";
    stylePanel.appendChild(stepper);
    var altStepper = this.createStepper(altInput, altUpdate, 1, 9);
    altStepper.style.display = altInput.style.display;
    altStepper.style.marginTop = "2px";
    altStylePanel.appendChild(altStepper);
    if (!mxgraph_1.mxClient.IS_QUIRKS) {
        input.style.position = "absolute";
        input.style.right = "32px";
        input.style.height = "15px";
        stepper.style.right = "20px";
        altInput.style.position = "absolute";
        altInput.style.right = "32px";
        altInput.style.height = "15px";
        altStepper.style.right = "20px";
    }
    else {
        input.style.height = "17px";
        altInput.style.height = "17px";
    }
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    mxgraph_1.mxEvent.addListener(input, "change", update);
    mxgraph_1.mxEvent.addListener(altInput, "blur", altUpdate);
    mxgraph_1.mxEvent.addListener(altInput, "change", altUpdate);
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        mxgraph_1.mxUtils.br(stylePanel2);
        mxgraph_1.mxUtils.br(stylePanel2);
    }
    var edgeStyle = this.editorUi.toolbar.addMenuFunctionInContainer(stylePanel2, "geSprite-orthogonal", mxgraph_1.mxResources.get("waypoints"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        if (ss.style.shape != "arrow") {
            this.editorUi.menus
                .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_EDGE, mxgraph_1.mxConstants.STYLE_CURVED, mxgraph_1.mxConstants.STYLE_NOEDGESTYLE], [null, null, null], "geIcon geSprite geSprite-straight", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("straight"));
            this.editorUi.menus
                .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_EDGE, mxgraph_1.mxConstants.STYLE_CURVED, mxgraph_1.mxConstants.STYLE_NOEDGESTYLE], ["orthogonalEdgeStyle", null, null], "geIcon geSprite geSprite-orthogonal", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("orthogonal"));
            this.editorUi.menus
                .edgeStyleChange(menu, "", [
                mxgraph_1.mxConstants.STYLE_EDGE,
                mxgraph_1.mxConstants.STYLE_ELBOW,
                mxgraph_1.mxConstants.STYLE_CURVED,
                mxgraph_1.mxConstants.STYLE_NOEDGESTYLE,
            ], ["elbowEdgeStyle", null, null, null], "geIcon geSprite geSprite-horizontalelbow", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("simple"));
            this.editorUi.menus
                .edgeStyleChange(menu, "", [
                mxgraph_1.mxConstants.STYLE_EDGE,
                mxgraph_1.mxConstants.STYLE_ELBOW,
                mxgraph_1.mxConstants.STYLE_CURVED,
                mxgraph_1.mxConstants.STYLE_NOEDGESTYLE,
            ], ["elbowEdgeStyle", "vertical", null, null], "geIcon geSprite geSprite-verticalelbow", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("simple"));
            this.editorUi.menus
                .edgeStyleChange(menu, "", [
                mxgraph_1.mxConstants.STYLE_EDGE,
                mxgraph_1.mxConstants.STYLE_ELBOW,
                mxgraph_1.mxConstants.STYLE_CURVED,
                mxgraph_1.mxConstants.STYLE_NOEDGESTYLE,
            ], ["isometricEdgeStyle", null, null, null], "geIcon geSprite geSprite-horizontalisometric", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("isometric"));
            this.editorUi.menus
                .edgeStyleChange(menu, "", [
                mxgraph_1.mxConstants.STYLE_EDGE,
                mxgraph_1.mxConstants.STYLE_ELBOW,
                mxgraph_1.mxConstants.STYLE_CURVED,
                mxgraph_1.mxConstants.STYLE_NOEDGESTYLE,
            ], ["isometricEdgeStyle", "vertical", null, null], "geIcon geSprite geSprite-verticalisometric", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("isometric"));
            if (ss.style.shape == "connector") {
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_EDGE, mxgraph_1.mxConstants.STYLE_CURVED, mxgraph_1.mxConstants.STYLE_NOEDGESTYLE], ["orthogonalEdgeStyle", "1", null], "geIcon geSprite geSprite-curved", null, true)
                    .setAttribute("title", mxgraph_1.mxResources.get("curved"));
            }
            this.editorUi.menus
                .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_EDGE, mxgraph_1.mxConstants.STYLE_CURVED, mxgraph_1.mxConstants.STYLE_NOEDGESTYLE], ["entityRelationEdgeStyle", null, null], "geIcon geSprite geSprite-entity", null, true)
                .setAttribute("title", mxgraph_1.mxResources.get("entityRelation"));
        }
    }));
    var lineStart = this.editorUi.toolbar.addMenuFunctionInContainer(stylePanel2, "geSprite-startclassic", mxgraph_1.mxResources.get("linestart"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        if (ss.style.shape == "connector" ||
            ss.style.shape == "flexArrow" ||
            ss.style.shape == "filledEdge") {
            var item = this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.NONE, 0], "geIcon", null, false);
            item.setAttribute("title", mxgraph_1.mxResources.get("none"));
            item.firstChild.firstChild.innerHTML =
                '<font style="font-size:10px;">' +
                    mxgraph_1.mxUtils.htmlEntities(mxgraph_1.mxResources.get("none")) +
                    "</font>";
            if (ss.style.shape == "connector" || ss.style.shape == "filledEdge") {
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC, 1], "geIcon geSprite geSprite-startclassic", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("classic"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC_THIN, 1], "geIcon geSprite geSprite-startclassicthin", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_OPEN, 0], "geIcon geSprite geSprite-startopen", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("openArrow"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_OPEN_THIN, 0], "geIcon geSprite geSprite-startopenthin", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["openAsync", 0], "geIcon geSprite geSprite-startopenasync", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_BLOCK, 1], "geIcon geSprite geSprite-startblock", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_BLOCK_THIN, 1], "geIcon geSprite geSprite-startblockthin", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["async", 1], "geIcon geSprite geSprite-startasync", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_OVAL, 1], "geIcon geSprite geSprite-startoval", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("oval"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND, 1], "geIcon geSprite geSprite-startdiamond", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamond"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND_THIN, 1], "geIcon geSprite geSprite-startthindiamond", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamondThin"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC, 0], "geIcon geSprite geSprite-startclassictrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("classic"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC_THIN, 0], "geIcon geSprite geSprite-startclassicthintrans", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_BLOCK, 0], "geIcon geSprite geSprite-startblocktrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_BLOCK_THIN, 0], "geIcon geSprite geSprite-startblockthintrans", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["async", 0], "geIcon geSprite geSprite-startasynctrans", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_OVAL, 0], "geIcon geSprite geSprite-startovaltrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("oval"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND, 0], "geIcon geSprite geSprite-startdiamondtrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamond"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND_THIN, 0], "geIcon geSprite geSprite-startthindiamondtrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamondThin"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["box", 0], "geIcon geSprite geSvgSprite geSprite-box", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["halfCircle", 0], "geIcon geSprite geSvgSprite geSprite-halfCircle", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["dash", 0], "geIcon geSprite geSprite-startdash", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["cross", 0], "geIcon geSprite geSprite-startcross", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["circlePlus", 0], "geIcon geSprite geSprite-startcircleplus", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["circle", 1], "geIcon geSprite geSprite-startcircle", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERone", 0], "geIcon geSprite geSprite-starterone", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERmandOne", 0], "geIcon geSprite geSprite-starteronetoone", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERmany", 0], "geIcon geSprite geSprite-startermany", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERoneToMany", 0], "geIcon geSprite geSprite-starteronetomany", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERzeroToOne", 1], "geIcon geSprite geSprite-starteroneopt", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW, "startFill"], ["ERzeroToMany", 1], "geIcon geSprite geSprite-startermanyopt", null, false);
            }
            else {
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_STARTARROW], [mxgraph_1.mxConstants.ARROW_BLOCK], "geIcon geSprite geSprite-startblocktrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
            }
        }
    }));
    var lineEnd = this.editorUi.toolbar.addMenuFunctionInContainer(stylePanel2, "geSprite-endclassic", mxgraph_1.mxResources.get("lineend"), false, mxgraph_1.mxUtils.bind(this, function (menu) {
        if (ss.style.shape == "connector" ||
            ss.style.shape == "flexArrow" ||
            ss.style.shape == "filledEdge") {
            var item = this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.NONE, 0], "geIcon", null, false);
            item.setAttribute("title", mxgraph_1.mxResources.get("none"));
            item.firstChild.firstChild.innerHTML =
                '<font style="font-size:10px;">' +
                    mxgraph_1.mxUtils.htmlEntities(mxgraph_1.mxResources.get("none")) +
                    "</font>";
            if (ss.style.shape == "connector" || ss.style.shape == "filledEdge") {
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC, 1], "geIcon geSprite geSprite-endclassic", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("classic"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC_THIN, 1], "geIcon geSprite geSprite-endclassicthin", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_OPEN, 0], "geIcon geSprite geSprite-endopen", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("openArrow"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_OPEN_THIN, 0], "geIcon geSprite geSprite-endopenthin", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["openAsync", 0], "geIcon geSprite geSprite-endopenasync", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_BLOCK, 1], "geIcon geSprite geSprite-endblock", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_BLOCK_THIN, 1], "geIcon geSprite geSprite-endblockthin", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["async", 1], "geIcon geSprite geSprite-endasync", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_OVAL, 1], "geIcon geSprite geSprite-endoval", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("oval"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND, 1], "geIcon geSprite geSprite-enddiamond", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamond"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND_THIN, 1], "geIcon geSprite geSprite-endthindiamond", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamondThin"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC, 0], "geIcon geSprite geSprite-endclassictrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("classic"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_CLASSIC_THIN, 0], "geIcon geSprite geSprite-endclassicthintrans", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_BLOCK, 0], "geIcon geSprite geSprite-endblocktrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_BLOCK_THIN, 0], "geIcon geSprite geSprite-endblockthintrans", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["async", 0], "geIcon geSprite geSprite-endasynctrans", null, false);
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_OVAL, 0], "geIcon geSprite geSprite-endovaltrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("oval"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND, 0], "geIcon geSprite geSprite-enddiamondtrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamond"));
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], [mxgraph_1.mxConstants.ARROW_DIAMOND_THIN, 0], "geIcon geSprite geSprite-endthindiamondtrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("diamondThin"));
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["box", 0], "geIcon geSprite geSvgSprite geFlipSprite geSprite-box", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["halfCircle", 0], "geIcon geSprite geSvgSprite geFlipSprite geSprite-halfCircle", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["dash", 0], "geIcon geSprite geSprite-enddash", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["cross", 0], "geIcon geSprite geSprite-endcross", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["circlePlus", 0], "geIcon geSprite geSprite-endcircleplus", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["circle", 1], "geIcon geSprite geSprite-endcircle", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERone", 0], "geIcon geSprite geSprite-enderone", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERmandOne", 0], "geIcon geSprite geSprite-enderonetoone", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERmany", 0], "geIcon geSprite geSprite-endermany", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERoneToMany", 0], "geIcon geSprite geSprite-enderonetomany", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERzeroToOne", 1], "geIcon geSprite geSprite-enderoneopt", null, false);
                this.editorUi.menus.edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW, "endFill"], ["ERzeroToMany", 1], "geIcon geSprite geSprite-endermanyopt", null, false);
            }
            else {
                this.editorUi.menus
                    .edgeStyleChange(menu, "", [mxgraph_1.mxConstants.STYLE_ENDARROW], [mxgraph_1.mxConstants.ARROW_BLOCK], "geIcon geSprite geSprite-endblocktrans", null, false)
                    .setAttribute("title", mxgraph_1.mxResources.get("block"));
            }
        }
    }));
    this.addArrow(edgeShape, 8);
    this.addArrow(edgeStyle);
    this.addArrow(lineStart);
    this.addArrow(lineEnd);
    var symbol = this.addArrow(pattern, 9);
    symbol.className = "geIcon";
    symbol.style.width = "84px";
    var altSymbol = this.addArrow(altPattern, 9);
    altSymbol.className = "geIcon";
    altSymbol.style.width = "22px";
    var solid = document.createElement("div");
    solid.style.width = "85px";
    solid.style.height = "1px";
    solid.style.borderBottom = "1px solid " + this.defaultStrokeColor;
    solid.style.marginBottom = "9px";
    symbol.appendChild(solid);
    var altSolid = document.createElement("div");
    altSolid.style.width = "23px";
    altSolid.style.height = "1px";
    altSolid.style.borderBottom = "1px solid " + this.defaultStrokeColor;
    altSolid.style.marginBottom = "9px";
    altSymbol.appendChild(altSolid);
    pattern.style.height = "15px";
    altPattern.style.height = "15px";
    edgeShape.style.height = "15px";
    edgeStyle.style.height = "17px";
    lineStart.style.marginLeft = "3px";
    lineStart.style.height = "17px";
    lineEnd.style.marginLeft = "3px";
    lineEnd.style.height = "17px";
    container.appendChild(colorPanel);
    container.appendChild(altStylePanel);
    container.appendChild(stylePanel);
    var arrowPanel = stylePanel.cloneNode(false);
    arrowPanel.style.paddingBottom = "6px";
    arrowPanel.style.paddingTop = "4px";
    arrowPanel.style.fontWeight = "normal";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.marginLeft = "3px";
    span.style.marginBottom = "12px";
    span.style.marginTop = "2px";
    span.style.fontWeight = "normal";
    span.style.width = "76px";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("lineend"));
    arrowPanel.appendChild(span);
    var endSpacingUpdate, endSizeUpdate;
    var endSpacing = this.addUnitInput(arrowPanel, "pt", 74, 33, function () {
        endSpacingUpdate.apply(this, arguments);
    });
    var endSize = this.addUnitInput(arrowPanel, "pt", 20, 33, function () {
        endSizeUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(arrowPanel);
    var spacer = document.createElement("div");
    spacer.style.height = "8px";
    arrowPanel.appendChild(spacer);
    span = span.cloneNode(false);
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("linestart"));
    arrowPanel.appendChild(span);
    var startSpacingUpdate, startSizeUpdate;
    var startSpacing = this.addUnitInput(arrowPanel, "pt", 74, 33, function () {
        startSpacingUpdate.apply(this, arguments);
    });
    var startSize = this.addUnitInput(arrowPanel, "pt", 20, 33, function () {
        startSizeUpdate.apply(this, arguments);
    });
    mxgraph_1.mxUtils.br(arrowPanel);
    this.addLabel(arrowPanel, mxgraph_1.mxResources.get("spacing"), 74, 50);
    this.addLabel(arrowPanel, mxgraph_1.mxResources.get("size"), 20, 50);
    mxgraph_1.mxUtils.br(arrowPanel);
    var perimeterPanel = colorPanel.cloneNode(false);
    perimeterPanel.style.fontWeight = "normal";
    perimeterPanel.style.position = "relative";
    perimeterPanel.style.paddingLeft = "16px";
    perimeterPanel.style.marginBottom = "2px";
    perimeterPanel.style.marginTop = "6px";
    perimeterPanel.style.borderWidth = "0px";
    perimeterPanel.style.paddingBottom = "18px";
    var span = document.createElement("div");
    span.style.position = "absolute";
    span.style.marginLeft = "3px";
    span.style.marginBottom = "12px";
    span.style.marginTop = "1px";
    span.style.fontWeight = "normal";
    span.style.width = "120px";
    mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("perimeter"));
    perimeterPanel.appendChild(span);
    var perimeterUpdate;
    var perimeterSpacing = this.addUnitInput(perimeterPanel, "pt", 20, 41, function () {
        perimeterUpdate.apply(this, arguments);
    });
    if (ss.edges.length == graph.getSelectionCount()) {
        container.appendChild(stylePanel2);
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            mxgraph_1.mxUtils.br(container);
            mxgraph_1.mxUtils.br(container);
        }
        container.appendChild(arrowPanel);
    }
    else if (ss.vertices.length == graph.getSelectionCount()) {
        if (mxgraph_1.mxClient.IS_QUIRKS) {
            mxgraph_1.mxUtils.br(container);
        }
        container.appendChild(perimeterPanel);
    }
    var listener = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
        ss = this.format.getSelectionState();
        if (force || document.activeElement != input) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STROKEWIDTH, 1));
            input.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != altInput) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STROKEWIDTH, 1));
            altInput.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        styleSelect.style.visibility =
            ss.style.shape == "connector" || ss.style.shape == "filledEdge" ? "" : "hidden";
        if (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_CURVED, null) == "1") {
            styleSelect.value = "curved";
        }
        else if (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ROUNDED, null) == "1") {
            styleSelect.value = "rounded";
        }
        if (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_DASHED, null) == "1") {
            if (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_DASH_PATTERN, null) == null) {
                solid.style.borderBottom = "1px dashed " + this.defaultStrokeColor;
            }
            else {
                solid.style.borderBottom = "1px dotted " + this.defaultStrokeColor;
            }
        }
        else {
            solid.style.borderBottom = "1px solid " + this.defaultStrokeColor;
        }
        altSolid.style.borderBottom = solid.style.borderBottom;
        // Updates toolbar icon for edge style
        var edgeStyleDiv = edgeStyle.getElementsByTagName("div")[0];
        var es = mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_EDGE, null);
        if (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_NOEDGESTYLE, null) == "1") {
            es = null;
        }
        if (es == "orthogonalEdgeStyle" &&
            mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_CURVED, null) == "1") {
            edgeStyleDiv.className = "geSprite geSprite-curved";
        }
        else if (es == "straight" || es == "none" || es == null) {
            edgeStyleDiv.className = "geSprite geSprite-straight";
        }
        else if (es == "entityRelationEdgeStyle") {
            edgeStyleDiv.className = "geSprite geSprite-entity";
        }
        else if (es == "elbowEdgeStyle") {
            edgeStyleDiv.className =
                "geSprite " +
                    (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ELBOW, null) == "vertical"
                        ? "geSprite-verticalelbow"
                        : "geSprite-horizontalelbow");
        }
        else if (es == "isometricEdgeStyle") {
            edgeStyleDiv.className =
                "geSprite " +
                    (mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ELBOW, null) == "vertical"
                        ? "geSprite-verticalisometric"
                        : "geSprite-horizontalisometric");
        }
        else {
            edgeStyleDiv.className = "geSprite geSprite-orthogonal";
        }
        // Updates icon for edge shape
        var edgeShapeDiv = edgeShape.getElementsByTagName("div")[0];
        if (ss.style.shape == "link") {
            edgeShapeDiv.className = "geSprite geSprite-linkedge";
        }
        else if (ss.style.shape == "flexArrow") {
            edgeShapeDiv.className = "geSprite geSprite-arrow";
        }
        else if (ss.style.shape == "arrow") {
            edgeShapeDiv.className = "geSprite geSprite-simplearrow";
        }
        else {
            edgeShapeDiv.className = "geSprite geSprite-connection";
        }
        if (ss.edges.length == graph.getSelectionCount()) {
            altStylePanel.style.display = "";
            stylePanel.style.display = "none";
        }
        else {
            altStylePanel.style.display = "none";
            stylePanel.style.display = "";
        }
        function updateArrow(marker, fill, elt, prefix) {
            var markerDiv = elt.getElementsByTagName("div")[0];
            markerDiv.className = ui.getCssClassForMarker(prefix, ss.style.shape, marker, fill);
            if (markerDiv.className == "geSprite geSprite-noarrow") {
                markerDiv.innerHTML = mxgraph_1.mxUtils.htmlEntities(mxgraph_1.mxResources.get("none"));
                markerDiv.style.backgroundImage = "none";
                markerDiv.style.verticalAlign = "top";
                markerDiv.style.marginTop = "5px";
                markerDiv.style.fontSize = "10px";
                markerDiv.style.filter = "none";
                markerDiv.style.color = StyleFormatPanel.prototype.defaultStrokeColor;
                markerDiv.nextSibling.style.marginTop = "0px";
            }
            return markerDiv;
        }
        var sourceDiv = updateArrow(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STARTARROW, null), mxgraph_1.mxUtils.getValue(ss.style, "startFill", "1"), lineStart, "start");
        var targetDiv = updateArrow(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ENDARROW, null), mxgraph_1.mxUtils.getValue(ss.style, "endFill", "1"), lineEnd, "end");
        // Special cases for markers
        if (ss.style.shape == "arrow") {
            sourceDiv.className = "geSprite geSprite-noarrow";
            targetDiv.className = "geSprite geSprite-endblocktrans";
        }
        else if (ss.style.shape == "link") {
            sourceDiv.className = "geSprite geSprite-noarrow";
            targetDiv.className = "geSprite geSprite-noarrow";
        }
        mxgraph_1.mxUtils.setOpacity(edgeStyle, ss.style.shape == "arrow" ? 30 : 100);
        if (ss.style.shape != "connector" &&
            ss.style.shape != "flexArrow" &&
            ss.style.shape != "filledEdge") {
            mxgraph_1.mxUtils.setOpacity(lineStart, 30);
            mxgraph_1.mxUtils.setOpacity(lineEnd, 30);
        }
        else {
            mxgraph_1.mxUtils.setOpacity(lineStart, 100);
            mxgraph_1.mxUtils.setOpacity(lineEnd, 100);
        }
        if (force || document.activeElement != startSize) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_STARTSIZE, mxgraph_1.mxConstants.DEFAULT_MARKERSIZE));
            startSize.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != startSpacing) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_SOURCE_PERIMETER_SPACING, 0));
            startSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != endSize) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_ENDSIZE, mxgraph_1.mxConstants.DEFAULT_MARKERSIZE));
            endSize.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != startSpacing) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_TARGET_PERIMETER_SPACING, 0));
            endSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
        if (force || document.activeElement != perimeterSpacing) {
            var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, mxgraph_1.mxConstants.STYLE_PERIMETER_SPACING, 0));
            perimeterSpacing.value = isNaN(tmp) ? "" : tmp + " pt";
        }
    });
    startSizeUpdate = this.installInputHandler(startSize, mxgraph_1.mxConstants.STYLE_STARTSIZE, mxgraph_1.mxConstants.DEFAULT_MARKERSIZE, 0, 999, " pt");
    startSpacingUpdate = this.installInputHandler(startSpacing, mxgraph_1.mxConstants.STYLE_SOURCE_PERIMETER_SPACING, 0, -999, 999, " pt");
    endSizeUpdate = this.installInputHandler(endSize, mxgraph_1.mxConstants.STYLE_ENDSIZE, mxgraph_1.mxConstants.DEFAULT_MARKERSIZE, 0, 999, " pt");
    endSpacingUpdate = this.installInputHandler(endSpacing, mxgraph_1.mxConstants.STYLE_TARGET_PERIMETER_SPACING, 0, -999, 999, " pt");
    perimeterUpdate = this.installInputHandler(perimeterSpacing, mxgraph_1.mxConstants.STYLE_PERIMETER_SPACING, 0, 0, 999, " pt");
    this.addKeyHandler(input, listener);
    this.addKeyHandler(startSize, listener);
    this.addKeyHandler(startSpacing, listener);
    this.addKeyHandler(endSize, listener);
    this.addKeyHandler(endSpacing, listener);
    this.addKeyHandler(perimeterSpacing, listener);
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
    return container;
};
/**
 * Adds UI for configuring line jumps.
 */
StyleFormatPanel.prototype.addLineJumps = function (container) {
    var ss = this.format.getSelectionState();
    if (Graph_1.Graph.lineJumpsEnabled && ss.edges.length > 0 && ss.vertices.length == 0 && ss.lineJumps) {
        container.style.padding = "8px 0px 24px 18px";
        var ui_1 = this.editorUi;
        var editor = ui_1.editor;
        var graph_1 = editor.graph;
        var span = document.createElement("div");
        span.style.position = "absolute";
        span.style.fontWeight = "bold";
        span.style.width = "80px";
        mxgraph_1.mxUtils.write(span, mxgraph_1.mxResources.get("lineJumps"));
        container.appendChild(span);
        var styleSelect_1 = document.createElement("select");
        styleSelect_1.style.position = "absolute";
        styleSelect_1.style.marginTop = "-2px";
        styleSelect_1.style.right = "76px";
        styleSelect_1.style.width = "62px";
        var styles = ["none", "arc", "gap", "sharp"];
        for (var i = 0; i < styles.length; i++) {
            var styleOption = document.createElement("option");
            styleOption.setAttribute("value", styles[i]);
            mxgraph_1.mxUtils.write(styleOption, mxgraph_1.mxResources.get(styles[i]));
            styleSelect_1.appendChild(styleOption);
        }
        mxgraph_1.mxEvent.addListener(styleSelect_1, "change", function (evt) {
            graph_1.getModel().beginUpdate();
            try {
                graph_1.setCellStyles("jumpStyle", styleSelect_1.value, graph_1.getSelectionCells());
                ui_1.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", ["jumpStyle"], "values", [styleSelect_1.value], "cells", graph_1.getSelectionCells()));
            }
            finally {
                graph_1.getModel().endUpdate();
            }
            mxgraph_1.mxEvent.consume(evt);
        });
        // Stops events from bubbling to color option event handler
        mxgraph_1.mxEvent.addListener(styleSelect_1, "click", function (evt) {
            mxgraph_1.mxEvent.consume(evt);
        });
        container.appendChild(styleSelect_1);
        var jumpSizeUpdate_1;
        var jumpSize_1 = this.addUnitInput(container, "pt", 22, 33, function () {
            jumpSizeUpdate_1.apply(this, arguments);
        });
        jumpSizeUpdate_1 = this.installInputHandler(jumpSize_1, "jumpSize", Graph_1.Graph.defaultJumpSize, 0, 999, " pt");
        var listener_3 = mxgraph_1.mxUtils.bind(this, function (sender, evt, force) {
            ss = this.format.getSelectionState();
            styleSelect_1.value = mxgraph_1.mxUtils.getValue(ss.style, "jumpStyle", "none");
            if (force || document.activeElement != jumpSize_1) {
                var tmp = parseInt(mxgraph_1.mxUtils.getValue(ss.style, "jumpSize", Graph_1.Graph.defaultJumpSize));
                jumpSize_1.value = isNaN(tmp) ? "" : tmp + " pt";
            }
        });
        this.addKeyHandler(jumpSize_1, listener_3);
        graph_1.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener_3);
        this.listeners.push({
            destroy: function () {
                graph_1.getModel().removeListener(listener_3);
            },
        });
        listener_3();
    }
    else {
        container.style.display = "none";
    }
    return container;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addEffects = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    var ss = this.format.getSelectionState();
    div.style.paddingTop = "0px";
    div.style.paddingBottom = "2px";
    var table = document.createElement("table");
    if (mxgraph_1.mxClient.IS_QUIRKS) {
        table.style.fontSize = "1em";
    }
    table.style.width = "100%";
    table.style.fontWeight = "bold";
    table.style.paddingRight = "20px";
    var tbody = document.createElement("tbody");
    var row = document.createElement("tr");
    row.style.padding = "0px";
    var left = document.createElement("td");
    left.style.padding = "0px";
    left.style.width = "50%";
    left.setAttribute("valign", "top");
    var right = left.cloneNode(true);
    right.style.paddingLeft = "8px";
    row.appendChild(left);
    row.appendChild(right);
    tbody.appendChild(row);
    table.appendChild(tbody);
    div.appendChild(table);
    var current = left;
    var count = 0;
    var addOption = mxgraph_1.mxUtils.bind(this, function (label, key, defaultValue) {
        var opt = this.createCellOption(label, key, defaultValue);
        opt.style.width = "100%";
        current.appendChild(opt);
        current = current == left ? right : left;
        count++;
    });
    var listener = mxgraph_1.mxUtils.bind(this, function () {
        ss = this.format.getSelectionState();
        left.innerHTML = "";
        right.innerHTML = "";
        current = left;
        if (ss.rounded) {
            addOption(mxgraph_1.mxResources.get("rounded"), mxgraph_1.mxConstants.STYLE_ROUNDED, 0);
        }
        if (ss.style.shape == "swimlane") {
            addOption(mxgraph_1.mxResources.get("divider"), "swimlaneLine", 1);
        }
        if (!ss.containsImage) {
            addOption(mxgraph_1.mxResources.get("shadow"), mxgraph_1.mxConstants.STYLE_SHADOW, 0);
        }
        if (ss.glass) {
            addOption(mxgraph_1.mxResources.get("glass"), mxgraph_1.mxConstants.STYLE_GLASS, 0);
        }
        if (ss.comic) {
            addOption(mxgraph_1.mxResources.get("comic"), "comic", 0);
        }
        if (count == 0) {
            div.style.display = "none";
        }
    });
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    listener();
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
StyleFormatPanel.prototype.addStyleOps = function (div) {
    div.style.paddingTop = "10px";
    div.style.paddingBottom = "10px";
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("setAsDefaultStyle"), mxgraph_1.mxUtils.bind(this, function () {
        this.editorUi.actions.get("setAsDefaultStyle").funct();
    }));
    btn.setAttribute("title", mxgraph_1.mxResources.get("setAsDefaultStyle") +
        " (" +
        this.editorUi.actions.get("setAsDefaultStyle").shortcut +
        ")");
    btn.style.width = "202px";
    div.appendChild(btn);
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
function DiagramFormatPanel(format, editorUi, container) {
    BaseFormatPanel.call(this, format, editorUi, container);
    this.init();
}
exports.DiagramFormatPanel = DiagramFormatPanel;
mxgraph_1.mxUtils.extend(DiagramFormatPanel, BaseFormatPanel);
/**
 * Switch to disable page view.
 */
DiagramFormatPanel.showPageView = true;
/**
 * Specifies if the background image option should be shown. Default is true.
 */
DiagramFormatPanel.prototype.showBackgroundImageOption = true;
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.init = function () {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    this.container.appendChild(this.addView(this.createPanel()));
    if (graph.isEnabled()) {
        this.container.appendChild(this.addOptions(this.createPanel()));
        this.container.appendChild(this.addPaperSize(this.createPanel()));
        this.container.appendChild(this.addStyleOps(this.createPanel()));
    }
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.addView = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("view")));
    // Grid
    this.addGridOption(div);
    // Page View
    if (DiagramFormatPanel.showPageView) {
        div.appendChild(this.createOption(mxgraph_1.mxResources.get("pageView"), function () {
            return graph.pageVisible;
        }, function () {
            ui.actions.get("pageView").funct();
        }, {
            install: function (apply) {
                this.listener = function () {
                    apply(graph.pageVisible);
                };
                ui.addListener("pageViewChanged", this.listener);
            },
            destroy: function () {
                ui.removeListener(this.listener);
            },
        }));
    }
    // - Background Option
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.addOptions = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("options")));
    if (graph.isEnabled()) {
        // Connection arrows
        div.appendChild(this.createOption(mxgraph_1.mxResources.get("connectionArrows"), function () {
            return graph.connectionArrowsEnabled;
        }, function () {
            ui.actions.get("connectionArrows").funct();
        }, {
            install: function (apply) {
                this.listener = function () {
                    apply(graph.connectionArrowsEnabled);
                };
                ui.addListener("connectionArrowsChanged", this.listener);
            },
            destroy: function () {
                ui.removeListener(this.listener);
            },
        }));
        // Connection points
        div.appendChild(this.createOption(mxgraph_1.mxResources.get("connectionPoints"), function () {
            return graph.connectionHandler.isEnabled();
        }, function () {
            ui.actions.get("connectionPoints").funct();
        }, {
            install: function (apply) {
                this.listener = function () {
                    apply(graph.connectionHandler.isEnabled());
                };
                ui.addListener("connectionPointsChanged", this.listener);
            },
            destroy: function () {
                ui.removeListener(this.listener);
            },
        }));
        // Guides
        div.appendChild(this.createOption(mxgraph_1.mxResources.get("guides"), function () {
            return graph.graphHandler.guidesEnabled;
        }, function () {
            ui.actions.get("guides").funct();
        }, {
            install: function (apply) {
                this.listener = function () {
                    apply(graph.graphHandler.guidesEnabled);
                };
                ui.addListener("guidesEnabledChanged", this.listener);
            },
            destroy: function () {
                ui.removeListener(this.listener);
            },
        }));
    }
    return div;
};
/**
 *
 */
DiagramFormatPanel.prototype.addGridOption = function (container) {
    var fPanel = this;
    var ui = this.editorUi;
    var graph = ui.editor.graph;
    var input = document.createElement("input");
    input.style.position = "absolute";
    input.style.textAlign = "right";
    input.style.width = "38px";
    input.value = this.inUnit(graph.getGridSize()) + " " + this.getUnit();
    var stepper = this.createStepper(input, update, this.getUnitStep(), null, null, null, this.isFloatUnit());
    input.style.display = graph.isGridEnabled() ? "" : "none";
    stepper.style.display = input.style.display;
    mxgraph_1.mxEvent.addListener(input, "keydown", function (e) {
        if (e.keyCode == 13) {
            graph.container.focus();
            mxgraph_1.mxEvent.consume(e);
        }
        else if (e.keyCode == 27) {
            input.value = graph.getGridSize();
            graph.container.focus();
            mxgraph_1.mxEvent.consume(e);
        }
    });
    function update(evt) {
        var value = fPanel.isFloatUnit() ? parseFloat(input.value) : parseInt(input.value);
        value = fPanel.fromUnit(Math.max(fPanel.inUnit(1), isNaN(value) ? fPanel.inUnit(10) : value));
        if (value != graph.getGridSize()) {
            graph.setGridSize(value);
        }
        input.value = fPanel.inUnit(value) + " " + fPanel.getUnit();
        mxgraph_1.mxEvent.consume(evt);
    }
    mxgraph_1.mxEvent.addListener(input, "blur", update);
    mxgraph_1.mxEvent.addListener(input, "change", update);
    var unitChangeListener = function () {
        input.value = fPanel.inUnit(graph.getGridSize()) + " " + fPanel.getUnit();
        fPanel.format.refresh();
    };
    graph.view.addListener("unitChanged", unitChangeListener);
    this.listeners.push({
        destroy: function () {
            graph.view.removeListener(unitChangeListener);
        },
    });
    input.style.marginTop = "-2px";
    input.style.right = "84px";
    stepper.style.marginTop = "-2px";
    stepper.style.right = "72px";
    var panel = this.createColorOption(mxgraph_1.mxResources.get("grid"), function () {
        var color = graph.view.gridColor;
        return graph.isGridEnabled() ? color : null;
    }, function (color) {
        var enabled = graph.isGridEnabled();
        if (color == mxgraph_1.mxConstants.NONE) {
            graph.setGridEnabled(false);
        }
        else {
            graph.setGridEnabled(true);
            ui.setGridColor(color);
        }
        input.style.display = graph.isGridEnabled() ? "" : "none";
        stepper.style.display = input.style.display;
        if (enabled != graph.isGridEnabled()) {
            ui.fireEvent(new mxgraph_1.mxEventObject("gridEnabledChanged"));
        }
    }, "#e0e0e0", {
        install: function (apply) {
            this.listener = function () {
                apply(graph.isGridEnabled() ? graph.view.gridColor : null);
            };
            ui.addListener("gridColorChanged", this.listener);
            ui.addListener("gridEnabledChanged", this.listener);
        },
        destroy: function () {
            ui.removeListener(this.listener);
        },
    });
    panel.appendChild(input);
    panel.appendChild(stepper);
    container.appendChild(panel);
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.addDocumentProperties = function (div) {
    // Hook for subclassers
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("options")));
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.addPaperSize = function (div) {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    div.appendChild(this.createTitle(mxgraph_1.mxResources.get("paperSize")));
    var accessor = Editor_1.PageSetupDialog.addPageFormatPanel(div, "formatpanel", graph.pageFormat, function (pageFormat) {
        if (graph.pageFormat == null ||
            graph.pageFormat.width != pageFormat.width ||
            graph.pageFormat.height != pageFormat.height) {
            var change = new PageSetup_1.PageSetup(ui, null, null, pageFormat);
            change.ignoreColor = true;
            change.ignoreImage = true;
            graph.model.execute(change);
        }
    });
    this.addKeyHandler(accessor.widthInput, function () {
        accessor.set(graph.pageFormat);
    });
    this.addKeyHandler(accessor.heightInput, function () {
        accessor.set(graph.pageFormat);
    });
    var listener = function () {
        accessor.set(graph.pageFormat);
    };
    ui.addListener("pageFormatChanged", listener);
    this.listeners.push({
        destroy: function () {
            ui.removeListener(listener);
        },
    });
    graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, listener);
    this.listeners.push({
        destroy: function () {
            graph.getModel().removeListener(listener);
        },
    });
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.addStyleOps = function (div) {
    // - Edit Data Option
    var btn = mxgraph_1.mxUtils.button(mxgraph_1.mxResources.get("clearDefaultStyle"), mxgraph_1.mxUtils.bind(this, function () {
        this.editorUi.actions.get("clearDefaultStyle").funct();
    }));
    btn.setAttribute("title", mxgraph_1.mxResources.get("clearDefaultStyle") +
        " (" +
        this.editorUi.actions.get("clearDefaultStyle").shortcut +
        ")");
    btn.style.width = "202px";
    div.appendChild(btn);
    return div;
};
/**
 * Adds the label menu items to the given menu and parent.
 */
DiagramFormatPanel.prototype.destroy = function () {
    BaseFormatPanel.prototype.destroy.apply(this, arguments);
    if (this.gridEnabledListener) {
        this.editorUi.removeListener(this.gridEnabledListener);
        this.gridEnabledListener = null;
    }
};
