"use strict";
/* eslint-disable */
/* eslint-enable no-undef, prettier/prettier, no-unused-vars */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Action = exports.Actions = void 0;
var mxgraph_1 = require("../../core/mxgraph");
var Dialogs_1 = require("./Dialogs");
var PageSetup_1 = require("./PageSetup");
var constant_1 = require("../utils/constant");
/**
 * Copyright (c) 2006-2020, JGraph Ltd
 * Copyright (c) 2006-2020, draw.io AG
 *
 * Constructs the actions object for the given UI.
 */
function Actions(editorUi) {
    this.editorUi = editorUi;
    this.actions = new Object();
    this.init();
}
exports.Actions = Actions;
/**
 * Adds the default actions.
 */
Actions.prototype.init = function () {
    var ui = this.editorUi;
    var editor = ui.editor;
    var graph = editor.graph;
    var isGraphEnabled = function () {
        return Action.prototype.isEnabled.apply(this, arguments) && graph.isEnabled();
    };
    // - File actions
    // Edit actions
    this.addAction("undo", function () {
        ui.undo();
    }, null, "sprite-undo", constant_1.EDITOR.CTRL_KEY + "+Z");
    this.addAction("redo", function () {
        ui.redo();
    }, null, "sprite-redo", !mxgraph_1.mxClient.IS_WIN ? constant_1.EDITOR.CTRL_KEY + "+Shift+Z" : constant_1.EDITOR.CTRL_KEY + "+Y");
    this.addAction("cut", function () {
        mxgraph_1.mxClipboard.cut(graph);
    }, null, "sprite-cut", constant_1.EDITOR.CTRL_KEY + "+X");
    this.addAction("copy", function () {
        try {
            mxgraph_1.mxClipboard.copy(graph);
        }
        catch (e) {
            ui.handleError(e);
        }
    }, null, "sprite-copy", constant_1.EDITOR.CTRL_KEY + "+C");
    this.addAction("paste", function () {
        if (graph.isEnabled() && !graph.isCellLocked(graph.getDefaultParent())) {
            mxgraph_1.mxClipboard.paste(graph);
        }
    }, false, "sprite-paste", constant_1.EDITOR.CTRL_KEY + "+V");
    this.addAction("pasteHere", function () {
        if (graph.isEnabled() && !graph.isCellLocked(graph.getDefaultParent())) {
            graph.getModel().beginUpdate();
            try {
                var cells = mxgraph_1.mxClipboard.paste(graph);
                if (cells != null) {
                    var includeEdges = true;
                    for (var i = 0; i < cells.length && includeEdges; i++) {
                        includeEdges = includeEdges && graph.model.isEdge(cells[i]);
                    }
                    var t = graph.view.translate;
                    var s = graph.view.scale;
                    var dx = t.x;
                    var dy = t.y;
                    var bb = null;
                    if (cells.length == 1 && includeEdges) {
                        var geo = graph.getCellGeometry(cells[0]);
                        if (geo != null) {
                            bb = geo.getTerminalPoint(true);
                        }
                    }
                    bb = bb != null ? bb : graph.getBoundingBoxFromGeometry(cells, includeEdges);
                    if (bb != null) {
                        var x = Math.round(graph.snap(graph.popupMenuHandler.triggerX / s - dx));
                        var y = Math.round(graph.snap(graph.popupMenuHandler.triggerY / s - dy));
                        graph.cellsMoved(cells, x - bb.x, y - bb.y);
                    }
                }
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    });
    this.addAction("copySize", function () {
        var cell = graph.getSelectionCell();
        if (graph.isEnabled() && cell != null && graph.getModel().isVertex(cell)) {
            var geo = graph.getCellGeometry(cell);
            if (geo != null) {
                ui.copiedSize = new mxgraph_1.mxRectangle(geo.x, geo.y, geo.width, geo.height);
            }
        }
    }, null, null, "Alt+Shift+X");
    this.addAction("pasteSize", function () {
        if (graph.isEnabled() && !graph.isSelectionEmpty() && ui.copiedSize != null) {
            graph.getModel().beginUpdate();
            try {
                var cells = graph.getSelectionCells();
                for (var i = 0; i < cells.length; i++) {
                    if (graph.getModel().isVertex(cells[i])) {
                        var geo = graph.getCellGeometry(cells[i]);
                        if (geo != null) {
                            geo = geo.clone();
                            geo.width = ui.copiedSize.width;
                            geo.height = ui.copiedSize.height;
                            graph.getModel().setGeometry(cells[i], geo);
                        }
                    }
                }
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    }, null, null, "Alt+Shift+V");
    function deleteCells(includeEdges) {
        // Cancels interactive operations
        graph.escape();
        var select = graph.deleteCells(graph.getDeletableCells(graph.getSelectionCells()), includeEdges);
        if (select != null) {
            graph.setSelectionCells(select);
        }
    }
    this.addAction("delete", function (evt) {
        deleteCells(evt != null && mxgraph_1.mxEvent.isShiftDown(evt));
    }, null, null, "Delete");
    this.addAction("deleteAll", function () {
        deleteCells(true);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Delete");
    this.addAction("duplicate", function () {
        try {
            graph.setSelectionCells(graph.duplicateCells());
        }
        catch (e) {
            ui.handleError(e);
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+D");
    this.put("turn", new Action(mxgraph_1.mxResources.get("turn") + " / " + mxgraph_1.mxResources.get("reverse"), function (evt) {
        graph.turnShapes(graph.getSelectionCells(), evt != null ? mxgraph_1.mxEvent.isShiftDown(evt) : false);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+R"));
    this.addAction("selectVertices", function () {
        graph.selectVertices(null, true);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+I");
    this.addAction("selectEdges", function () {
        graph.selectEdges();
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+E");
    this.addAction("selectAll", function () {
        graph.selectAll(null, true);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+A");
    this.addAction("selectNone", function () {
        graph.clearSelection();
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+A");
    this.addAction("lockUnlock", function () {
        if (!graph.isSelectionEmpty()) {
            graph.getModel().beginUpdate();
            try {
                var defaultValue = graph.isCellMovable(graph.getSelectionCell()) ? 1 : 0;
                graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_MOVABLE, defaultValue);
                graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_RESIZABLE, defaultValue);
                graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_ROTATABLE, defaultValue);
                graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_DELETABLE, defaultValue);
                graph.toggleCellStyles(mxgraph_1.mxConstants.STYLE_EDITABLE, defaultValue);
                graph.toggleCellStyles("connectable", defaultValue);
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+L");
    // Navigation actions
    this.addAction("home", function () {
        graph.home();
    }, null, null, "Shift+Home");
    this.addAction("exitGroup", function () {
        graph.exitGroup();
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+Home");
    this.addAction("enterGroup", function () {
        graph.enterGroup();
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+End");
    this.addAction("collapse", function () {
        graph.foldCells(true);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Home");
    this.addAction("expand", function () {
        graph.foldCells(false);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+End");
    // Arrange actions
    this.addAction("toFront", function () {
        graph.orderCells(false);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+F");
    this.addAction("toBack", function () {
        graph.orderCells(true);
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+B");
    this.addAction("group", function () {
        if (graph.getSelectionCount() == 1) {
            graph.setCellStyles("container", "1");
        }
        else {
            graph.setSelectionCell(graph.groupCells(null, 0));
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+G");
    this.addAction("ungroup", function () {
        if (graph.getSelectionCount() == 1 &&
            graph.getModel().getChildCount(graph.getSelectionCell()) == 0) {
            graph.setCellStyles("container", "0");
        }
        else {
            graph.setSelectionCells(graph.ungroupCells());
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+U");
    this.addAction("removeFromGroup", function () {
        graph.removeCellsFromParent();
    });
    // Adds action
    this.addAction("edit", function () {
        if (graph.isEnabled()) {
            graph.startEditingAtCell();
        }
    }, null, null, "F2/Enter");
    // - Edit Edit Tooltip Action
    // - Insert Image Action
    // - Insert Link Action
    this.addAction("autosize", function () {
        var cells = graph.getSelectionCells();
        if (cells != null) {
            graph.getModel().beginUpdate();
            try {
                for (var i = 0; i < cells.length; i++) {
                    var cell = cells[i];
                    if (graph.getModel().getChildCount(cell)) {
                        graph.updateGroupBounds([cell], 20);
                    }
                    else {
                        var state = graph.view.getState(cell);
                        var geo = graph.getCellGeometry(cell);
                        if (graph.getModel().isVertex(cell) &&
                            state != null &&
                            state.text != null &&
                            geo != null &&
                            graph.isWrapping(cell)) {
                            geo = geo.clone();
                            geo.height = state.text.boundingBox.height / graph.view.scale;
                            graph.getModel().setGeometry(cell, geo);
                        }
                        else {
                            graph.updateCellSize(cell);
                        }
                    }
                }
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+Y");
    this.addAction("formattedText", function () {
        var refState = graph.getView().getState(graph.getSelectionCell());
        if (refState != null) {
            graph.stopEditing();
            var value = refState.style["html"] == "1" ? null : "1";
            graph.getModel().beginUpdate();
            try {
                var cells = graph.getSelectionCells();
                for (var i = 0; i < cells.length; i++) {
                    var state = graph.getView().getState(cells[i]);
                    if (state != null) {
                        var html = mxgraph_1.mxUtils.getValue(state.style, "html", "0");
                        if (html == "1" && value == null) {
                            var label = graph.convertValueToString(state.cell);
                            if (mxgraph_1.mxUtils.getValue(state.style, "nl2Br", "1") != "0") {
                                // Removes newlines from HTML and converts breaks to newlines
                                // to match the HTML output in plain text
                                label = label.replace(/\n/g, "").replace(/<br\s*.?>/g, "\n");
                            }
                            // Removes HTML tags
                            var temp = document.createElement("div");
                            temp.innerHTML = graph.sanitizeHtml(label);
                            label = mxgraph_1.mxUtils.extractTextWithWhitespace(temp.childNodes);
                            graph.cellLabelChanged(state.cell, label);
                            graph.setCellStyles("html", value, [cells[i]]);
                        }
                        else if (html == "0" && value == "1") {
                            // Converts HTML tags to text
                            var label = mxgraph_1.mxUtils.htmlEntities(graph.convertValueToString(state.cell), false);
                            if (mxgraph_1.mxUtils.getValue(state.style, "nl2Br", "1") != "0") {
                                // Converts newlines in plain text to breaks in HTML
                                // to match the plain text output
                                label = label.replace(/\n/g, "<br/>");
                            }
                            graph.cellLabelChanged(state.cell, graph.sanitizeHtml(label));
                            graph.setCellStyles("html", value, [cells[i]]);
                        }
                    }
                }
                ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", ["html"], "values", [value != null ? value : "0"], "cells", cells));
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    });
    this.addAction("wordWrap", function () {
        var state = graph.getView().getState(graph.getSelectionCell());
        var value = "wrap";
        graph.stopEditing();
        if (state != null && state.style[mxgraph_1.mxConstants.STYLE_WHITE_SPACE] == "wrap") {
            value = null;
        }
        graph.setCellStyles(mxgraph_1.mxConstants.STYLE_WHITE_SPACE, value);
    });
    this.addAction("rotation", function () {
        var value = "0";
        var state = graph.getView().getState(graph.getSelectionCell());
        if (state != null) {
            value = state.style[mxgraph_1.mxConstants.STYLE_ROTATION] || value;
        }
        var dlg = new Dialogs_1.FilenameDialog(ui, value, mxgraph_1.mxResources.get("apply"), function (newValue) {
            if (newValue != null && newValue.length > 0) {
                graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ROTATION, newValue);
            }
        }, mxgraph_1.mxResources.get("enterValue") + " (" + mxgraph_1.mxResources.get("rotation") + " 0-360)");
        ui.showDialog(dlg.container, 375, 80, true, true);
        dlg.init();
    });
    // View actions
    this.addAction("resetView", function () {
        graph.zoomTo(1);
        ui.resetScrollbars();
    }, null, null, "Home");
    this.addAction("zoomIn", function () {
        if (graph.isFastZoomEnabled()) {
            graph.lazyZoom(true, true, ui.buttonZoomDelay);
        }
        else {
            graph.zoomIn();
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + " + (Numpad) / Alt+Mousewheel");
    this.addAction("zoomOut", function () {
        if (graph.isFastZoomEnabled()) {
            graph.lazyZoom(false, true, ui.buttonZoomDelay);
        }
        else {
            graph.zoomOut();
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + " - (Numpad) / Alt+Mousewheel");
    this.addAction("fitWindow", function () {
        var bounds = graph.isSelectionEmpty()
            ? graph.getGraphBounds()
            : graph.getBoundingBox(graph.getSelectionCells());
        var t = graph.view.translate;
        var s = graph.view.scale;
        bounds.width /= s;
        bounds.height /= s;
        bounds.x = bounds.x / s - t.x;
        bounds.y = bounds.y / s - t.y;
        var cw = graph.container.clientWidth - 10;
        var ch = graph.container.clientHeight - 10;
        var scale = Math.floor(20 * Math.min(cw / bounds.width, ch / bounds.height)) / 20;
        graph.zoomTo(scale);
        if (mxgraph_1.mxUtils.hasScrollbars(graph.container)) {
            graph.container.scrollTop =
                (bounds.y + t.y) * scale - Math.max((ch - bounds.height * scale) / 2 + 5, 0);
            graph.container.scrollLeft =
                (bounds.x + t.x) * scale - Math.max((cw - bounds.width * scale) / 2 + 5, 0);
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+H");
    this.addAction("fitPage", mxgraph_1.mxUtils.bind(this, function () {
        if (!graph.pageVisible) {
            this.get("pageView").funct();
        }
        var fmt = graph.pageFormat;
        var ps = graph.pageScale;
        var cw = graph.container.clientWidth - 10;
        var ch = graph.container.clientHeight - 10;
        var scale = Math.floor(20 * Math.min(cw / fmt.width / ps, ch / fmt.height / ps)) / 20;
        graph.zoomTo(scale);
        if (mxgraph_1.mxUtils.hasScrollbars(graph.container)) {
            var pad = graph.getPagePadding();
            graph.container.scrollTop = pad.y * graph.view.scale - 1;
            graph.container.scrollLeft =
                Math.min(pad.x * graph.view.scale, (graph.container.scrollWidth - graph.container.clientWidth) / 2) - 1;
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+J");
    this.addAction("fitTwoPages", mxgraph_1.mxUtils.bind(this, function () {
        if (!graph.pageVisible) {
            this.get("pageView").funct();
        }
        var fmt = graph.pageFormat;
        var ps = graph.pageScale;
        var cw = graph.container.clientWidth - 10;
        var ch = graph.container.clientHeight - 10;
        var scale = Math.floor(20 * Math.min(cw / (2 * fmt.width) / ps, ch / fmt.height / ps)) / 20;
        graph.zoomTo(scale);
        if (mxgraph_1.mxUtils.hasScrollbars(graph.container)) {
            var pad = graph.getPagePadding();
            graph.container.scrollTop = Math.min(pad.y, (graph.container.scrollHeight - graph.container.clientHeight) / 2);
            graph.container.scrollLeft = Math.min(pad.x, (graph.container.scrollWidth - graph.container.clientWidth) / 2);
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+J");
    this.addAction("fitPageWidth", mxgraph_1.mxUtils.bind(this, function () {
        if (!graph.pageVisible) {
            this.get("pageView").funct();
        }
        var fmt = graph.pageFormat;
        var ps = graph.pageScale;
        var cw = graph.container.clientWidth - 10;
        var scale = Math.floor((20 * cw) / fmt.width / ps) / 20;
        graph.zoomTo(scale);
        if (mxgraph_1.mxUtils.hasScrollbars(graph.container)) {
            var pad = graph.getPagePadding();
            graph.container.scrollLeft = Math.min(pad.x * graph.view.scale, (graph.container.scrollWidth - graph.container.clientWidth) / 2);
        }
    }));
    this.put("customZoom", new Action(mxgraph_1.mxResources.get("custom") + "...", mxgraph_1.mxUtils.bind(this, function () {
        var dlg = new Dialogs_1.FilenameDialog(this.editorUi, parseInt(graph.getView().getScale() * 100), mxgraph_1.mxResources.get("apply"), mxgraph_1.mxUtils.bind(this, function (newValue) {
            var val = parseInt(newValue);
            if (!isNaN(val) && val > 0) {
                graph.zoomTo(val / 100);
            }
        }), mxgraph_1.mxResources.get("zoom") + " (%)");
        this.editorUi.showDialog(dlg.container, 300, 80, true, true);
        dlg.init();
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+0"));
    this.addAction("pageScale...", mxgraph_1.mxUtils.bind(this, function () {
        var dlg = new Dialogs_1.FilenameDialog(this.editorUi, parseInt(graph.pageScale * 100), mxgraph_1.mxResources.get("apply"), mxgraph_1.mxUtils.bind(this, function (newValue) {
            var val = parseInt(newValue);
            if (!isNaN(val) && val > 0) {
                var change = new PageSetup_1.PageSetup(ui, null, null, null, val / 100);
                change.ignoreColor = true;
                change.ignoreImage = true;
                graph.model.execute(change);
            }
        }), mxgraph_1.mxResources.get("pageScale") + " (%)");
        this.editorUi.showDialog(dlg.container, 300, 80, true, true);
        dlg.init();
    }));
    // Option actions
    var action = null;
    action = this.addAction("grid", function () {
        graph.setGridEnabled(!graph.isGridEnabled());
        ui.fireEvent(new mxgraph_1.mxEventObject("gridEnabledChanged"));
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+G");
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.isGridEnabled();
    });
    action.setEnabled(false);
    action = this.addAction("guides", function () {
        graph.graphHandler.guidesEnabled = !graph.graphHandler.guidesEnabled;
        ui.fireEvent(new mxgraph_1.mxEventObject("guidesEnabledChanged"));
    });
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.graphHandler.guidesEnabled;
    });
    action.setEnabled(false);
    action = this.addAction("tooltips", function () {
        graph.tooltipHandler.setEnabled(!graph.tooltipHandler.isEnabled());
    });
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.tooltipHandler.isEnabled();
    });
    action = this.addAction("collapseExpand", function () {
        var change = new PageSetup_1.PageSetup(ui);
        change.ignoreColor = true;
        change.ignoreImage = true;
        change.foldingEnabled = !graph.foldingEnabled;
        graph.model.execute(change);
    });
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.foldingEnabled;
    });
    action.isEnabled = isGraphEnabled;
    action = this.addAction("scrollbars", function () {
        ui.setScrollbars(!ui.hasScrollbars());
    });
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.scrollbars;
    });
    action = this.addAction("pageView", mxgraph_1.mxUtils.bind(this, function () {
        ui.setPageVisible(!graph.pageVisible);
    }));
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.pageVisible;
    });
    action = this.addAction("connectionArrows", function () {
        graph.connectionArrowsEnabled = !graph.connectionArrowsEnabled;
        ui.fireEvent(new mxgraph_1.mxEventObject("connectionArrowsChanged"));
    }, null, null, "Alt+Shift+A");
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.connectionArrowsEnabled;
    });
    action = this.addAction("connectionPoints", function () {
        graph.setConnectable(!graph.connectionHandler.isEnabled());
        ui.fireEvent(new mxgraph_1.mxEventObject("connectionPointsChanged"));
    }, null, null, "Alt+Shift+P");
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.connectionHandler.isEnabled();
    });
    action = this.addAction("copyConnect", function () {
        graph.connectionHandler.setCreateTarget(!graph.connectionHandler.isCreateTarget());
        ui.fireEvent(new mxgraph_1.mxEventObject("copyConnectChanged"));
    });
    action.setToggleAction(true);
    action.setSelectedCallback(function () {
        return graph.connectionHandler.isCreateTarget();
    });
    action.isEnabled = isGraphEnabled;
    // - AutoSave Action
    // - Help Action
    // - About Action
    // Font style actions
    var toggleFontStyle = mxgraph_1.mxUtils.bind(this, function (key, style, fn, shortcut) {
        return this.addAction(key, function () {
            if (fn != null && graph.cellEditor.isContentEditing()) {
                fn();
            }
            else {
                graph.stopEditing(false);
                graph.getModel().beginUpdate();
                try {
                    var cells = graph.getSelectionCells();
                    graph.toggleCellStyleFlags(mxgraph_1.mxConstants.STYLE_FONTSTYLE, style, cells);
                    // Removes bold and italic tags and CSS styles inside labels
                    if ((style & mxgraph_1.mxConstants.FONT_BOLD) == mxgraph_1.mxConstants.FONT_BOLD) {
                        graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                            elt.style.fontWeight = null;
                            if (elt.nodeName == "B") {
                                graph.replaceElement(elt);
                            }
                        });
                    }
                    else if ((style & mxgraph_1.mxConstants.FONT_ITALIC) == mxgraph_1.mxConstants.FONT_ITALIC) {
                        graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                            elt.style.fontStyle = null;
                            if (elt.nodeName == "I") {
                                graph.replaceElement(elt);
                            }
                        });
                    }
                    else if ((style & mxgraph_1.mxConstants.FONT_UNDERLINE) == mxgraph_1.mxConstants.FONT_UNDERLINE) {
                        graph.updateLabelElements(graph.getSelectionCells(), function (elt) {
                            elt.style.textDecoration = null;
                            if (elt.nodeName == "U") {
                                graph.replaceElement(elt);
                            }
                        });
                    }
                    for (var i = 0; i < cells.length; i++) {
                        if (graph.model.getChildCount(cells[i]) == 0) {
                            graph.autoSizeCell(cells[i], false);
                        }
                    }
                }
                finally {
                    graph.getModel().endUpdate();
                }
            }
        }, null, null, shortcut);
    });
    toggleFontStyle("bold", mxgraph_1.mxConstants.FONT_BOLD, function () {
        document.execCommand("bold", false, null);
    }, constant_1.EDITOR.CTRL_KEY + "+B");
    toggleFontStyle("italic", mxgraph_1.mxConstants.FONT_ITALIC, function () {
        document.execCommand("italic", false, null);
    }, constant_1.EDITOR.CTRL_KEY + "+I");
    toggleFontStyle("underline", mxgraph_1.mxConstants.FONT_UNDERLINE, function () {
        document.execCommand("underline", false, null);
    }, constant_1.EDITOR.CTRL_KEY + "+U");
    // Color actions
    this.addAction("fontColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_FONTCOLOR, "forecolor", "000000");
    });
    this.addAction("strokeColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_STROKECOLOR);
    });
    this.addAction("fillColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_FILLCOLOR);
    });
    this.addAction("gradientColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_GRADIENTCOLOR);
    });
    this.addAction("backgroundColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_LABEL_BACKGROUNDCOLOR, "backcolor");
    });
    this.addAction("borderColor...", function () {
        ui.menus.pickColor(mxgraph_1.mxConstants.STYLE_LABEL_BORDERCOLOR);
    });
    // Format actions
    this.addAction("vertical", function () {
        ui.menus.toggleStyle(mxgraph_1.mxConstants.STYLE_HORIZONTAL, true);
    });
    this.addAction("shadow", function () {
        ui.menus.toggleStyle(mxgraph_1.mxConstants.STYLE_SHADOW);
    });
    this.addAction("solid", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASHED, null);
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASH_PATTERN, null);
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], "values", [null, null], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("dashed", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASHED, "1");
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASH_PATTERN, null);
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], "values", ["1", null], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("dotted", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASHED, "1");
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_DASH_PATTERN, "1 4");
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_DASHED, mxgraph_1.mxConstants.STYLE_DASH_PATTERN], "values", ["1", "1 4"], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("sharp", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ROUNDED, "0");
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_CURVED, "0");
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_ROUNDED, mxgraph_1.mxConstants.STYLE_CURVED], "values", ["0", "0"], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("rounded", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ROUNDED, "1");
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_CURVED, "0");
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_ROUNDED, mxgraph_1.mxConstants.STYLE_CURVED], "values", ["1", "0"], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("toggleRounded", function () {
        if (!graph.isSelectionEmpty() && graph.isEnabled()) {
            graph.getModel().beginUpdate();
            try {
                var cells = graph.getSelectionCells();
                var style = graph.getCurrentCellStyle(cells[0]);
                var value = mxgraph_1.mxUtils.getValue(style, mxgraph_1.mxConstants.STYLE_ROUNDED, "0") == "1" ? "0" : "1";
                graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ROUNDED, value);
                graph.setCellStyles(mxgraph_1.mxConstants.STYLE_CURVED, null);
                ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_ROUNDED, mxgraph_1.mxConstants.STYLE_CURVED], "values", [value, "0"], "cells", graph.getSelectionCells()));
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    });
    this.addAction("curved", function () {
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_ROUNDED, "0");
            graph.setCellStyles(mxgraph_1.mxConstants.STYLE_CURVED, "1");
            ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", [mxgraph_1.mxConstants.STYLE_ROUNDED, mxgraph_1.mxConstants.STYLE_CURVED], "values", ["0", "1"], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
    this.addAction("collapsible", function () {
        var state = graph.view.getState(graph.getSelectionCell());
        var value = "1";
        if (state != null && graph.getFoldingImage(state) != null) {
            value = "0";
        }
        graph.setCellStyles("collapsible", value);
        ui.fireEvent(new mxgraph_1.mxEventObject("styleChanged", "keys", ["collapsible"], "values", [value], "cells", graph.getSelectionCells()));
    });
    // - Edit Style Action
    this.addAction("setAsDefaultStyle", function () {
        if (graph.isEnabled() && !graph.isSelectionEmpty()) {
            ui.setDefaultStyle(graph.getSelectionCell());
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+D");
    this.addAction("clearDefaultStyle", function () {
        if (graph.isEnabled()) {
            ui.clearDefaultStyle();
        }
    }, null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+R");
    this.addAction("addWaypoint", function () {
        var cell = graph.getSelectionCell();
        if (cell != null && graph.getModel().isEdge(cell)) {
            var handler = editor.graph.selectionCellsHandler.getHandler(cell);
            if (handler instanceof mxgraph_1.mxEdgeHandler) {
                var t = graph.view.translate;
                var s = graph.view.scale;
                var dx = t.x;
                var dy = t.y;
                var parent_1 = graph.getModel().getParent(cell);
                var pgeo = graph.getCellGeometry(parent_1);
                while (graph.getModel().isVertex(parent_1) && pgeo != null) {
                    dx += pgeo.x;
                    dy += pgeo.y;
                    parent_1 = graph.getModel().getParent(parent_1);
                    pgeo = graph.getCellGeometry(parent_1);
                }
                var x = Math.round(graph.snap(graph.popupMenuHandler.triggerX / s - dx));
                var y = Math.round(graph.snap(graph.popupMenuHandler.triggerY / s - dy));
                handler.addPointAt(handler.state, x, y);
            }
        }
    });
    this.addAction("removeWaypoint", function () {
        // TODO: Action should run with "this" set to action
        var rmWaypointAction = ui.actions.get("removeWaypoint");
        if (rmWaypointAction.handler != null) {
            // NOTE: Popupevent handled and action updated in Menus.createPopupMenu
            rmWaypointAction.handler.removePoint(rmWaypointAction.handler.state, rmWaypointAction.index);
        }
    });
    this.addAction("clearWaypoints", function () {
        var cells = graph.getSelectionCells();
        if (cells != null) {
            cells = graph.addAllEdges(cells);
            graph.getModel().beginUpdate();
            try {
                for (var i = 0; i < cells.length; i++) {
                    var cell = cells[i];
                    if (graph.getModel().isEdge(cell)) {
                        var geo = graph.getCellGeometry(cell);
                        if (geo != null) {
                            geo = geo.clone();
                            geo.points = null;
                            graph.getModel().setGeometry(cell, geo);
                        }
                    }
                }
            }
            finally {
                graph.getModel().endUpdate();
            }
        }
    }, null, null, "Alt+Shift+C");
    action = this.addAction("subscript", mxgraph_1.mxUtils.bind(this, function () {
        if (graph.cellEditor.isContentEditing()) {
            document.execCommand("subscript", false, null);
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+,");
    action = this.addAction("superscript", mxgraph_1.mxUtils.bind(this, function () {
        if (graph.cellEditor.isContentEditing()) {
            document.execCommand("superscript", false, null);
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+.");
    action = this.addAction("indent", mxgraph_1.mxUtils.bind(this, function () {
        // NOTE: Alt+Tab for outdent implemented via special code in
        // keyHandler.getFunction in EditorUi.js. Ctrl+Tab is reserved.
        if (graph.cellEditor.isContentEditing()) {
            document.execCommand("indent", false, null);
        }
    }), null, null, "Shift+Tab");
    // - Image... Action
    action = this.addAction("layers", mxgraph_1.mxUtils.bind(this, function () {
        if (this.layersWindow == null) {
            // LATER: Check outline window for initial placement
            this.layersWindow = new Dialogs_1.LayersWindow(ui, document.body.offsetWidth - 280, 120, 220, 196);
            this.layersWindow.window.addListener("show", function () {
                ui.fireEvent(new mxgraph_1.mxEventObject("layers"));
            });
            this.layersWindow.window.addListener("hide", function () {
                ui.fireEvent(new mxgraph_1.mxEventObject("layers"));
            });
            this.layersWindow.window.setVisible(true);
            ui.fireEvent(new mxgraph_1.mxEventObject("layers"));
            this.layersWindow.init();
        }
        else {
            this.layersWindow.window.setVisible(!this.layersWindow.window.isVisible());
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+L");
    action.setToggleAction(true);
    action.setSelectedCallback(mxgraph_1.mxUtils.bind(this, function () {
        return this.layersWindow != null && this.layersWindow.window.isVisible();
    }));
    action = this.addAction("formatPanel", mxgraph_1.mxUtils.bind(this, function () {
        ui.toggleFormatPanel();
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+P");
    action.setToggleAction(true);
    action.setSelectedCallback(mxgraph_1.mxUtils.bind(this, function () {
        return ui.formatWidth > 0;
    }));
    action = this.addAction("outline", mxgraph_1.mxUtils.bind(this, function () {
        if (this.outlineWindow == null) {
            // LATER: Check layers window for initial placement
            this.outlineWindow = new Dialogs_1.OutlineWindow(ui, document.body.offsetWidth - 260, 100, 180, 180);
            this.outlineWindow.window.addListener("show", function () {
                ui.fireEvent(new mxgraph_1.mxEventObject("outline"));
            });
            this.outlineWindow.window.addListener("hide", function () {
                ui.fireEvent(new mxgraph_1.mxEventObject("outline"));
            });
            this.outlineWindow.window.setVisible(true);
            ui.fireEvent(new mxgraph_1.mxEventObject("outline"));
        }
        else {
            this.outlineWindow.window.setVisible(!this.outlineWindow.window.isVisible());
        }
    }), null, null, constant_1.EDITOR.CTRL_KEY + "+Shift+O");
    action.setToggleAction(true);
    action.setSelectedCallback(mxgraph_1.mxUtils.bind(this, function () {
        return this.outlineWindow != null && this.outlineWindow.window.isVisible();
    }));
};
/**
 * Registers the given action under the given name.
 */
Actions.prototype.addAction = function (key, funct, enabled, iconCls, shortcut) {
    var title;
    if (key.substring(key.length - 3) == "...") {
        key = key.substring(0, key.length - 3);
        title = mxgraph_1.mxResources.get(key) + "...";
    }
    else {
        title = mxgraph_1.mxResources.get(key);
    }
    return this.put(key, new Action(title, funct, enabled, iconCls, shortcut));
};
/**
 * Registers the given action under the given name.
 */
Actions.prototype.put = function (name, action) {
    this.actions[name] = action;
    return action;
};
/**
 * Returns the action for the given name or null if no such action exists.
 */
Actions.prototype.get = function (name) {
    return this.actions[name];
};
/**
 * Constructs a new action for the given parameters.
 */
function Action(label, funct, enabled, iconCls, shortcut) {
    mxgraph_1.mxEventSource.call(this);
    this.label = label;
    this.funct = this.createFunction(funct);
    this.enabled = enabled != null ? enabled : true;
    this.iconCls = iconCls;
    this.shortcut = shortcut;
    this.visible = true;
}
exports.Action = Action;
// Action inherits from mxEventSource
mxgraph_1.mxUtils.extend(Action, mxgraph_1.mxEventSource);
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.createFunction = function (funct) {
    return funct;
};
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.setEnabled = function (value) {
    if (this.enabled != value) {
        this.enabled = value;
        this.fireEvent(new mxgraph_1.mxEventObject("stateChanged"));
    }
};
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.isEnabled = function () {
    return this.enabled;
};
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.setToggleAction = function (value) {
    this.toggleAction = value;
};
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.setSelectedCallback = function (funct) {
    this.selectedCallback = funct;
};
/**
 * Sets the enabled state of the action and fires a stateChanged event.
 */
Action.prototype.isSelected = function () {
    return this.selectedCallback();
};
