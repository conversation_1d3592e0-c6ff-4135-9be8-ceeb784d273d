/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/**
 * Special Layout for tables.
 */
export function TableLayout(graph: any): void;
export class TableLayout {
    /**
     * Special Layout for tables.
     */
    constructor(graph: any);
    /**
     * Function: isHorizontal
     *
     * Overrides stack layout to handle row reorder.
     */
    isHorizontal(): boolean;
    /**
     * Function: getSize
     *
     * Returns the total vertical or horizontal size of the given cells.
     */
    getSize(cells: any, horizontal: any): number;
    /**
     * Function: getRowLayout
     *
     * Returns the column positions for the given row and table width.
     */
    getRowLayout(row: any, width: any): number[];
    /**
     * Function: layoutRow
     *
     * Places the cells at the given positions in the given row.
     */
    layoutRow(row: any, positions: any, height: any, tw: any): number;
    /**
     * Function: execute
     *
     * Implements <mxGraphLayout.execute>.
     */
    execute(parent: any): void;
}
/**
 * Constructs a new graph instance. Note that the constructor does not take a
 * container because the graph instance is needed for creating the UI, which
 * in turn will create the container for the graph. Hence, the container is
 * assigned later in EditorUi.
 */
/**
 * Defines graph class.
 */
export function Graph(container: any, model: any, renderHint: any, stylesheet: any, themes: any, standalone: any): void;
export class Graph {
    /**
     * Constructs a new graph instance. Note that the constructor does not take a
     * container because the graph instance is needed for creating the UI, which
     * in turn will create the container for the graph. Hence, the container is
     * assigned later in EditorUi.
     */
    /**
     * Defines graph class.
     */
    constructor(container: any, model: any, renderHint: any, stylesheet: any, themes: any, standalone: any);
    themes: any;
    currentEdgeStyle: any;
    currentVertexStyle: any;
    standalone: any;
    domainUrl: string;
    domainPathUrl: string;
    isHtmlLabel: (cell: any) => boolean;
    allowAutoPanning: boolean | undefined;
    resetEdgesOnConnect: boolean | undefined;
    constrainChildren: boolean | undefined;
    constrainRelativeChildren: boolean | undefined;
    alternateEdgeStyle: string | undefined;
    getRubberband: (() => import("mxgraph").mxRubberband) | undefined;
    isToggleEvent: ((evt: any, ...args: any[]) => any) | undefined;
    click: ((me: any, ...args: any[]) => any) | undefined;
    getCursorForMouseEvent: ((me: any) => any) | undefined;
    getCursorForCell: ((cell: any, ...args: any[]) => any) | undefined;
    selectRegion: ((rect: any, evt: any) => any) | undefined;
    getAllCells: ((x: any, y: any, width: any, height: any, parent: any, result: any) => any) | undefined;
    isCellLocked: ((cell: any) => boolean) | undefined;
    updateMouseEvent: ((me: any, ...args: any[]) => any) | undefined;
    currentTranslate: import("mxgraph").mxPoint;
    /**
     * Allows all values in fit.
     */
    minFitScale: any;
    /**
     * Allows all values in fit.
     */
    maxFitScale: any;
    /**
     * Sets the policy for links. Possible values are "self" to replace any framesets,
     * "blank" to load the URL in <linkTarget> and "auto" (default).
     */
    linkPolicy: string;
    /**
     * Target for links that open in a new window. Default is _blank.
     */
    linkTarget: string;
    /**
     * Value to the rel attribute of links. Default is 'nofollow noopener noreferrer'.
     * NOTE: There are security implications when this is changed and if noopener is removed,
     * then <openLink> must be overridden to allow for the opener to be set by default.
     */
    linkRelation: string;
    /**
     * Scrollbars are enabled on non-touch devices (not including Firefox because touch events
     * cannot be detected in Firefox, see above).
     */
    defaultScrollbars: boolean;
    /**
     * Specifies if the page should be visible for new files. Default is true.
     */
    defaultPageVisible: boolean;
    /**
     * Specifies if the app should run in chromeless mode. Default is false.
     * This default is only used if the contructor argument is null.
     */
    lightbox: boolean;
    /**
     *
     */
    defaultPageBackgroundColor: string;
    /**
     *
     */
    defaultPageBorderColor: string;
    /**
     * Specifies the size of the size for "tiles" to be used for a graph with
     * scrollbars but no visible background page. A good value is large
     * enough to reduce the number of repaints that is caused for auto-
     * translation, which depends on this value, and small enough to give
     * a small empty buffer around the graph. Default is 400x400.
     */
    scrollTileSize: import("mxgraph").mxRectangle;
    /**
     * Overrides the background color and paints a transparent background.
     */
    transparentBackground: boolean;
    /**
     * Sets global constants.
     */
    selectParentAfterDelete: boolean;
    /**
     * Sets the default target for all links in cells.
     */
    defaultEdgeLength: number;
    /**
     * Disables move of bends/segments without selecting.
     */
    edgeMode: boolean;
    /**
     * Allows all values in fit.
     */
    connectionArrowsEnabled: boolean;
    /**
     * Specifies the regular expression for matching placeholders.
     */
    placeholderPattern: RegExp;
    /**
     * Specifies the regular expression for matching placeholders.
     */
    absoluteUrlPattern: RegExp;
    /**
     * Specifies the default name for the theme. Default is 'default'.
     */
    defaultThemeName: string;
    /**
     * Specifies the default name for the theme. Default is 'default'.
     */
    defaultThemes: {};
    /**
     * Base URL for relative links.
     */
    baseUrl: string;
    /**
     * Specifies if the label should be edited after an insert.
     */
    editAfterInsert: boolean;
    /**
     * Defines the built-in properties to be ignored in tooltips.
     */
    builtInProperties: string[];
    /**
     * Installs child layout styles.
     */
    init(...args: any[]): void;
    /**
     * Sets the XML node for the current diagram.
     */
    isLightboxView(): boolean;
    /**
     * Sets the XML node for the current diagram.
     */
    isViewer(): boolean;
    /**
     * Installs automatic layout via styles
     */
    labelLinkClicked(state: any, elt: any, evt: any): void;
    /**
     * Returns the size of the page format scaled with the page size.
     */
    openLink(href: any, target: any, allowOpener: any): Window & typeof globalThis;
    /**
     * Adds support for page links.
     */
    getLinkTitle(href: any): any;
    /**
     * Adds support for page links.
     */
    isCustomLink(href: any): boolean;
    /**
     * Adds support for page links.
     */
    customLinkClicked(): boolean;
    /**
     * Returns true if the given href references an external protocol that
     * should never open in a new window. Default returns true for mailto.
     */
    isExternalProtocol(href: any): boolean;
    /**
     * Hook for links to open in same window. Default returns true for anchors,
     * links to same domain or if target == 'self' in the config.
     */
    isBlankLink(href: any): boolean;
    /**
     *
     */
    isRelativeUrl(url: any): boolean;
    /**
     *
     */
    getAbsoluteUrl(url: any): any;
    /**
     * Installs automatic layout via styles
     */
    initLayoutManager(): void;
    layoutManager: import("mxgraph").mxLayoutManager | undefined;
    /**
     * Returns the size of the page format scaled with the page size.
     */
    getPageSize(): import("mxgraph").mxRectangle;
    /**
     * Returns a rectangle describing the position and count of the
     * background pages, where x and y are the position of the top,
     * left page and width and height are the vertical and horizontal
     * page count.
     */
    getPageLayout(): import("mxgraph").mxRectangle;
    /**
     * Sanitizes the given HTML markup.
     */
    sanitizeHtml(value: any, editing: any): any;
    /**
     * Revalidates all cells with placeholders in the current graph model.
     */
    updatePlaceholders(): void;
    /**
     * Adds support for placeholders in labels.
     */
    isReplacePlaceholders(cell: any): boolean;
    /**
     * Returns true if the given mouse wheel event should be used for zooming. This
     * is invoked if no dialogs are showing and returns true with Alt or Control
     * (or cmd in macOS only) is pressed.
     */
    isZoomWheelEvent(evt: any): boolean;
    /**
     * Returns true if the given scroll wheel event should be used for scrolling.
     */
    isScrollWheelEvent(evt: any): boolean;
    /**
     * Adds Alt+click to select cells behind cells (Shift+Click on Chrome OS).
     */
    isTransparentClickEvent(evt: any): boolean;
    /**
     * Adds ctrl+shift+connect to disable connections.
     */
    isIgnoreTerminalEvent(evt: any): boolean;
    /**
     * Adds support for placeholders in labels.
     */
    isSplitTarget(target: any, cells: any, evt: any, ...args: any[]): boolean;
    /**
     * Adds support for placeholders in labels.
     */
    getLabel(cell: any, ...args: any[]): string | Node;
    /**
     * Adds labelMovable style.
     */
    isLabelMovable(cell: any): any;
    /**
     * Adds event if grid size is changed.
     */
    setGridSize(value: any): void;
    gridSize: any;
    /**
     * Function: getClickableLinkForCell
     *
     * Returns the first non-null link for the cell or its ancestors.
     *
     * Parameters:
     *
     * cell - <mxCell> whose link should be returned.
     */
    getClickableLinkForCell(cell: any): any;
    /**
     * Private helper method.
     */
    getGlobalVariable(name: any): any;
    /**
     * Formats a date, see http://blog.stevenlevithan.com/archives/date-time-format
     */
    formatDate(date: any, mask: any, utc: any, ...args: any[]): any;
    dateFormatCache: {
        i18n: {
            dayNames: string[];
            monthNames: string[];
        };
        masks: {
            default: string;
            shortDate: string;
            mediumDate: string;
            longDate: string;
            fullDate: string;
            shortTime: string;
            mediumTime: string;
            longTime: string;
            isoDate: string;
            isoTime: string;
            isoDateTime: string;
            isoUtcDateTime: string;
        };
    } | undefined;
    /**
     *
     */
    createLayersDialog(): HTMLDivElement;
    /**
     * Private helper method.
     */
    replacePlaceholders(cell: any, str: any): string;
    /**
     * Resolves the given cells in the model and selects them.
     */
    restoreSelection(cells: any): void;
    /**
     * Selects cells for connect vertex return value.
     */
    selectCellsForConnectVertex(cells: any, evt: any, hoverIcons: any): void;
    /**
     * Adds a connection to the given vertex.
     */
    connectVertex(source: any, direction: any, length: any, evt: any, forceClone: any, ignoreCellAt: any): any[];
    /**
     * Returns all labels in the diagram as a string.
     */
    getIndexableText(): string;
    /**
     * Returns the label for the given cell.
     */
    convertValueToString(cell: any, ...args: any[]): any;
    /**
     * Returns the link for the given cell.
     */
    getLinksForState(state: any): any;
    /**
     * Returns the link for the given cell.
     */
    getLinkForCell(cell: any): any;
    /**
     * Overrides label orientation for collapsed swimlanes inside stack and
     * for partial rectangles inside tables.
     */
    getCellStyle(cell: any, ...args: any[]): import("mxgraph").StyleMap;
    /**
     * Disables alternate width persistence for stack layout parents
     */
    updateAlternateBounds(cell: any, geo: any, ...args: any[]): void;
    /**
     * Adds Shift+collapse/expand and size management for folding inside stack
     */
    isMoveCellsEvent(evt: any, state: any): boolean;
    /**
     * Adds Shift+collapse/expand and size management for folding inside stack
     */
    foldCells(collapse: any, recurse: any, cells: any, checkFoldable: any, evt: any, ...args: any[]): void;
    /**
     * Overrides label orientation for collapsed swimlanes inside stack.
     */
    moveSiblings(state: any, parent: any, dx: any, dy: any): void;
    /**
     * Overrides label orientation for collapsed swimlanes inside stack.
     */
    resizeParentStacks(parent: any, layout: any, dx: any, dy: any): void;
    /**
     * Disables drill-down for non-swimlanes.
     */
    isContainer(cell: any): boolean;
    /**
     * Adds a connectable style.
     */
    isCellConnectable(cell: any, ...args: any[]): boolean;
    /**
     * Function: selectAll
     *
     * Selects all children of the given parent cell or the children of the
     * default parent if no parent is specified. To select leaf vertices and/or
     * edges use <selectCells>.
     *
     * Parameters:
     *
     * parent - Optional <mxCell> whose children should be selected.
     * Default is <defaultParent>.
     */
    selectAll(parent: any, ...args: any[]): void;
    /**
     * Function: selectCells
     *
     * Selects all vertices and/or edges depending on the given boolean
     * arguments recursively, starting at the given parent or the default
     * parent if no parent is specified. Use <selectAll> to select all cells.
     * For vertices, only cells with no children are selected.
     *
     * Parameters:
     *
     * vertices - Boolean indicating if vertices should be selected.
     * edges - Boolean indicating if edges should be selected.
     * parent - Optional <mxCell> that acts as the root of the recursion.
     * Default is <defaultParent>.
     */
    selectCells(vertices: any, edges: any, parent: any, ...args: any[]): void;
    /**
     * Function: getSwimlaneAt
     *
     * Returns the bottom-most swimlane that intersects the given point (x, y)
     * in the cell hierarchy that starts at the given parent.
     *
     * Parameters:
     *
     * x - X-coordinate of the location to be checked.
     * y - Y-coordinate of the location to be checked.
     * parent - <mxCell> that should be used as the root of the recursion.
     * Default is <defaultParent>.
     */
    getSwimlaneAt(...args: any[]): import("mxgraph").mxCell;
    /**
     * Disables folding for non-swimlanes.
     */
    isCellFoldable(cell: any): any;
    /**
     * Stops all interactions and clears the selection.
     */
    reset(): void;
    /**
     * Overridden to limit zoom to 1% - 16.000%.
     */
    zoom(factor: any, ...args: any[]): void;
    /**
     * Function: zoomIn
     *
     * Zooms into the graph by <zoomFactor>.
     */
    zoomIn(): void;
    /**
     * Function: zoomOut
     *
     * Zooms out of the graph by <zoomFactor>.
     */
    zoomOut(): void;
    /**
     * Overrides tooltips to show custom tooltip or metadata.
     */
    getTooltipForCell(cell: any): string;
    /**
     * Turns the given string into an array.
     */
    stringToBytes(str: any): any[];
    /**
     * Turns the given array into a string.
     */
    bytesToString(arr: any): string;
    /**
     * Returns a base64 encoded version of the compressed outer XML of the given node.
     */
    compressNode(node: any): any;
    /**
     * Returns a base64 encoded version of the compressed string.
     */
    compress(data: any, deflate: any): any;
    /**
     * Returns a decompressed version of the base64 encoded string.
     */
    decompress(data: any, inflate: any): any;
    /**
     * Redirects to Graph.zapGremlins.
     */
    zapGremlins(text: any): string;
    /**
     * Returns true if the given cell is a table.
     */
    createParent(parent: any, child: any, childCount: any, dx: any, dy: any): any;
    /**
     * Returns true if the given cell is a table.
     */
    createTable(rowCount: any, colCount: any, w: any, h: any, title: any, startSize: any, tableStyle: any, rowStyle: any, cellStyle: any): any;
    /**
     * Sets the values for the cells and rows in the given table and returns the table.
     */
    setTableValues(table: any, values: any, rowValues: any): any;
    /**
     *
     */
    createCrossFunctionalSwimlane(rowCount: any, colCount: any, w: any, h: any, startSize: any, tableStyle: any, rowStyle: any, firstCellStyle: any, cellStyle: any): any;
    /**
     * Returns true if the given cell is a table cell.
     */
    isTableCell(cell: any): any;
    /**
     * Returns true if the given cell is a table row.
     */
    isTableRow(cell: any): any;
    /**
     * Returns true if the given cell is a table.
     */
    isTable(cell: any): boolean;
    /**
     * Updates the row and table heights.
     */
    setTableRowHeight(row: any, dy: any, extend: any): void;
    /**
     * Updates column width and row height.
     */
    setTableColumnWidth(col: any, dx: any, extend: any): void;
}
export namespace Graph {
    let touchStyle: boolean;
    let fileSupport: boolean;
    let lineJumpsEnabled: boolean;
    let defaultJumpSize: number;
    let minTableColumnWidth: number;
    let minTableRowHeight: number;
    let foreignObjectWarningText: string;
    let foreignObjectWarningLink: string;
    /**
     * Helper function for creating SVG data URI.
     */
    function createSvgImage(w: any, h: any, data: any, coordWidth: any, coordHeight: any): import("mxgraph").mxImage;
    /**
     * Removes all illegal control characters with ASCII code <32 except TAB, LF
     * and CR.
     */
    function zapGremlins(text: any): string;
    /**
     * Turns the given string into an array.
     */
    function stringToBytes(str: any): any[];
    /**
     * Turns the given array into a string.
     */
    function bytesToString(arr: any): string;
    /**
     * Returns a base64 encoded version of the compressed outer XML of the given node.
     */
    function compressNode(node: any, checked: any): any;
    /**
     * Returns a base64 encoded version of the compressed string.
     */
    function compress(data: any, deflate: any): any;
    /**
     * Returns a decompressed version of the base64 encoded string.
     */
    function decompress(data: any, inflate: any, checked: any): any;
    /**
     * Removes formatting from pasted HTML.
     */
    function removePasteFormatting(elt: any): void;
    /**
     * Sanitizes the given HTML markup.
     */
    function sanitizeHtml(value: any): any;
    /**
     * Returns true if the given string is a link.
     *
     * See https://stackoverflow.com/questions/5717093/check-if-a-javascript-string-is-a-url
     */
    function isLink(text: any): boolean;
    let linkPattern: RegExp;
}
/**
 * Hover icons are used for hover, vertex handler and drag from sidebar.
 */
export function HoverIcons(graph: any): void;
export class HoverIcons {
    /**
     * Hover icons are used for hover, vertex handler and drag from sidebar.
     */
    constructor(graph: any);
    graph: any;
    /**
     * Up arrow.
     */
    arrowSpacing: number;
    /**
     * Delay to switch to another state for overlapping bbox. Default is 500ms.
     */
    updateDelay: number;
    /**
     * Delay to switch between states. Default is 140ms.
     */
    activationDelay: number;
    /**
     * Up arrow.
     */
    currentState: any;
    /**
     * Up arrow.
     */
    activeArrow: any;
    /**
     * Up arrow.
     */
    inactiveOpacity: number;
    /**
     * Up arrow.
     */
    cssCursor: string;
    /**
     * Whether to hide arrows that collide with vertices.
     * LATER: Add keyboard override, touch support.
     */
    checkCollisions: boolean;
    /**
     * Up arrow.
     */
    arrowFill: string;
    /**
     * Up arrow.
     */
    triangleUp: import("mxgraph").mxImage;
    /**
     * Right arrow.
     */
    triangleRight: import("mxgraph").mxImage;
    /**
     * Down arrow.
     */
    triangleDown: import("mxgraph").mxImage;
    /**
     * Left arrow.
     */
    triangleLeft: import("mxgraph").mxImage;
    /**
     * Round target.
     */
    roundDrop: import("mxgraph").mxImage;
    /**
     * Refresh target.
     */
    refreshTarget: import("mxgraph").mxImage;
    /**
     * Tolerance for hover icon clicks.
     */
    tolerance: number;
    /**
     *
     */
    init(): void;
    arrowUp: HTMLImageElement | undefined;
    arrowRight: HTMLImageElement | undefined;
    arrowDown: HTMLImageElement | undefined;
    arrowLeft: HTMLImageElement | undefined;
    elts: HTMLImageElement[] | undefined;
    resetHandler: (() => void) | undefined;
    repaintHandler: (() => void) | undefined;
    /**
     *
     */
    isResetEvent(evt: any): boolean;
    /**
     *
     */
    createArrow(img: any, tooltip: any): HTMLImageElement;
    /**
     *
     */
    resetActiveArrow(): void;
    /**
     *
     */
    getDirection(): "east";
    /**
     *
     */
    visitNodes(visitor: any): void;
    /**
     *
     */
    removeNodes(): void;
    /**
     *
     */
    setDisplay(display: any): void;
    /**
     *
     */
    isActive(): boolean;
    /**
     *
     */
    drag(evt: any, x: any, y: any): void;
    /**
     *
     */
    getStateAt(state: any, x: any, y: any): any;
    /**
     *
     */
    click(state: any, dir: any, me: any): void;
    /**
     *
     */
    reset(clearTimeout: any): void;
    mouseDownPoint: any;
    bbox: import("mxgraph").mxRectangle | null | undefined;
    /**
     *
     */
    repaint(): void;
    /**
     *
     */
    computeBoundingBox(): import("mxgraph").mxRectangle | null;
    /**
     *
     */
    getState(state: any): any;
    /**
     *
     */
    update(state: any, x: any, y: any): void;
    startTime: number | undefined;
    prev: any;
    updateThread: number | undefined;
    /**
     *
     */
    setCurrentState(state: any): void;
}
