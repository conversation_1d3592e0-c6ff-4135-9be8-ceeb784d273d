/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
/**
 * Constructs a new graph editor
 */
export function EditorUi(editor: any, container: any, lightbox: any, onExit: any): void;
export class EditorUi {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    /**
     * Constructs a new graph editor
     */
    constructor(editor: any, container: any, lightbox: any, onExit: any);
    destroyFunctions: any[];
    editor: any;
    container: any;
    lazyZoomDelay: number;
    footerHeight: number;
    actions: Actions;
    menus: Menus;
    hoverIcons: HoverIcons | undefined;
    keydownHandler: ((evt: any) => void) | undefined;
    keyupHandler: (() => void) | undefined;
    keyHandler: import("mxgraph").mxKeyHandler | undefined;
    getKeyHandler: (() => import("mxgraph").mxKeyHandler | undefined) | undefined;
    setDefaultStyle: ((cell: any) => void) | undefined;
    clearDefaultStyle: (() => void) | undefined;
    gestureHandler: ((evt: any) => void) | undefined;
    resizeHandler: (() => void) | undefined;
    orientationChangeHandler: (() => void) | undefined;
    scrollHandler: (() => void) | undefined;
    /**
     * Specifies the size of the split bar.
     */
    splitSize: number;
    /**
     * Specifies the height of the menubar. Default is 30.
     */
    menubarHeight: number;
    /**
     * Specifies the width of the format panel should be enabled. Default is true.
     */
    formatEnabled: boolean;
    /**
     * Specifies the width of the format panel. Default is 240.
     */
    formatWidth: number | undefined;
    /**
     * Specifies the height of the toolbar. Default is 38.
     */
    toolbarHeight: number;
    /**
     * Specifies the height of the optional sidebarFooterContainer. Default is 34.
     */
    sidebarFooterHeight: number;
    /**
     * Specifies the position of the horizontal split bar. Default is 240 or 118 for
     * screen widths <= 640px.
     */
    hsplitPosition: number;
    /**
     * Specifies if animations are allowed in <executeLayout>. Default is true.
     */
    allowAnimation: boolean;
    /**
     * Default is 2.
     */
    lightboxMaxFitScale: number;
    /**
     * Default is 4.
     */
    lightboxVerticalDivider: number;
    /**
     * Specifies if single click on horizontal split should collapse sidebar. Default is false.
     */
    hsplitClickEnabled: boolean;
    /**
     * Installs the listeners to update the action states.
     */
    init(): void;
    /**
     * Returns true if the given event should start editing. This implementation returns true.
     */
    onKeyDown(evt: any): void;
    /**
     * Returns true if the given event should start editing. This implementation returns true.
     */
    onKeyPress(evt: any): void;
    /**
     * Returns true if the given event should start editing. This implementation returns true.
     */
    isImmediateEditingEvent(): boolean;
    /**
     * Private helper method.
     */
    getCssClassForMarker(prefix: any, shape: any, marker: any, fill: any): string;
    /**
     * "Installs" menus in EditorUi.
     */
    createMenus(): Menus;
    /**
     * Hook for allowing selection and context menu for certain events.
     */
    updatePasteActionStates(): void;
    /**
     * Hook for allowing selection and context menu for certain events.
     */
    initClipboard(): void;
    /**
     * Delay before update of DOM when using preview.
     */
    wheelZoomDelay: number;
    /**
     * Delay before update of DOM when using preview.
     */
    buttonZoomDelay: number;
    /**
     * Initializes the infinite canvas.
     */
    initCanvas(): void;
    chromelessResize: ((autoscale: any, maxScale: any, cx: any, cy: any) => void) | undefined;
    chromelessWindowResize: (() => void) | undefined;
    chromelessToolbar: HTMLDivElement | undefined;
    /**
     * Creates a temporary graph instance for rendering off-screen content.
     */
    addChromelessToolbarItems(addButton: any): void;
    /**
     * Creates a temporary graph instance for rendering off-screen content.
     */
    createTemporaryGraph(stylesheet: any): Graph;
    /**
     *
     */
    toggleFormatPanel(visible: any): void;
    /**
     * Adds support for placeholders in labels.
     */
    lightboxFit(maxHeight: any): void;
    /**
     * Translates this point by the given vector.
     *
     * @param {number} dx X-coordinate of the translation.
     * @param {number} dy Y-coordinate of the translation.
     */
    isDiagramEmpty(): boolean;
    /**
     * Hook for allowing selection and context menu for certain events.
     */
    isSelectionAllowed(evt: any): boolean;
    /**
     * Installs dialog if browser window is closed without saving
     * This must be disabled during save and image export.
     */
    addBeforeUnloadListener(): void;
    /**
     * Sets the onbeforeunload for the application
     */
    onBeforeUnload(): string | undefined;
    /**
     * Opens the current diagram via the window.opener if one exists.
     */
    open(): void;
    /**
     * Sets the current menu and element.
     */
    setCurrentMenu(menu: any, elt: any): void;
    currentMenuElt: any;
    currentMenu: any;
    /**
     * Resets the current menu and element.
     */
    resetCurrentMenu(): void;
    /**
     * Hides and destroys the current menu.
     */
    hideCurrentMenu(): void;
    /**
     * Updates the document title.
     */
    updateDocumentTitle(): void;
    /**
     * Updates the document title.
     */
    createHoverIcons(): HoverIcons;
    /**
     * Returns the URL for a copy of this editor with no state.
     */
    redo(): void;
    /**
     * Returns the URL for a copy of this editor with no state.
     */
    undo(): void;
    /**
     * Returns the URL for a copy of this editor with no state.
     */
    canRedo(): any;
    /**
     * Returns the URL for a copy of this editor with no state.
     */
    canUndo(): any;
    /**
     *
     */
    getEditBlankXml(): string;
    /**
     * Returns the URL for a copy of this editor with no state.
     */
    getUrl(pathname: any): any;
    /**
     * Specifies if the graph has scrollbars.
     */
    setScrollbars(value: any): void;
    /**
     * Returns true if the graph has scrollbars.
     */
    hasScrollbars(): any;
    /**
     * Resets the state of the scrollbars.
     */
    resetScrollbars(): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setPageVisible(value: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setBackgroundColor(value: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setFoldingEnabled(value: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setPageFormat(value: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setPageScale(value: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setGridColor(value: any): void;
    /**
     * Updates the states of the given undo/redo items.
     */
    addUndoListener(): void;
    /**
     * Updates the states of the given toolbar items based on the selection.
     */
    updateActionStates(): void;
    zeroOffset: import("mxgraph").mxPoint;
    getDiagramContainerOffset(): import("mxgraph").mxPoint;
    /**
     * Refreshes the viewport.
     */
    refresh(sizeDidChange: any): void;
    /**
     * Creates the required containers.
     */
    createTabContainer(): null;
    /**
     * Creates the required containers.
     */
    createDivs(): void;
    menubarContainer: HTMLDivElement | undefined;
    toolbarContainer: HTMLDivElement | undefined;
    sidebarContainer: HTMLDivElement | undefined;
    formatContainer: HTMLDivElement | undefined;
    diagramContainer: HTMLDivElement | undefined;
    footerContainer: HTMLDivElement | undefined;
    hsplit: HTMLDivElement | undefined;
    sidebarFooterContainer: any;
    tabContainer: any;
    /**
     * Hook for sidebar footer container. This implementation returns null.
     */
    createSidebarFooterContainer(): null;
    /**
     * Creates the required containers.
     */
    createUi(): void;
    menubar: import("./Menus").Menubar | null | undefined;
    exitButton: HTMLDivElement | undefined;
    statusContainer: HTMLAnchorElement | undefined;
    sidebar: Sidebar | null | undefined;
    format: Format | null | undefined;
    toolbar: Toolbar | null | undefined;
    /**
     * Creates a new toolbar for the given container.
     */
    createStatusContainer(): HTMLAnchorElement;
    /**
     * Creates a new toolbar for the given container.
     */
    setStatusText(value: any): void;
    /**
     * Creates a new toolbar for the given container.
     */
    createToolbar(container: any): Toolbar;
    /**
     * Creates a new sidebar for the given container.
     */
    createSidebar(container: any): Sidebar;
    /**
     * Creates a new sidebar for the given container.
     */
    createFormat(container: any): Format;
    /**
     * Creates and returns a new footer.
     */
    createFooter(): HTMLDivElement;
    /**
     * Creates the actual toolbar for the toolbar container.
     */
    createDiv(classname: any): HTMLDivElement;
    /**
     * Updates the states of the given undo/redo items.
     */
    addSplitHandler(elt: any, horizontal: any, dx: any, onChange: any): void;
    /**
     * Translates this point by the given vector.
     *
     * @param {number} dx X-coordinate of the translation.
     * @param {number} dy Y-coordinate of the translation.
     */
    handleError(resp: any, title: any, fn: any, invokeFnOnClose: any): void;
    /**
     * Translates this point by the given vector.
     *
     * @param {number} dx X-coordinate of the translation.
     * @param {number} dy Y-coordinate of the translation.
     */
    showError(title: any, msg: any, btn: any, fn: any, retry: any, btn2: any, fn2: any, btn3: any, fn3: any, w: any, h: any, hide: any, onClose: any): void;
    /**
     * Displays a print dialog.
     */
    showDialog(elt: any, w: any, h: any, modal: any, closable: any, onClose: any, noScroll: any, transparent: any, onResize: any, ignoreBgClick: any): void;
    dialogs: any[] | undefined;
    dialog: any;
    /**
     * Displays a print dialog.
     */
    hideDialog(cancel: any, isEsc: any): void;
    /**
     * Handles ctrl+enter keystroke to clone cells.
     */
    ctrlEnter(): void;
    /**
     * Display a color dialog.
     */
    pickColor(color: any, apply: any): void;
    /**
     * Extracs the graph model from the given HTML data from a data transfer event.
     */
    extractGraphModelFromHtml(data: any): any;
    /**
     * Opens the given files in the editor.
     */
    extractGraphModelFromEvent(evt: any): any;
    /**
     * Hook for subclassers to return true if event data is a supported format.
     * This implementation always returns false.
     */
    isCompatibleString(): boolean;
    /**
     * Executes the given layout.
     */
    executeLayout(exec: any, animate: any, post: any): void;
    /**
     * Hides the current menu.
     */
    showImageDialog(title: any, value: any, fn: any): void;
    /**
     * Hides the current menu.
     */
    showLinkDialog(): void;
    /**
     * Hides the current menu.
     */
    showBackgroundImageDialog(apply: any, img: any): void;
    /**
     * Loads the stylesheet for this graph.
     */
    setBackgroundImage(image: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    confirm(msg: any, okFn: any, cancelFn: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    createOutline(): import("mxgraph").mxOutline;
    altShiftActions: {
        67: string;
        65: string;
        80: string;
        84: string;
        86: string;
        88: string;
    };
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    createKeyHandler(): import("mxgraph").mxKeyHandler;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    destroy(): void;
}
export namespace EditorUi {
    let compactUi: boolean;
}
import { Actions } from "./Actions";
import { Menus } from "./Menus";
import { HoverIcons } from "./Graph";
import { Graph } from "./Graph";
import { Sidebar } from "./Sidebar";
import { Format } from "./Format";
import { Toolbar } from "./Toolbar";
