/**
 * Adds the label menu items to the given menu and parent.
 */
export function StyleFormatPanel(format: any, editorUi: any, container: any): void;
export class StyleFormatPanel {
    /**
     * Adds the label menu items to the given menu and parent.
     */
    constructor(format: any, editorUi: any, container: any);
    /**
     *
     */
    defaultStrokeColor: string;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    /**
     * Use browser for parsing CSS.
     */
    getCssRules(css: any): CSSRuleList;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addSvgStyles(container: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addSvgRule(container: any, rule: any, svg: any, styleElem: any, rules: any, ruleIndex: any, regex: any): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addEditOps(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addFill(container: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    getCustomColors(): {
        title: string;
        key: string;
        defaultValue: string;
    }[];
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addStroke(container: any): any;
    /**
     * Adds UI for configuring line jumps.
     */
    addLineJumps(container: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addEffects(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addStyleOps(div: any): any;
}
/**
 * Adds the label menu items to the given menu and parent.
 */
export function TextFormatPanel(format: any, editorUi: any, container: any): void;
export class TextFormatPanel {
    /**
     * Adds the label menu items to the given menu and parent.
     */
    constructor(format: any, editorUi: any, container: any);
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addFont(container: any): any;
}
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
export function Format(editorUi: any, container: any): void;
export class Format {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    constructor(editorUi: any, container: any);
    editorUi: any;
    container: any;
    /**
     * Returns information about the current selection.
     */
    labelIndex: number;
    /**
     * Returns information about the current selection.
     */
    currentIndex: number;
    /**
     * Returns information about the current selection.
     */
    showCloseButton: boolean;
    /**
     * Background color for inactive tabs.
     */
    inactiveTabBackgroundColor: string;
    /**
     * Background color for inactive tabs.
     */
    roundableShapes: string[];
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    update: (() => void) | undefined;
    /**
     * Returns information about the current selection.
     */
    clearSelectionState(): void;
    selectionState: {
        vertices: never[];
        edges: never[];
        x: null;
        y: null;
        width: null;
        height: null;
        style: {};
        containsImage: boolean;
        containsLabel: boolean;
        fill: boolean;
        glass: boolean;
        rounded: boolean;
        comic: boolean;
        autoSize: boolean;
        image: boolean;
        shadow: boolean;
        lineJumps: boolean;
        resizable: boolean;
        table: boolean;
        cell: boolean;
        row: boolean;
        movable: boolean;
        rotatable: boolean;
    } | null | undefined;
    /**
     * Returns information about the current selection.
     */
    getSelectionState(): {
        vertices: never[];
        edges: never[];
        x: null;
        y: null;
        width: null;
        height: null;
        style: {};
        containsImage: boolean;
        containsLabel: boolean;
        fill: boolean;
        glass: boolean;
        rounded: boolean;
        comic: boolean;
        autoSize: boolean;
        image: boolean;
        shadow: boolean;
        lineJumps: boolean;
        resizable: boolean;
        table: boolean;
        cell: boolean;
        row: boolean;
        movable: boolean;
        rotatable: boolean;
    };
    /**
     * Returns information about the current selection.
     */
    createSelectionState(): {
        vertices: never[];
        edges: never[];
        x: null;
        y: null;
        width: null;
        height: null;
        style: {};
        containsImage: boolean;
        containsLabel: boolean;
        fill: boolean;
        glass: boolean;
        rounded: boolean;
        comic: boolean;
        autoSize: boolean;
        image: boolean;
        shadow: boolean;
        lineJumps: boolean;
        resizable: boolean;
        table: boolean;
        cell: boolean;
        row: boolean;
        movable: boolean;
        rotatable: boolean;
    };
    /**
     * Returns information about the current selection.
     */
    initSelectionState(): {
        vertices: never[];
        edges: never[];
        x: null;
        y: null;
        width: null;
        height: null;
        style: {};
        containsImage: boolean;
        containsLabel: boolean;
        fill: boolean;
        glass: boolean;
        rounded: boolean;
        comic: boolean;
        autoSize: boolean;
        image: boolean;
        shadow: boolean;
        lineJumps: boolean;
        resizable: boolean;
        table: boolean;
        cell: boolean;
        row: boolean;
        movable: boolean;
        rotatable: boolean;
    };
    /**
     * Returns information about the current selection.
     */
    updateSelectionStateForCell(result: any, cell: any): void;
    /**
     * Returns information about the current selection.
     */
    isFillState(state: any): any;
    /**
     * Returns information about the current selection.
     */
    isGlassState(state: any): boolean;
    /**
     * Returns information about the current selection.
     */
    isRoundedState(state: any): any;
    /**
     * Returns information about the current selection.
     */
    isLineJumpState(state: any): boolean;
    /**
     * Returns information about the current selection.
     */
    isComicState(state: any): boolean;
    /**
     * Returns information about the current selection.
     */
    isAutoSizeState(state: any): boolean;
    /**
     * Returns information about the current selection.
     */
    isImageState(state: any): boolean;
    /**
     * Returns information about the current selection.
     */
    isShadowState(state: any): boolean;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    clear(): void;
    panels: any[] | undefined;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    refresh(): void;
}
/**
 * Adds the label menu items to the given menu and parent.
 */
export function ArrangePanel(format: any, editorUi: any, container: any): void;
export class ArrangePanel {
    /**
     * Adds the label menu items to the given menu and parent.
     */
    constructor(format: any, editorUi: any, container: any);
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    /**
     *
     */
    addTable(div: any): any;
    /**
     *
     */
    addLayerOps(div: any): any;
    /**
     *
     */
    addGroupOps(div: any): any;
    /**
     *
     */
    addAlign(div: any): any;
    /**
     *
     */
    addFlip(div: any): any;
    /**
     *
     */
    addDistribute(div: any): any;
    /**
     *
     */
    addAngle(div: any): any;
    /**
     *
     */
    addGeometry(container: any): void;
    /**
     *
     */
    addGeometryHandler(input: any, fn: any): (evt: any) => void;
    addEdgeGeometryHandler(input: any, fn: any): (evt: any) => void;
    /**
     *
     */
    addEdgeGeometry(container: any): void;
}
/**
 * Base class for format panels.
 */
export function BaseFormatPanel(format: any, editorUi: any, container: any): void;
export class BaseFormatPanel {
    /**
     * Base class for format panels.
     */
    constructor(format: any, editorUi: any, container: any);
    format: any;
    editorUi: any;
    container: any;
    listeners: any[];
    /**
     *
     */
    buttonBackgroundColor: string;
    /**
     * Adds the given color option.
     */
    getSelectionState(): any;
    /**
     * Install input handler.
     */
    installInputHandler(input: any, key: any, defaultValue: any, min: any, max: any, unit: any, textEditFallback: any, isFloat: any): (evt: any) => void;
    /**
     * Adds the given option.
     */
    createPanel(): HTMLDivElement;
    /**
     * Adds the given option.
     */
    createTitle(title: any): HTMLDivElement;
    /**
     *
     */
    createStepper(input: any, update: any, step: any, height: any, disableFocus: any, defaultValue: any, isFloat: any): HTMLDivElement;
    /**
     * Adds the given option.
     */
    createOption(label: any, isCheckedFn: any, setCheckedFn: any, listener: any): HTMLDivElement;
    /**
     * The string 'null' means use null in values.
     */
    createCellOption(label: any, key: any, defaultValue: any, enabledValue: any, disabledValue: any, fn: any, action: any, stopEditing: any): HTMLDivElement;
    /**
     * Adds the given color option.
     */
    createColorOption(label: any, getColorFn: any, setColorFn: any, defaultColor: any, listener: any, callbackFn: any, hideCheckbox: any): HTMLDivElement;
    /**
     *
     */
    createCellColorOption(label: any, colorKey: any, defaultColor: any, callbackFn: any, setStyleFn: any): HTMLDivElement;
    /**
     *
     */
    addArrow(elt: any, height: any): any;
    /**
     *
     */
    addUnitInput(container: any, unit: any, right: any, width: any, update: any, step: any, marginTop: any, disableFocus: any, isFloat: any): HTMLInputElement;
    /**
     *
     */
    createRelativeOption(label: any, key: any, width: any, handler: any, init: any): HTMLDivElement;
    /**
     *
     */
    addLabel(div: any, title: any, right: any, width: any): void;
    /**
     *
     */
    addKeyHandler(input: any, listener: any): void;
    /**
     *
     */
    styleButtons(elts: any): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    destroy(): void;
    getUnit(): "pt" | "\"" | "mm" | undefined;
    inUnit(pixels: any): any;
    fromUnit(value: any): any;
    isFloatUnit(): boolean;
    getUnitStep(): 1 | 0.5 | 0.1 | undefined;
}
/**
 * Adds the label menu items to the given menu and parent.
 */
export function DiagramFormatPanel(format: any, editorUi: any, container: any): void;
export class DiagramFormatPanel {
    /**
     * Adds the label menu items to the given menu and parent.
     */
    constructor(format: any, editorUi: any, container: any);
    /**
     * Specifies if the background image option should be shown. Default is true.
     */
    showBackgroundImageOption: boolean;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addView(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addOptions(div: any): any;
    /**
     *
     */
    addGridOption(container: any): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addDocumentProperties(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addPaperSize(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addStyleOps(div: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    destroy(...args: any[]): void;
    gridEnabledListener: any;
}
export namespace DiagramFormatPanel {
    let showPageView: boolean;
}
