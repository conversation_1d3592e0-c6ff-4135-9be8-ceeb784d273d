/**
 * Copyright (c) 2006-2020, JGraph Ltd
 * Copyright (c) 2006-2020, draw.io AG
 *
 * Constructs the actions object for the given UI.
 */
export function Actions(editorUi: any): void;
export class Actions {
    /**
     * Copyright (c) 2006-2020, JGraph Ltd
     * Copyright (c) 2006-2020, draw.io AG
     *
     * Constructs the actions object for the given UI.
     */
    constructor(editorUi: any);
    editorUi: any;
    actions: Object;
    /**
     * Adds the default actions.
     */
    init(): void;
    /**
     * Registers the given action under the given name.
     */
    addAction(key: any, funct: any, enabled: any, iconCls: any, shortcut: any): any;
    /**
     * Registers the given action under the given name.
     */
    put(name: any, action: any): any;
    /**
     * Returns the action for the given name or null if no such action exists.
     */
    get(name: any): any;
}
/**
 * Constructs a new action for the given parameters.
 */
export function Action(label: any, funct: any, enabled: any, iconCls: any, shortcut: any): void;
export class Action {
    /**
     * Constructs a new action for the given parameters.
     */
    constructor(label: any, funct: any, enabled: any, iconCls: any, shortcut: any);
    label: any;
    funct: any;
    enabled: any;
    iconCls: any;
    shortcut: any;
    visible: boolean;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    createFunction(funct: any): any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    setEnabled(value: any): void;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    isEnabled(): any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    setToggleAction(value: any): void;
    toggleAction: any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    setSelectedCallback(funct: any): void;
    selectedCallback: any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    isSelected(): any;
}
