export function mxURI(uri: any): {
    scheme: any;
    authority: any;
    path: string;
    query: any;
    fragment: any;
    parse(uri: any): any;
    toString(): string;
    toAbsolute(base: any): any;
};
export function mxColor(target: any, prop: any): {
    required: boolean;
    adjust: boolean;
    hash: boolean;
    caps: boolean;
    slider: boolean;
    valueElement: any;
    styleElement: any;
    onImmediateChange: any;
    hsv: number[];
    rgb: number[];
    pickerOnfocus: boolean;
    pickerMode: string;
    pickerPosition: string;
    pickerSmartPosition: boolean;
    pickerButtonHeight: number;
    pickerClosable: boolean;
    pickerCloseText: string;
    pickerButtonColor: string;
    pickerFace: number;
    pickerFaceColor: string;
    pickerBorder: number;
    pickerBorderColor: string;
    pickerInset: number;
    pickerInsetColor: string;
    pickerZIndex: number;
    hidePicker(): void;
    showPicker(): void;
    importColor(): void;
    exportColor(flags: any): void;
    fromHSV(h: any, s: any, v: any, flags: any): void;
    fromRGB(r: any, g: any, b: any, flags: any): void;
    fromString(hex: any, flags: any): boolean;
    toString(): string;
};
export namespace mxJSColor {
    let dir: string;
    let bindClass: string;
    let binding: boolean;
    let preloading: boolean;
    function init(): void;
    function getDir(): string;
    function detectDir(): any;
    function preload(): void;
    namespace images {
        let pad: number[];
        let sld: number[];
        let cross: number[];
        let arrow: number[];
    }
    let imgRequire: {};
    let imgLoaded: {};
    function requireImage(filename: any): void;
    function loadImage(filename: any): void;
    function fetchElement(mixed: any): any;
    function addEvent(el: any, evnt: any, func: any): void;
    function fireEvent(el: any, evnt: any): void;
    function getElementPos(e: any): number[];
    function getElementSize(e: any): any[];
    function getRelMousePos(e: any): {
        x: number;
        y: number;
    };
    function getViewPos(): number[];
    function getViewSize(): number[];
}
