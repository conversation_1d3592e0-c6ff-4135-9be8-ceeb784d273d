"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertXMLToSVG = void 0;
var mxgraph_1 = require("../core/mxgraph");
var default_1 = require("../styles/default");
var is_1 = require("laser-utils/dist/es/is");
var xml_1 = require("./xml");
var XMLNS = "http://www.w3.org/2000/svg";
var convertXMLToSVG = function (xml, style) {
    var element = document.createElement("div");
    var doc = (0, is_1.isString)(xml) ? (0, xml_1.stringToXml)(xml) : xml;
    var stylesheet = style
        ? (0, is_1.isString)(style)
            ? (0, xml_1.stringToXml)(style)
            : style
        : (0, xml_1.stringToXml)(default_1.DEFAULT_STYLE_XML);
    if (doc) {
        var graph = new mxgraph_1.mxGraph(element);
        var codec = new mxgraph_1.mxCodec(doc);
        graph.model.beginUpdate();
        graph.setEnabled(false);
        codec.decode(doc.documentElement, graph.getModel());
        stylesheet && codec.decode(stylesheet.documentElement, graph.getStylesheet());
        graph.model.endUpdate();
        var svg = document.createElementNS(XMLNS, "svg");
        var bounds = graph.getGraphBounds();
        svg.setAttribute("xmlns", XMLNS);
        svg.setAttribute("width", bounds.width.toString());
        svg.setAttribute("height", bounds.height.toString());
        svg.setAttribute("viewBox", "0 0 ".concat(bounds.width, " ").concat(bounds.height));
        svg.setAttribute("version", "1.1");
        var canvas = new mxgraph_1.mxSvgCanvas2D(svg);
        canvas.translate(-bounds.x, -bounds.y);
        var exporter = new mxgraph_1.mxImageExport();
        var state = graph.getView().getState(graph.model.root);
        exporter.drawState(state, canvas);
        graph.destroy();
        return svg;
    }
    return null;
};
exports.convertXMLToSVG = convertXMLToSVG;
