"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeSVGDownloader = exports.svgToBase64 = exports.base64ToSvgString = exports.stringToSvg = exports.svgToString = void 0;
var is_1 = require("laser-utils/dist/es/is");
var js_base64_1 = require("js-base64");
var svgToString = function (svg) {
    if (!svg)
        return null;
    try {
        var serialize = new XMLSerializer();
        return serialize.serializeToString(svg);
    }
    catch (error) {
        console.log("SvgToString Error: ", error);
        return null;
    }
};
exports.svgToString = svgToString;
var stringToSvg = function (str) {
    try {
        var parser = new DOMParser();
        return parser.parseFromString(str, "image/svg+xml").firstChild;
    }
    catch (error) {
        console.log("StringToSvg Error: ", error);
        return null;
    }
};
exports.stringToSvg = stringToSvg;
var base64ToSvgString = function (base64) {
    try {
        var svg = js_base64_1.Base64.decode(base64.replace("data:image/svg+xml;base64,", ""));
        return svg;
    }
    catch (error) {
        console.log("base64ToSvgString Error: ", error);
        return null;
    }
};
exports.base64ToSvgString = base64ToSvgString;
var svgToBase64 = function (svg) {
    var svgString = (0, is_1.isString)(svg) ? svg : (0, exports.svgToString)(svg);
    if (svgString) {
        return "data:image/svg+xml;base64,".concat(js_base64_1.Base64.encode(svgString));
    }
    return null;
};
exports.svgToBase64 = svgToBase64;
var makeSVGDownloader = function (svg, name) {
    if (name === void 0) { name = "image.jpg"; }
    return new Promise(function (r) {
        var svgBase64 = (0, exports.svgToBase64)(svg);
        if (!svgBase64) {
            r(null);
            return void 0;
        }
        var image = new Image();
        image.crossOrigin = "anonymous";
        image.onload = function () {
            var canvas = document.createElement("canvas");
            var ratio = window.devicePixelRatio || 1;
            canvas.width = image.width * ratio;
            canvas.height = image.height * ratio;
            var ctx = canvas.getContext("2d");
            ctx.scale(ratio, ratio);
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(image, 0, 0);
            var func = function () {
                var link = document.createElement("a");
                link.download = name;
                link.href = canvas.toDataURL("image/jpeg");
                link.click();
            };
            r(func);
        };
        image.src = svgBase64;
    });
};
exports.makeSVGDownloader = makeSVGDownloader;
