"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mxObjectIdentity = exports.mxCellState = exports.mxEllipse = exports.mxConstraintHandler = exports.mxStencil = exports.mxStencilRegistry = exports.mxFastOrganicLayout = exports.mxCircleLayout = exports.mxHierarchicalLayout = exports.mxCompactTreeLayout = exports.mxLayoutManager = exports.mxCellHighlight = exports.mxObjectCodec = exports.mxGraphLayout = exports.mxImage = exports.mxGuide = exports.mxDragSource = exports.mxCellRenderer = exports.mxEdgeHandler = exports.mxClipboard = exports.mxSelectionCellsHandler = exports.mxEdgeStyle = exports.mxShape = exports.mxGraphModel = exports.mxRubberband = exports.mxText = exports.mxUndoManager = exports.mxPopupMenuHandler = exports.mxRectangleShape = exports.mxCellMarker = exports.mxConnectionHandler = exports.mxGraphHandler = exports.mxPolyline = exports.mxMouseEvent = exports.mxGraphView = exports.mxPoint = exports.mxPopupMenu = exports.mxDivResizer = exports.mxRectangle = exports.mxClient = exports.mxUtils = exports.mxEvent = exports.mxEventObject = exports.mxResources = exports.mxEventSource = exports.mxImageExport = exports.mxSvgCanvas2D = exports.mxConstants = exports.mxCodec = exports.mxGraph = void 0;
exports.mxCodecRegistry = exports.mxArrow = exports.mxCloud = exports.mxTriangle = exports.mxLine = exports.mxHandle = exports.mxLabel = exports.mxSwimlane = exports.mxHexagon = exports.mxDoubleEllipse = exports.mxArrowConnector = exports.mxPerimeter = exports.mxRhombus = exports.mxCylinder = exports.mxMarker = exports.mxActor = exports.mxRadialTreeLayout = exports.mxMorphing = exports.mxWindow = exports.mxForm = exports.mxXmlCanvas2D = exports.mxXmlRequest = exports.mxGeometry = exports.mxCell = exports.mxKeyHandler = exports.mxStyleRegistry = exports.mxConnector = exports.mxStackLayout = exports.mxImageShape = exports.mxElbowEdgeHandler = exports.mxPanningHandler = exports.mxOutline = exports.mxVertexHandler = exports.mxCellEditor = exports.mxConnectionConstraint = exports.mxDictionary = void 0;
var mxgraph_1 = __importDefault(require("mxgraph"));
window.mxBasePath = "static";
window.mxLoadResources = false;
window.mxForceIncludes = false;
window.mxLoadStylesheets = false;
window.mxResourceExtension = ".txt";
// https://github.com/jgraph/mxgraph/issues/479
var mx = (0, mxgraph_1.default)();
// 需要用到的再引用 实际上还是把所有的包都打进来了
exports.mxGraph = mx.mxGraph, exports.mxCodec = mx.mxCodec, exports.mxConstants = mx.mxConstants, exports.mxSvgCanvas2D = mx.mxSvgCanvas2D, exports.mxImageExport = mx.mxImageExport, exports.mxEventSource = mx.mxEventSource, exports.mxResources = mx.mxResources, exports.mxEventObject = mx.mxEventObject, exports.mxEvent = mx.mxEvent, exports.mxUtils = mx.mxUtils, exports.mxClient = mx.mxClient, exports.mxRectangle = mx.mxRectangle, exports.mxDivResizer = mx.mxDivResizer, exports.mxPopupMenu = mx.mxPopupMenu, exports.mxPoint = mx.mxPoint, exports.mxGraphView = mx.mxGraphView, exports.mxMouseEvent = mx.mxMouseEvent, exports.mxPolyline = mx.mxPolyline, exports.mxGraphHandler = mx.mxGraphHandler, exports.mxConnectionHandler = mx.mxConnectionHandler, exports.mxCellMarker = mx.mxCellMarker, exports.mxRectangleShape = mx.mxRectangleShape, exports.mxPopupMenuHandler = mx.mxPopupMenuHandler, exports.mxUndoManager = mx.mxUndoManager, exports.mxText = mx.mxText, exports.mxRubberband = mx.mxRubberband, exports.mxGraphModel = mx.mxGraphModel, exports.mxShape = mx.mxShape, exports.mxEdgeStyle = mx.mxEdgeStyle, exports.mxSelectionCellsHandler = mx.mxSelectionCellsHandler, exports.mxClipboard = mx.mxClipboard, exports.mxEdgeHandler = mx.mxEdgeHandler, exports.mxCellRenderer = mx.mxCellRenderer, exports.mxDragSource = mx.mxDragSource, exports.mxGuide = mx.mxGuide, exports.mxImage = mx.mxImage, exports.mxGraphLayout = mx.mxGraphLayout, exports.mxObjectCodec = mx.mxObjectCodec, exports.mxCellHighlight = mx.mxCellHighlight, exports.mxLayoutManager = mx.mxLayoutManager, exports.mxCompactTreeLayout = mx.mxCompactTreeLayout, exports.mxHierarchicalLayout = mx.mxHierarchicalLayout, exports.mxCircleLayout = mx.mxCircleLayout, exports.mxFastOrganicLayout = mx.mxFastOrganicLayout, exports.mxStencilRegistry = mx.mxStencilRegistry, exports.mxStencil = mx.mxStencil, exports.mxConstraintHandler = mx.mxConstraintHandler, exports.mxEllipse = mx.mxEllipse, exports.mxCellState = mx.mxCellState, exports.mxObjectIdentity = mx.mxObjectIdentity, exports.mxDictionary = mx.mxDictionary, exports.mxConnectionConstraint = mx.mxConnectionConstraint, exports.mxCellEditor = mx.mxCellEditor, exports.mxVertexHandler = mx.mxVertexHandler, exports.mxOutline = mx.mxOutline, exports.mxPanningHandler = mx.mxPanningHandler, exports.mxElbowEdgeHandler = mx.mxElbowEdgeHandler, exports.mxImageShape = mx.mxImageShape, exports.mxStackLayout = mx.mxStackLayout, exports.mxConnector = mx.mxConnector, exports.mxStyleRegistry = mx.mxStyleRegistry, exports.mxKeyHandler = mx.mxKeyHandler, exports.mxCell = mx.mxCell, exports.mxGeometry = mx.mxGeometry, exports.mxXmlRequest = mx.mxXmlRequest, exports.mxXmlCanvas2D = mx.mxXmlCanvas2D, exports.mxForm = mx.mxForm, exports.mxWindow = mx.mxWindow, exports.mxMorphing = mx.mxMorphing, exports.mxRadialTreeLayout = mx.mxRadialTreeLayout, exports.mxActor = mx.mxActor, exports.mxMarker = mx.mxMarker, exports.mxCylinder = mx.mxCylinder, exports.mxRhombus = mx.mxRhombus, exports.mxPerimeter = mx.mxPerimeter, exports.mxArrowConnector = mx.mxArrowConnector, exports.mxDoubleEllipse = mx.mxDoubleEllipse, exports.mxHexagon = mx.mxHexagon, exports.mxSwimlane = mx.mxSwimlane, exports.mxLabel = mx.mxLabel, exports.mxHandle = mx.mxHandle, exports.mxLine = mx.mxLine, exports.mxTriangle = mx.mxTriangle, exports.mxCloud = mx.mxCloud, exports.mxArrow = mx.mxArrow, exports.mxCodecRegistry = mx.mxCodecRegistry;
// https://github.com/maxGraph/maxGraph/issues/102
// https://github.com/jgraph/mxgraph/blob/master/javascript/src/js/io/mxCodec.js#L423
exports.mxCodec.prototype.decode = function (node, into) {
    this.updateElements();
    var obj = null;
    if (node && node.nodeType == exports.mxConstants.NODETYPE_ELEMENT) {
        var ctor = null;
        try {
            // @ts-expect-error 需要处理的 XML Node 可能不在 Window 上
            ctor = mx[node.nodeName] || window[node.nodeName];
        }
        catch (error) {
            console.log("NODE ".concat(node.nodeName, " IS NOT FOUND"), error);
        }
        var dec = mx.mxCodecRegistry.getCodec(ctor);
        if (dec) {
            obj = dec.decode(this, node, into);
        }
        else {
            obj = node.cloneNode(true);
            obj && obj.removeAttribute("as");
        }
    }
    return obj;
};
// https://github.com/jgraph/mxgraph/issues/58
exports.mxUtils.getScrollOrigin = function (node, includeAncestors, includeDocument) {
    includeAncestors = includeAncestors != null ? includeAncestors : false;
    includeDocument = includeDocument != null ? includeDocument : false;
    var doc = node != null ? node.ownerDocument : document;
    var b = doc.body;
    var d = doc.documentElement;
    var result = new exports.mxPoint();
    var fixed = false;
    while (node != null && node != b && node != d) {
        if (!isNaN(node.scrollLeft) && !isNaN(node.scrollTop)) {
            result.x += node.scrollLeft;
            result.y += node.scrollTop;
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        var style = exports.mxUtils.getCurrentStyle(node);
        if (style != null) {
            fixed = fixed || style.position == "fixed";
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        node = includeAncestors ? node.parentNode : null;
    }
    if (!fixed && includeDocument) {
        var origin_1 = exports.mxUtils.getDocumentScrollOrigin(doc);
        result.x += origin_1.x;
        result.y += origin_1.y;
    }
    return result;
};
// https://github.com/WindrunnerMax/FlowChartEditor/issues/4
exports.mxSvgCanvas2D.prototype.createClip = function (x2, y2, w3, h3) {
    x2 = Math.round(x2);
    y2 = Math.round(y2);
    w3 = Math.round(w3);
    h3 = Math.round(h3);
    var id = "mx-clip-".concat(x2, "-").concat(y2, "-").concat(w3, "-").concat(h3);
    var counter = 0;
    var tmp = "".concat(id, "-").concat(counter);
    while (document.getElementById(tmp) != null) {
        tmp = "".concat(id, "-").concat(++counter);
    }
    var clip = this.createElement("clipPath");
    clip.setAttribute("id", tmp);
    var rect = this.createElement("rect");
    rect.setAttribute("x", x2.toString());
    rect.setAttribute("y", y2.toString());
    rect.setAttribute("width", w3.toString());
    rect.setAttribute("height", h3.toString());
    clip.appendChild(rect);
    return clip;
};
// https://github.com/WindrunnerMax/FlowChartEditor/issues/4
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
exports.mxPopupMenu.prototype.createSubmenu = function (parent) {
    parent.table = document.createElement("table");
    parent.table.className = "mxPopupMenu";
    parent.tbody = document.createElement("tbody");
    parent.table.appendChild(parent.tbody);
    parent.div = document.createElement("div");
    parent.div.className = "mxPopupMenu";
    parent.div.style.position = "absolute";
    parent.div.style.display = "inline";
    parent.div.style.zIndex = this.zIndex.toString();
    parent.div.appendChild(parent.table);
    var img = document.createElement("img");
    img.setAttribute("src", this.submenuImage);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    var td = parent.firstChild.nextSibling.nextSibling;
    td.appendChild(img);
};
