import type { Language } from "../editor/i18n";
import { getLanguage } from "../editor/i18n";
import type { A, F } from "laser-utils/dist/es/types";
export declare class DiagramEditor {
    private container;
    private onExit;
    private editor;
    private editorUi;
    private diagramContainer;
    constructor(container: HTMLElement, onExit: () => void);
    start: (lang: Language, init?: XMLDocument | null, onXMLChange?: ((xml: Element, changes: A.Any) => void) | undefined) => void;
    getXML: () => Element | null;
    exit: () => void;
    static getLang: (lang: F.Args<typeof getLanguage>["0"]) => Promise<Language>;
}
