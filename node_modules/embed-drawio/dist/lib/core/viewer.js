"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagramViewer = void 0;
require("../editor/js/Shapes");
var xml_1 = require("../utils/xml");
var default_1 = require("../styles/default");
var Graph_1 = require("../editor/js/Graph");
var mxgraph_1 = require("./mxgraph");
var themes = {};
themes[Graph_1.Graph.prototype.defaultThemeName] = (0, xml_1.stringToXml)(default_1.DEFAULT_STYLE_XML).documentElement;
var DiagramViewer = /** @class */ (function () {
    function DiagramViewer(xml) {
        var _this = this;
        this.xml = xml;
        this.renderSVG = function (background, scale, border) {
            if (scale === void 0) { scale = 1; }
            if (border === void 0) { border = 1; }
            if (!_this.graph)
                return null;
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var model = _this.graph.getModel();
            _this.xml && new mxgraph_1.mxCodec(_this.xml).decode(_this.xml.documentElement, model);
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var svg = _this.graph.getSvg(background, scale, border);
            return svg;
        };
        this.destroy = function () {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            _this.graph && _this.graph.destroy();
            _this.container && mxgraph_1.mxEvent.removeAllListeners(_this.container);
            _this.graph = null;
            _this.container = null;
        };
        var container = document.createElement("div");
        var graph = new Graph_1.Graph(container, null, null, null, themes, true);
        this.container = container;
        this.graph = graph;
    }
    DiagramViewer.xmlToSvg = function (xml, background, scale, border) {
        if (scale === void 0) { scale = 1; }
        if (border === void 0) { border = 1; }
        if (!xml)
            return null;
        var viewer = new DiagramViewer(xml);
        var svg = viewer.renderSVG(background, scale, border);
        viewer.destroy();
        return svg;
    };
    return DiagramViewer;
}());
exports.DiagramViewer = DiagramViewer;
