"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagramEditor = void 0;
var xml_1 = require("../utils/xml");
var default_1 = require("../styles/default");
var editor_1 = require("../editor");
var mxgraph_1 = require("./mxgraph");
var i18n_1 = require("../editor/i18n");
var themes = {};
themes[editor_1.Graph.prototype.defaultThemeName] = (0, xml_1.stringToXml)(default_1.DEFAULT_STYLE_XML).documentElement;
var DiagramEditor = /** @class */ (function () {
    function DiagramEditor(container, onExit) {
        var _this = this;
        this.container = container;
        this.onExit = onExit;
        this.start = function (lang, init, onXMLChange) {
            _this.container.appendChild(_this.diagramContainer);
            _this.container.style.overflow = "hidden";
            mxgraph_1.mxResources.parse(lang);
            _this.editor = new editor_1.Editor(false, themes);
            _this.editorUi = new editor_1.EditorUi(_this.editor, _this.diagramContainer, null, _this.onExit);
            if (init) {
                _this.editorUi.editor.setGraphXml(init.documentElement);
            }
            _this.editor.graph.getModel().addListener(mxgraph_1.mxEvent.CHANGE, function (_, event) {
                if (onXMLChange) {
                    var changes = [];
                    if (event && event.properties && event.properties.changes) {
                        changes = event.properties.changes;
                    }
                    onXMLChange(_this.editorUi && _this.editorUi.editor.getGraphXml(), changes);
                }
            });
        };
        this.getXML = function () {
            return _this.editorUi && _this.editorUi.editor.getGraphXml();
        };
        this.exit = function () {
            _this.container.style.overflow = "";
            mxgraph_1.mxEvent.removeAllListeners(window);
            mxgraph_1.mxEvent.removeAllListeners(document);
            _this.editor && _this.editor.destroy();
            _this.editorUi && _this.editorUi.destroy();
            _this.container.removeChild(_this.diagramContainer);
        };
        this.editor = null;
        this.editorUi = null;
        this.diagramContainer = document.createElement("div");
        this.diagramContainer.className = "diagram-container geEditor";
    }
    DiagramEditor.getLang = function (lang) {
        return (0, i18n_1.getLanguage)(lang);
    };
    return DiagramEditor;
}());
exports.DiagramEditor = DiagramEditor;
