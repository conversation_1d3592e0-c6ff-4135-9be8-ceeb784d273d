import { isString } from "laser-utils/dist/es/is";
import { Base64 } from "js-base64";
export var svgToString = function (svg) {
    if (!svg)
        return null;
    try {
        var serialize = new XMLSerializer();
        return serialize.serializeToString(svg);
    }
    catch (error) {
        console.log("SvgToString Error: ", error);
        return null;
    }
};
export var stringToSvg = function (str) {
    try {
        var parser = new DOMParser();
        return parser.parseFromString(str, "image/svg+xml").firstChild;
    }
    catch (error) {
        console.log("StringToSvg Error: ", error);
        return null;
    }
};
export var base64ToSvgString = function (base64) {
    try {
        var svg = Base64.decode(base64.replace("data:image/svg+xml;base64,", ""));
        return svg;
    }
    catch (error) {
        console.log("base64ToSvgString Error: ", error);
        return null;
    }
};
export var svgToBase64 = function (svg) {
    var svgString = isString(svg) ? svg : svgToString(svg);
    if (svgString) {
        return "data:image/svg+xml;base64,".concat(Base64.encode(svgString));
    }
    return null;
};
export var makeSVGDownloader = function (svg, name) {
    if (name === void 0) { name = "image.jpg"; }
    return new Promise(function (r) {
        var svgBase64 = svgToBase64(svg);
        if (!svgBase64) {
            r(null);
            return void 0;
        }
        var image = new Image();
        image.crossOrigin = "anonymous";
        image.onload = function () {
            var canvas = document.createElement("canvas");
            var ratio = window.devicePixelRatio || 1;
            canvas.width = image.width * ratio;
            canvas.height = image.height * ratio;
            var ctx = canvas.getContext("2d");
            ctx.scale(ratio, ratio);
            ctx.fillStyle = "#fff";
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(image, 0, 0);
            var func = function () {
                var link = document.createElement("a");
                link.download = name;
                link.href = canvas.toDataURL("image/jpeg");
                link.click();
            };
            r(func);
        };
        image.src = svgBase64;
    });
};
