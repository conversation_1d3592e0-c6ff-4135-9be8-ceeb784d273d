import { mxGraph, mxCodec, mxSvgCanvas2D, mxImageExport } from "../core/mxgraph";
import { DEFAULT_STYLE_XML } from "../styles/default";
import { isString } from "laser-utils/dist/es/is";
import { stringToXml } from "./xml";
var XMLNS = "http://www.w3.org/2000/svg";
export var convertXMLToSVG = function (xml, style) {
    var element = document.createElement("div");
    var doc = isString(xml) ? stringToXml(xml) : xml;
    var stylesheet = style
        ? isString(style)
            ? stringToXml(style)
            : style
        : stringToXml(DEFAULT_STYLE_XML);
    if (doc) {
        var graph = new mxGraph(element);
        var codec = new mxCodec(doc);
        graph.model.beginUpdate();
        graph.setEnabled(false);
        codec.decode(doc.documentElement, graph.getModel());
        stylesheet && codec.decode(stylesheet.documentElement, graph.getStylesheet());
        graph.model.endUpdate();
        var svg = document.createElementNS(XMLNS, "svg");
        var bounds = graph.getGraphBounds();
        svg.setAttribute("xmlns", XMLNS);
        svg.setAttribute("width", bounds.width.toString());
        svg.setAttribute("height", bounds.height.toString());
        svg.setAttribute("viewBox", "0 0 ".concat(bounds.width, " ").concat(bounds.height));
        svg.setAttribute("version", "1.1");
        var canvas = new mxSvgCanvas2D(svg);
        canvas.translate(-bounds.x, -bounds.y);
        var exporter = new mxImageExport();
        var state = graph.getView().getState(graph.model.root);
        exporter.drawState(state, canvas);
        graph.destroy();
        return svg;
    }
    return null;
};
