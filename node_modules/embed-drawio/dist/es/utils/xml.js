export var xmlToString = function (xml) {
    if (!xml)
        return null;
    try {
        var serialize = new XMLSerializer();
        return serialize.serializeToString(xml);
    }
    catch (error) {
        console.log("XmlToString Error: ", error);
        return null;
    }
};
export var stringToXml = function (str) {
    try {
        var parser = new DOMParser();
        return parser.parseFromString(str, "text/xml");
    }
    catch (error) {
        console.log("StringToXml Error: ", error);
        return null;
    }
};
