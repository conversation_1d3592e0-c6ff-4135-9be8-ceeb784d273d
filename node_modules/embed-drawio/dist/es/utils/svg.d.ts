import type { Func } from "laser-utils/dist/es/types";
export declare const svgToString: (svg: Node | null) => string | null;
export declare const stringToSvg: (str: string) => SVGElement | null;
export declare const base64ToSvgString: (base64: string) => string | null;
export declare const svgToBase64: (svg: string | SVGElement) => string | null;
export declare const makeSVGDownloader: (svg: string | SVGElement, name?: string) => Promise<Func.Plain | null>;
