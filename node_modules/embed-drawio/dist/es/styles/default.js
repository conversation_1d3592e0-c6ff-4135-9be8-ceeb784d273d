// https://github.com/jgraph/mxgraph/tree/master/javascript/examples/grapheditor/www/styles
export var DEFAULT_STYLE_XML = "<mxStylesheet><add as=\"defaultVertex\"><add as=\"shape\" value=\"label\"/><add as=\"perimeter\" value=\"rectanglePerimeter\"/><add as=\"fontSize\" value=\"12\"/><add as=\"fontFamily\" value=\"Helvetica\"/><add as=\"align\" value=\"center\"/><add as=\"verticalAlign\" value=\"middle\"/><add as=\"fillColor\" value=\"#ffffff\"/><add as=\"strokeColor\" value=\"#000000\"/><add as=\"fontColor\" value=\"#000000\"/></add><add as=\"defaultEdge\"><add as=\"shape\" value=\"connector\"/><add as=\"labelBackgroundColor\" value=\"#ffffff\"/><add as=\"endArrow\" value=\"classic\"/><add as=\"fontSize\" value=\"11\"/><add as=\"fontFamily\" value=\"Helvetica\"/><add as=\"align\" value=\"center\"/><add as=\"verticalAlign\" value=\"middle\"/><add as=\"rounded\" value=\"1\"/><add as=\"strokeColor\" value=\"#000000\"/><add as=\"fontColor\" value=\"#000000\"/></add><add as=\"text\"><add as=\"fillColor\" value=\"none\"/><add as=\"gradientColor\" value=\"none\"/><add as=\"strokeColor\" value=\"none\"/><add as=\"align\" value=\"left\"/><add as=\"verticalAlign\" value=\"top\"/></add><add as=\"edgeLabel\" extend=\"text\"><add as=\"labelBackgroundColor\" value=\"#ffffff\"/><add as=\"fontSize\" value=\"11\"/></add><add as=\"label\"><add as=\"fontStyle\" value=\"1\"/><add as=\"align\" value=\"left\"/><add as=\"verticalAlign\" value=\"middle\"/><add as=\"spacing\" value=\"2\"/><add as=\"spacingLeft\" value=\"52\"/><add as=\"imageWidth\" value=\"42\"/><add as=\"imageHeight\" value=\"42\"/><add as=\"rounded\" value=\"1\"/></add><add as=\"icon\" extend=\"label\"><add as=\"align\" value=\"center\"/><add as=\"imageAlign\" value=\"center\"/><add as=\"verticalLabelPosition\" value=\"bottom\"/><add as=\"verticalAlign\" value=\"top\"/><add as=\"spacingTop\" value=\"4\"/><add as=\"labelBackgroundColor\" value=\"#ffffff\"/><add as=\"spacing\" value=\"0\"/><add as=\"spacingLeft\" value=\"0\"/><add as=\"spacingTop\" value=\"6\"/><add as=\"fontStyle\" value=\"0\"/><add as=\"imageWidth\" value=\"48\"/><add as=\"imageHeight\" value=\"48\"/></add><add as=\"swimlane\"><add as=\"shape\" value=\"swimlane\"/><add as=\"fontSize\" value=\"12\"/><add as=\"fontStyle\" value=\"1\"/><add as=\"startSize\" value=\"23\"/></add><add as=\"group\"><add as=\"verticalAlign\" value=\"top\"/><add as=\"fillColor\" value=\"none\"/><add as=\"strokeColor\" value=\"none\"/><add as=\"gradientColor\" value=\"none\"/><add as=\"pointerEvents\" value=\"0\"/></add><add as=\"ellipse\"><add as=\"shape\" value=\"ellipse\"/><add as=\"perimeter\" value=\"ellipsePerimeter\"/></add><add as=\"rhombus\"><add as=\"shape\" value=\"rhombus\"/><add as=\"perimeter\" value=\"rhombusPerimeter\"/></add><add as=\"triangle\"><add as=\"shape\" value=\"triangle\"/><add as=\"perimeter\" value=\"trianglePerimeter\"/></add><add as=\"line\"><add as=\"shape\" value=\"line\"/><add as=\"strokeWidth\" value=\"4\"/><add as=\"labelBackgroundColor\" value=\"#ffffff\"/><add as=\"verticalAlign\" value=\"top\"/><add as=\"spacingTop\" value=\"8\"/></add><add as=\"image\"><add as=\"shape\" value=\"image\"/><add as=\"labelBackgroundColor\" value=\"white\"/><add as=\"verticalAlign\" value=\"top\"/><add as=\"verticalLabelPosition\" value=\"bottom\"/></add><add as=\"roundImage\" extend=\"image\"><add as=\"perimeter\" value=\"ellipsePerimeter\"/></add><add as=\"rhombusImage\" extend=\"image\"><add as=\"perimeter\" value=\"rhombusPerimeter\"/></add><add as=\"arrow\"><add as=\"shape\" value=\"arrow\"/><add as=\"edgeStyle\" value=\"none\"/><add as=\"fillColor\" value=\"#ffffff\"/></add>\n</mxStylesheet>";
