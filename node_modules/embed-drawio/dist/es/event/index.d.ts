import { EditorEvent } from "./basic";
import type { Config, ExportMsg, SaveMsg } from "./interface";
export declare class EditorBus extends EditorEvent {
    private lock;
    protected url: string;
    private config;
    protected iframe: HTMLIFrameElement | null;
    constructor(config?: Config);
    startEdit: () => undefined;
    exitEdit: () => void;
    onConfig(): void;
    onInit(): void;
    onLoad(): void;
    onAutoSave(msg: SaveMsg): void;
    onSave(msg: SaveMsg): void;
    onExit(msg: SaveMsg): void;
    onExport(msg: ExportMsg): void;
}
