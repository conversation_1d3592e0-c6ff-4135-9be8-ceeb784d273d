export var getLanguage = function (language) {
    if (language === void 0) { language = "zh"; }
    switch (language) {
        case "en":
            return import("./lang-en").then(function (r) { return r.langEN; });
        case "zh":
            return import("./lang-zh").then(function (r) { return r.langEN; });
        default:
            return import("./lang-en").then(function (r) { return r.langEN; });
    }
};
