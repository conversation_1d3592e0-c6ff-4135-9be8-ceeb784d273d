/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
/**
 * Construcs a new sidebar for the given editor.
 */
export function Sidebar(editorUi: any, container: any): void;
export class Sidebar {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    /**
     * Construcs a new sidebar for the given editor.
     */
    constructor(editorUi: any, container: any);
    editorUi: any;
    container: any;
    palettes: Object;
    taglist: Object;
    showTooltips: boolean;
    graph: any;
    pointerUpHandler: () => void;
    pointerDownHandler: () => void;
    pointerMoveHandler: (evt: any) => void;
    pointerOutHandler: (evt: any) => void;
    triangleUp: import("mxgraph").mxImage;
    triangleRight: import("mxgraph").mxImage;
    triangleDown: import("mxgraph").mxImage;
    triangleLeft: import("mxgraph").mxImage;
    refreshTarget: import("mxgraph").mxImage;
    roundDrop: import("mxgraph").mxImage;
    /**
     * Adds all palettes to the sidebar.
     */
    init(): void;
    /**
     * Sets the default font size.
     */
    collapsedImage: string;
    /**
     * Sets the default font size.
     */
    expandedImage: string;
    /**
     *
     */
    searchImage: string;
    /**
     *
     */
    dragPreviewBorder: string;
    /**
     * Specifies if tooltips should be visible. Default is true.
     */
    enableTooltips: boolean;
    /**
     * Specifies the delay for the tooltip. Default is 16 px.
     */
    tooltipBorder: number;
    /**
     * Specifies the delay for the tooltip. Default is 300 ms.
     */
    tooltipDelay: number;
    /**
     * Specifies the delay for the drop target icons. Default is 200 ms.
     */
    dropTargetDelay: number;
    /**
     * Specifies the URL of the gear image.
     */
    gearImage: string;
    /**
     * Specifies the width of the thumbnails.
     */
    thumbWidth: number;
    /**
     * Specifies the height of the thumbnails.
     */
    thumbHeight: number;
    /**
     * Specifies the width of the thumbnails.
     */
    minThumbStrokeWidth: number;
    /**
     * Specifies the width of the thumbnails.
     */
    thumbAntiAlias: boolean;
    /**
     * Specifies the padding for the thumbnails. Default is 3.
     */
    thumbPadding: number;
    /**
     * Specifies the delay for the tooltip. Default is 2 px.
     */
    thumbBorder: number;
    /**
     * Specifies the size of the sidebar titles.
     */
    sidebarTitleSize: number;
    /**
     * Specifies if titles in the sidebar should be enabled.
     */
    sidebarTitles: boolean;
    /**
     * Specifies if titles in the tooltips should be enabled.
     */
    tooltipTitles: boolean;
    /**
     * Specifies if titles in the tooltips should be enabled.
     */
    maxTooltipWidth: number;
    /**
     * Specifies if titles in the tooltips should be enabled.
     */
    maxTooltipHeight: number;
    /**
     * Specifies if stencil files should be loaded and added to the search index
     * when stencil palettes are added. If this is false then the stencil files
     * are lazy-loaded when the palette is shown.
     */
    addStencilsToIndex: boolean;
    /**
     * Specifies the width for clipart images. Default is 80.
     */
    defaultImageWidth: number;
    /**
     * Specifies the height for clipart images. Default is 80.
     */
    defaultImageHeight: number;
    /**
     * Adds all palettes to the sidebar.
     */
    getTooltipOffset(): import("mxgraph").mxPoint;
    /**
     * Adds all palettes to the sidebar.
     */
    showTooltip(elt: any, cells: any, w: any, h: any, title: any, showLabel: any): void;
    thread: number | null | undefined;
    currentElt: any;
    /**
     * Hides the current tooltip.
     */
    hideTooltip(): void;
    /**
     * Hides the current tooltip.
     */
    addDataEntry(tags: any, width: any, height: any, title: any, data: any): any;
    /**
     * Adds the give entries to the search index.
     */
    addEntries(images: any): void;
    /**
     * Hides the current tooltip.
     */
    addEntry(tags: any, fn: any): any;
    /**
     * Adds shape search UI.
     */
    searchEntries(searchTerms: any, count: any, page: any, success: any): void;
    /**
     * Adds shape search UI.
     */
    filterTags(tags: any): string | null;
    /**
     * Adds the general palette to the sidebar.
     */
    cloneCell(cell: any, value: any): any;
    /**
     * Adds shape search UI.
     */
    addSearchPalette(expand: any): void;
    /**
     * Adds the general palette to the sidebar.
     */
    insertSearchHint(div: any, searchTerm: any, count: any, page: any, results: any): void;
    /**
     * Adds the general palette to the sidebar.
     */
    addGeneralPalette(expand: any): void;
    /**
     * Adds the general palette to the sidebar.
     */
    addBasicPalette(): void;
    /**
     * Adds the general palette to the sidebar.
     */
    addMiscPalette(expand: any): void;
    /**
     * Adds the container palette to the sidebar.
     */
    addAdvancedPalette(expand: any): void;
    /**
     * Adds the container palette to the sidebar.
     */
    createAdvancedShapes(): any[];
    /**
     * Adds the general palette to the sidebar.
     */
    addUmlPalette(expand: any): void;
    /**
     * Adds the BPMN library to the sidebar.
     */
    addBpmnPalette(): void;
    /**
     * Creates and returns the given title element.
     */
    createTitle(label: any): HTMLAnchorElement;
    /**
     * Creates a thumbnail for the given cells.
     */
    createThumb(cells: any, width: any, height: any, parent: any, title: any, showLabel: any, showTitle: any): any;
    /**
     * Creates and returns a new palette item for the given image.
     */
    createItem(cells: any, title: any, showLabel: any, showTitle: any, width: any, height: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    updateShapes(source: any, targets: any): any[];
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createDropHandler(cells: any, allowSplit: any, allowCellsInserted: any, bounds: any): (graph: any, evt: any, target: any, x: any, y: any, force: any) => void;
    /**
     * Creates and returns a preview element for the given width and height.
     */
    createDragPreview(width: any, height: any): HTMLDivElement;
    /**
     * Creates a drag source for the given element.
     */
    dropAndConnect(source: any, targets: any, direction: any, dropCellIndex: any, evt: any): any;
    /**
     * Creates a drag source for the given element.
     */
    getDropAndConnectGeometry(source: any, target: any, direction: any, targets: any): any;
    /**
     * Limits drop style to non-transparent source shapes.
     */
    isDropStyleEnabled(cells: any, firstVertex: any): boolean;
    /**
     * Ignores swimlanes as drop style targets.
     */
    isDropStyleTargetIgnored(state: any): any;
    /**
     * Creates a drag source for the given element.
     */
    createDragSource(elt: any, dropHandler: any, preview: any, cells: any, bounds: any): import("mxgraph").mxDragSource;
    /**
     * Adds a handler for inserting the cell with a single click.
     */
    itemClicked(cells: any, ds: any, evt: any): void;
    /**
     * Adds a handler for inserting the cell with a single click.
     */
    addClickHandler(elt: any, ds: any, cells: any): void;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createVertexTemplateEntry(style: any, width: any, height: any, value: any, title: any, showLabel: any, showTitle: any, tags: any): any;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createVertexTemplate(style: any, width: any, height: any, value: any, title: any, showLabel: any, showTitle: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createVertexTemplateFromData(data: any, width: any, height: any, title: any, showLabel: any, showTitle: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createVertexTemplateFromCells(cells: any, width: any, height: any, title: any, showLabel: any, showTitle: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     *
     */
    createEdgeTemplateEntry(style: any, width: any, height: any, value: any, title: any, showLabel: any, tags: any, allowCellsInserted: any): any;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createEdgeTemplate(style: any, width: any, height: any, value: any, title: any, showLabel: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     * Creates a drop handler for inserting the given cells.
     */
    createEdgeTemplateFromCells(cells: any, width: any, height: any, title: any, showLabel: any, allowCellsInserted: any): HTMLAnchorElement;
    /**
     * Adds the given palette.
     */
    addPaletteFunctions(id: any, title: any, expanded: any, fns: any): void;
    /**
     * Adds the given palette.
     */
    addPalette(id: any, title: any, expanded: any, onInit: any): HTMLDivElement;
    /**
     * Create the given title element.
     */
    addFoldingHandler(title: any, content: any, funct: any): void;
    /**
     * Removes the palette for the given ID.
     */
    removePalette(id: any): boolean;
    /**
     * Adds the given image palette.
     */
    addImagePalette(id: any, title: any, prefix: any, postfix: any, items: any, titles: any, tags: any): void;
    /**
     * Creates the array of tags for the given stencil. Duplicates are allowed and will be filtered out later.
     */
    getTagsForStencil(packageName: any, stencilName: any, moreTags: any): any;
    /**
     * Adds the given stencil palette.
     */
    addStencilPalette(id: any, title: any, stencilFile: any, style: any, ignore: any, onInit: any, scale: any, tags: any, customFns: any, expand?: boolean): void;
    /**
     * Adds the given stencil palette.
     */
    destroy(): void;
}
