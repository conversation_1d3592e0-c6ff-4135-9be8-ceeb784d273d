/**
 * Construcs a new menubar for the given editor.
 */
export function Menubar(editorUi: any, container: any): void;
export class <PERSON>ubar {
    /**
     * Construcs a new menubar for the given editor.
     */
    constructor(editorUi: any, container: any);
    editorUi: any;
    container: any;
    /**
     * Adds the menubar elements.
     */
    hideMenu(): void;
    /**
     * Adds a submenu to this menubar.
     */
    addMenu(label: any, funct: any, before: any): HTMLAnchorElement;
    /**
     * Adds a handler for showing a menu in the given element.
     */
    addMenuHandler(elt: any, funct: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    destroy(): void;
}
/**
 * Constructs a new action for the given parameters.
 */
export function Menu(funct: any, enabled: any): void;
export class Menu {
    /**
     * Constructs a new action for the given parameters.
     */
    constructor(funct: any, enabled: any);
    funct: any;
    enabled: any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    isEnabled(): any;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    setEnabled(value: any): void;
    /**
     * Sets the enabled state of the action and fires a stateChanged event.
     */
    execute(menu: any, parent: any): void;
}
/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
/**
 * Constructs a new graph editor
 */
export function Menus(editorUi: any): void;
export class Menus {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    /**
     * Constructs a new graph editor
     */
    constructor(editorUi: any);
    editorUi: any;
    menus: Object;
    /**
     * Sets the default font family.
     */
    defaultFont: string;
    /**
     * Sets the default font size.
     */
    defaultFontSize: string;
    /**
     * Sets the default font size.
     */
    defaultMenuItems: string[];
    /**
     * Adds the label menu items to the given menu and parent.
     */
    defaultFonts: string[];
    /**
     * Adds the label menu items to the given menu and parent.
     */
    init(): void;
    customFonts: any[] | undefined;
    customFontSizes: any[] | undefined;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    put(name: any, menu: any): any;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    get(name: any): any;
    /**
     * Adds the given submenu.
     */
    addSubmenu(name: any, menu: any, parent: any, label: any): void;
    /**
     * Adds the label menu items to the given menu and parent.
     */
    addMenu(name: any, popupMenu: any, parent: any): void;
    /**
     * Adds a menu item to insert a table cell.
     */
    addInsertTableCellItem(menu: any, parent: any): void;
    /**
     * Adds a menu item to insert a table.
     */
    addInsertTableItem(menu: any, insertFn: any, parent: any): void;
    /**
     * Adds a style change item to the given menu.
     */
    edgeStyleChange(menu: any, label: any, keys: any, values: any, sprite: any, parent: any, reset: any): any;
    /**
     * Adds a style change item to the given menu.
     */
    styleChange(menu: any, label: any, keys: any, values: any, sprite: any, parent: any, fn: any, post: any): any;
    /**
     *
     */
    createStyleChangeFunction(keys: any, values: any): (post: any) => void;
    /**
     * Adds a style change item with a prompt to the given menu.
     */
    promptChange(menu: any, label: any, hint: any, defaultValue: any, key: any, parent: any, enabled: any, fn: any, sprite: any): any;
    /**
     * Adds a handler for showing a menu in the given element.
     */
    pickColor(key: any, cmd: any, defaultValue: any): void;
    colorDialog: ColorDialog | undefined;
    /**
     * Adds a handler for showing a menu in the given element.
     */
    toggleStyle(key: any, defaultValue: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addMenuItem(menu: any, key: any, parent: any, trigger: any, sprite: any, label: any): any;
    /**
     * Adds a checkmark to the given menuitem.
     */
    addShortcut(item: any, action: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addMenuItems(menu: any, keys: any, parent: any, trigger: any, sprites: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    createPopupMenu(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuHistoryItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuEditItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuStyleItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuArrangeItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuCellItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    addPopupMenuSelectionItems(menu: any, cell: any, evt: any): void;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    createMenubar(container: any): Menubar;
    /**
     * Creates the keyboard event handler for the current graph and history.
     */
    menuCreated(menu: any, elt: any, className: any): void;
}
import { ColorDialog } from "./Dialogs";
