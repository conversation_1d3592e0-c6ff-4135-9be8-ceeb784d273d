/**
 * Copyright (c) 2006-2012, JGraph Ltd
 */
/**
 * Construcs a new toolbar for the given editor.
 */
export function Toolbar(editorUi: any, container: any): void;
export class Toolbar {
    /**
     * Copyright (c) 2006-2012, JGraph Ltd
     */
    /**
     * Construcs a new toolbar for the given editor.
     */
    constructor(editorUi: any, container: any);
    editorUi: any;
    container: any;
    staticElements: any[];
    gestureHandler: (evt: any) => void;
    /**
     * Image for the dropdown arrow.
     */
    dropdownImage: string;
    /**
     * Image element for the dropdown arrow.
     */
    dropdownImageHtml: string;
    /**
     * Defines the background for selected buttons.
     */
    selectedBackground: string;
    /**
     * Defines the background for selected buttons.
     */
    unselectedBackground: string;
    /**
     * Adds the toolbar elements.
     */
    init(): void;
    updateZoom: (() => void) | undefined;
    edgeShapeMenu: HTMLAnchorElement | undefined;
    edgeStyleMenu: HTMLAnchorElement | undefined;
    /**
     * Adds the toolbar elements.
     */
    addTableDropDown(): HTMLAnchorElement;
    /**
     * Adds the toolbar elements.
     */
    addDropDownArrow(menu: any, sprite: any, width: any, atlasWidth: any, left: any, top: any, atlasDelta: any, atlasLeft: any): void;
    /**
     * Sets the current font name.
     */
    setFontName(value: any): void;
    /**
     * Sets the current font name.
     */
    setFontSize(value: any): void;
    /**
     * Hides the current menu.
     */
    createTextToolbar(): void;
    fontMenu: HTMLAnchorElement | undefined;
    sizeMenu: HTMLAnchorElement | undefined;
    /**
     * Hides the current menu.
     */
    hideMenu(): void;
    /**
     * Adds a label to the toolbar.
     */
    addMenu(label: any, tooltip: any, showLabels: any, name: any, c: any, showAll: any, ignoreState: any): HTMLAnchorElement;
    /**
     * Adds a label to the toolbar.
     */
    addMenuFunction(label: any, tooltip: any, showLabels: any, funct: any, c: any, showAll: any): HTMLAnchorElement;
    /**
     * Adds a label to the toolbar.
     */
    addMenuFunctionInContainer(container: any, label: any, tooltip: any, showLabels: any, funct: any, showAll: any): HTMLAnchorElement;
    /**
     * Adds a separator to the separator.
     */
    addSeparator(c: any): HTMLDivElement;
    /**
     * Adds given action item
     */
    addItems(keys: any, c: any, ignoreDisabled: any): (HTMLAnchorElement | HTMLDivElement | null)[];
    /**
     * Adds given action item
     */
    addItem(sprite: any, key: any, c: any, ignoreDisabled: any): HTMLAnchorElement | null;
    /**
     * Adds a button to the toolbar.
     */
    addButton(classname: any, tooltip: any, funct: any, c: any): HTMLAnchorElement;
    /**
     * Initializes the given toolbar element.
     */
    initElement(elt: any, tooltip: any): void;
    /**
     * Adds enabled state with setter to DOM node (avoids JS wrapper).
     */
    addEnabledState(elt: any): void;
    /**
     * Adds enabled state with setter to DOM node (avoids JS wrapper).
     */
    addClickHandler(elt: any, funct: any): void;
    /**
     * Creates and returns a new button.
     */
    createButton(classname: any): HTMLAnchorElement;
    /**
     * Creates and returns a new button.
     */
    createLabel(label: any): HTMLAnchorElement;
    /**
     * Adds a handler for showing a menu in the given element.
     */
    addMenuHandler(elt: any, showLabels: any, funct: any, showAll: any): void;
    /**
     * Adds a handler for showing a menu in the given element.
     */
    destroy(): void;
}
