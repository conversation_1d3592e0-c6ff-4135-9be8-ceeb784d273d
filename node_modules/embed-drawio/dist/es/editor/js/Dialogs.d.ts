/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/**
 * Constructs a new color dialog.
 */
export function ColorDialog(editorUi: any, color: any, apply: any, cancelFn: any): void;
export class ColorDialog {
    /**
     * Constructs a new color dialog.
     */
    constructor(editorUi: any, color: any, apply: any, cancelFn: any);
    editorUi: any;
    init: () => void;
    picker: any;
    colorInput: HTMLInputElement;
    container: HTMLDivElement;
    /**
     * Creates function to apply value
     */
    presetColors: string[];
    /**
     * Creates function to apply value
     */
    defaultColors: string[];
    /**
     * Creates function to apply value
     */
    createApplyFunction(): (color: any) => void;
}
export namespace ColorDialog {
    let recentColors: any[];
    /**
     * Adds recent color for later use.
     */
    function addRecentColor(color: any, max: any): void;
    /**
     * Adds recent color for later use.
     */
    function resetRecentColors(): void;
}
/**
 *
 */
export function OutlineWindow(editorUi: any, x: any, y: any, w: any, h: any): void;
export class OutlineWindow {
    /**
     *
     */
    constructor(editorUi: any, x: any, y: any, w: any, h: any);
    window: import("mxgraph").mxWindow;
    destroy: () => void;
}
/**
 *
 */
export function LayersWindow(editorUi: any, x: any, y: any, w: any, h: any): void;
export class LayersWindow {
    /**
     *
     */
    constructor(editorUi: any, x: any, y: any, w: any, h: any);
    window: import("mxgraph").mxWindow;
    init: () => void;
    refreshLayers: () => void;
    destroy: () => void;
}
/**
 * Constructs a new filename dialog.
 */
export function FilenameDialog(editorUi: any, filename: any, buttonText: any, fn: any, label: any, validateFn: any, content: any, helpLink: any, closeOnBtn: any, cancelFn: any, hints: any, w: any): void;
export class FilenameDialog {
    /**
     * Constructs a new filename dialog.
     */
    constructor(editorUi: any, filename: any, buttonText: any, fn: any, label: any, validateFn: any, content: any, helpLink: any, closeOnBtn: any, cancelFn: any, hints: any, w: any);
    init: () => void;
    container: HTMLTableElement;
}
export namespace FilenameDialog {
    let filenameHelpLink: any;
    /**
     *
     */
    function createTypeHint(ui: any, nameInput: any, hints: any): HTMLImageElement;
    /**
     *
     */
    function createFileTypes(editorUi: any, nameInput: any, types: any): HTMLSelectElement;
}
