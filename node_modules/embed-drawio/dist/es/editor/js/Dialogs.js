/* eslint-disable */
/* eslint-enable no-undef, prettier/prettier, no-unused-vars */
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import { mxConstants, mxResources, mxEventObject, mxEvent, mxUtils, mxClient, mxRectangle, mxPopupMenu, mxCell, mxWindow, } from "../../core/mxgraph";
import { checkmarkImage, closeImage, helpImage, lockedImage, noColorImage, unlockedImage, } from "../images/base64";
import { mxJSColor, mxColor } from "../jscolor/jscolor";
import { PRESENT_COLORS, DEFAULT_COLORS, EDITOR_UI, DIALOG } from "../utils/constant";
import { Graph } from "./Graph";
export { ColorDialog, OutlineWindow, LayersWindow, FilenameDialog };
/**
 * Constructs a new color dialog.
 */
function ColorDialog(editorUi, color, apply, cancelFn) {
    this.editorUi = editorUi;
    var input = document.createElement("input");
    input.style.marginBottom = "10px";
    input.style.width = "216px";
    // Required for picker to render in IE
    if (mxClient.IS_IE) {
        input.style.marginTop = "10px";
        document.body.appendChild(input);
    }
    var applyFunction = apply != null ? apply : this.createApplyFunction();
    function doApply() {
        var color = input.value;
        // Blocks any non-alphabetic chars in colors
        if (/(^#?[a-zA-Z0-9]*$)/.test(color)) {
            if (color != "none" && color.charAt(0) != "#") {
                color = "#" + color;
            }
            ColorDialog.addRecentColor(color != "none" ? color.substring(1) : color, 12);
            applyFunction(color);
            editorUi.hideDialog();
        }
        else {
            editorUi.handleError({ message: mxResources.get("invalidInput") });
        }
    }
    this.init = function () {
        if (!mxClient.IS_TOUCH) {
            input.focus();
        }
    };
    var picker = new mxColor(input);
    picker.pickerOnfocus = false;
    picker.showPicker();
    var div = document.createElement("div");
    mxJSColor.picker.box.style.position = "relative";
    mxJSColor.picker.box.style.width = "230px";
    mxJSColor.picker.box.style.height = "100px";
    mxJSColor.picker.box.style.paddingBottom = "10px";
    div.appendChild(mxJSColor.picker.box);
    var center = document.createElement("center");
    function createRecentColorTable() {
        var table = addPresets(ColorDialog.recentColors.length == 0 ? ["FFFFFF"] : ColorDialog.recentColors, 11, "FFFFFF", true);
        table.style.marginBottom = "8px";
        return table;
    }
    function addPresets(presets, rowLength, defaultColor, addResetOption) {
        rowLength = rowLength != null ? rowLength : 12;
        var table = document.createElement("table");
        table.style.borderCollapse = "collapse";
        table.setAttribute("cellspacing", "0");
        table.style.marginBottom = "20px";
        table.style.cellSpacing = "0px";
        var tbody = document.createElement("tbody");
        table.appendChild(tbody);
        var rows = presets.length / rowLength;
        for (var row = 0; row < rows; row++) {
            var tr = document.createElement("tr");
            for (var i = 0; i < rowLength; i++) {
                (function (clr) {
                    var td = document.createElement("td");
                    td.style.border = "1px solid black";
                    td.style.padding = "0px";
                    td.style.width = "16px";
                    td.style.height = "16px";
                    if (clr == null) {
                        clr = defaultColor;
                    }
                    if (clr == "none") {
                        td.style.background = "url('" + noColorImage + "')";
                    }
                    else {
                        td.style.backgroundColor = "#" + clr;
                    }
                    tr.appendChild(td);
                    if (clr != null) {
                        td.style.cursor = "pointer";
                        mxEvent.addListener(td, "click", function () {
                            if (clr == "none") {
                                picker.fromString("ffffff");
                                input.value = "none";
                            }
                            else {
                                picker.fromString(clr);
                            }
                        });
                        mxEvent.addListener(td, "dblclick", doApply);
                    }
                })(presets[row * rowLength + i]);
            }
            tbody.appendChild(tr);
        }
        if (addResetOption) {
            var td = document.createElement("td");
            td.setAttribute("title", mxResources.get("reset"));
            td.style.border = "1px solid black";
            td.style.padding = "0px";
            td.style.width = "16px";
            td.style.height = "16px";
            td.style.backgroundImage = "url('" + closeImage + "')";
            td.style.backgroundPosition = "center center";
            td.style.backgroundRepeat = "no-repeat";
            td.style.cursor = "pointer";
            tr.appendChild(td);
            mxEvent.addListener(td, "click", function () {
                ColorDialog.resetRecentColors();
                table.parentNode.replaceChild(createRecentColorTable(), table);
            });
        }
        center.appendChild(table);
        return table;
    }
    div.appendChild(input);
    mxUtils.br(div);
    // Adds recent colors
    createRecentColorTable();
    // Adds presets
    var table = addPresets(this.presetColors);
    table.style.marginBottom = "8px";
    table = addPresets(this.defaultColors);
    table.style.marginBottom = "16px";
    div.appendChild(center);
    var buttons = document.createElement("div");
    buttons.style.textAlign = "right";
    buttons.style.whiteSpace = "nowrap";
    var cancelBtn = mxUtils.button(mxResources.get("cancel"), function () {
        editorUi.hideDialog();
        if (cancelFn != null) {
            cancelFn();
        }
    });
    cancelBtn.className = "geBtn";
    if (editorUi.editor.cancelFirst) {
        buttons.appendChild(cancelBtn);
    }
    var applyBtn = mxUtils.button(mxResources.get("apply"), doApply);
    applyBtn.className = "geBtn gePrimaryBtn";
    buttons.appendChild(applyBtn);
    if (!editorUi.editor.cancelFirst) {
        buttons.appendChild(cancelBtn);
    }
    if (color != null) {
        if (color == "none") {
            picker.fromString("ffffff");
            input.value = "none";
        }
        else {
            picker.fromString(color);
        }
    }
    div.appendChild(buttons);
    this.picker = picker;
    this.colorInput = input;
    // LATER: Only fires if input if focused, should always
    // fire if this dialog is showing.
    mxEvent.addListener(div, "keydown", function (e) {
        if (e.keyCode == 27) {
            editorUi.hideDialog();
            if (cancelFn != null) {
                cancelFn();
            }
            mxEvent.consume(e);
        }
    });
    this.container = div;
}
/**
 * Creates function to apply value
 */
ColorDialog.prototype.presetColors = PRESENT_COLORS;
/**
 * Creates function to apply value
 */
ColorDialog.prototype.defaultColors = __spreadArray(["none"], __read(DEFAULT_COLORS), false);
/**
 * Creates function to apply value
 */
ColorDialog.prototype.createApplyFunction = function () {
    return mxUtils.bind(this, function (color) {
        var graph = this.editorUi.editor.graph;
        graph.getModel().beginUpdate();
        try {
            graph.setCellStyles(this.currentColorKey, color);
            this.editorUi.fireEvent(new mxEventObject("styleChanged", "keys", [this.currentColorKey], "values", [color], "cells", graph.getSelectionCells()));
        }
        finally {
            graph.getModel().endUpdate();
        }
    });
};
/**
 *
 */
ColorDialog.recentColors = [];
/**
 * Adds recent color for later use.
 */
ColorDialog.addRecentColor = function (color, max) {
    if (color != null) {
        mxUtils.remove(color, ColorDialog.recentColors);
        ColorDialog.recentColors.splice(0, 0, color);
        if (ColorDialog.recentColors.length >= max) {
            ColorDialog.recentColors.pop();
        }
    }
};
/**
 * Adds recent color for later use.
 */
ColorDialog.resetRecentColors = function () {
    ColorDialog.recentColors = [];
};
/**
 *
 */
function OutlineWindow(editorUi, x, y, w, h) {
    var graph = editorUi.editor.graph;
    var div = document.createElement("div");
    div.style.position = "absolute";
    div.style.width = "100%";
    div.style.height = "100%";
    div.style.border = "1px solid whiteSmoke";
    div.style.overflow = "hidden";
    this.window = new mxWindow(mxResources.get("outline"), div, x, y, w, h, true, true);
    this.window.minimumSize = new mxRectangle(0, 0, 80, 80);
    this.window.destroyOnClose = false;
    this.window.setMaximizable(false);
    this.window.setResizable(true);
    this.window.setClosable(true);
    this.window.setVisible(true);
    this.window.setLocation = function (x, y) {
        var iw = window.innerWidth || document.body.clientWidth || document.documentElement.clientWidth;
        var ih = window.innerHeight || document.body.clientHeight || document.documentElement.clientHeight;
        x = Math.max(0, Math.min(x, iw - this.table.clientWidth));
        y = Math.max(0, Math.min(y, ih - this.table.clientHeight - 48));
        if (this.getX() != x || this.getY() != y) {
            mxWindow.prototype.setLocation.apply(this, arguments);
        }
    };
    var resizeListener = mxUtils.bind(this, function () {
        var x = this.window.getX();
        var y = this.window.getY();
        this.window.setLocation(x, y);
    });
    mxEvent.addListener(window, "resize", resizeListener);
    var outline = editorUi.createOutline(this.window);
    this.destroy = function () {
        mxEvent.removeListener(window, "resize", resizeListener);
        this.window.destroy();
        outline.destroy();
    };
    this.window.addListener(mxEvent.RESIZE, mxUtils.bind(this, function () {
        outline.update(false);
        outline.outline.sizeDidChange();
    }));
    this.window.addListener(mxEvent.SHOW, mxUtils.bind(this, function () {
        this.window.fit();
        outline.suspended = false;
        outline.outline.refresh();
        outline.update();
    }));
    this.window.addListener(mxEvent.HIDE, mxUtils.bind(this, function () {
        outline.suspended = true;
    }));
    this.window.addListener(mxEvent.NORMALIZE, mxUtils.bind(this, function () {
        outline.suspended = false;
        outline.update();
    }));
    this.window.addListener(mxEvent.MINIMIZE, mxUtils.bind(this, function () {
        outline.suspended = true;
    }));
    var outlineCreateGraph = outline.createGraph;
    outline.createGraph = function () {
        var g = outlineCreateGraph.apply(this, arguments);
        g.gridEnabled = false;
        g.pageScale = graph.pageScale;
        g.pageFormat = graph.pageFormat;
        g.background =
            graph.background == null || graph.background == mxConstants.NONE
                ? graph.defaultPageBackgroundColor
                : graph.background;
        g.pageVisible = graph.pageVisible;
        var current = mxUtils.getCurrentStyle(graph.container);
        div.style.backgroundColor = current.backgroundColor;
        return g;
    };
    function update() {
        outline.outline.pageScale = graph.pageScale;
        outline.outline.pageFormat = graph.pageFormat;
        outline.outline.pageVisible = graph.pageVisible;
        outline.outline.background =
            graph.background == null || graph.background == mxConstants.NONE
                ? graph.defaultPageBackgroundColor
                : graph.background;
        var current = mxUtils.getCurrentStyle(graph.container);
        div.style.backgroundColor = current.backgroundColor;
        if (graph.view.backgroundPageShape != null &&
            outline.outline.view.backgroundPageShape != null) {
            outline.outline.view.backgroundPageShape.fill = graph.view.backgroundPageShape.fill;
        }
        outline.outline.refresh();
    }
    outline.init(div);
    editorUi.editor.addListener("resetGraphView", update);
    editorUi.addListener("pageFormatChanged", update);
    editorUi.addListener("backgroundColorChanged", update);
    editorUi.addListener("backgroundImageChanged", update);
    editorUi.addListener("pageViewChanged", function () {
        update();
        outline.update(true);
    });
    if (outline.outline.dialect == mxConstants.DIALECT_SVG) {
        var zoomInAction_1 = editorUi.actions.get("zoomIn");
        var zoomOutAction_1 = editorUi.actions.get("zoomOut");
        mxEvent.addMouseWheelListener(function (evt, up) {
            var outlineWheel = false;
            var source = mxEvent.getSource(evt);
            while (source != null) {
                if (source == outline.outline.view.canvas.ownerSVGElement) {
                    outlineWheel = true;
                    break;
                }
                source = source.parentNode;
            }
            if (outlineWheel) {
                if (up) {
                    zoomInAction_1.funct();
                }
                else {
                    zoomOutAction_1.funct();
                }
            }
        });
    }
}
/**
 *
 */
function LayersWindow(editorUi, x, y, w, h) {
    var graph = editorUi.editor.graph;
    var div = document.createElement("div");
    div.style.userSelect = "none";
    div.style.background = DIALOG.BACK_DROP_COLOR == "white" ? "whiteSmoke" : DIALOG.BACK_DROP_COLOR;
    div.style.border = "1px solid whiteSmoke";
    div.style.height = "100%";
    div.style.marginBottom = "10px";
    div.style.overflow = "auto";
    var tbarHeight = !EDITOR_UI.COMPAT_UI ? "30px" : "26px";
    var listDiv = document.createElement("div");
    listDiv.style.backgroundColor =
        DIALOG.BACK_DROP_COLOR == "white" ? "#dcdcdc" : DIALOG.BACK_DROP_COLOR;
    listDiv.style.position = "absolute";
    listDiv.style.overflow = "auto";
    listDiv.style.left = "0px";
    listDiv.style.right = "0px";
    listDiv.style.top = "0px";
    listDiv.style.bottom = parseInt(tbarHeight) + 7 + "px";
    div.appendChild(listDiv);
    var dragSource = null;
    var dropIndex = null;
    mxEvent.addListener(div, "dragover", function (evt) {
        evt.dataTransfer.dropEffect = "move";
        dropIndex = 0;
        evt.stopPropagation();
        evt.preventDefault();
    });
    // Workaround for "no element found" error in FF
    mxEvent.addListener(div, "drop", function (evt) {
        evt.stopPropagation();
        evt.preventDefault();
    });
    var layerCount = null;
    var selectionLayer = null;
    var ldiv = document.createElement("div");
    ldiv.className = "geToolbarContainer";
    ldiv.style.position = "absolute";
    ldiv.style.bottom = "0px";
    ldiv.style.left = "0px";
    ldiv.style.right = "0px";
    ldiv.style.height = tbarHeight;
    ldiv.style.overflow = "hidden";
    ldiv.style.padding = !EDITOR_UI.COMPAT_UI ? "1px" : "4px 0px 3px 0px";
    ldiv.style.backgroundColor =
        DIALOG.BACK_DROP_COLOR == "white" ? "whiteSmoke" : DIALOG.BACK_DROP_COLOR;
    ldiv.style.borderWidth = "1px 0px 0px 0px";
    ldiv.style.borderColor = "#c3c3c3";
    ldiv.style.borderStyle = "solid";
    ldiv.style.display = "block";
    ldiv.style.whiteSpace = "nowrap";
    if (mxClient.IS_QUIRKS) {
        ldiv.style.filter = "none";
    }
    var link = document.createElement("a");
    link.className = "geButton";
    if (mxClient.IS_QUIRKS) {
        link.style.filter = "none";
    }
    var removeLink = link.cloneNode();
    removeLink.innerHTML =
        '<div class="geSprite geSprite-delete" style="display:inline-block;"></div>';
    mxEvent.addListener(removeLink, "click", function (evt) {
        if (graph.isEnabled()) {
            graph.model.beginUpdate();
            try {
                var index = graph.model.root.getIndex(selectionLayer);
                graph.removeCells([selectionLayer], false);
                // Creates default layer if no layer exists
                if (graph.model.getChildCount(graph.model.root) == 0) {
                    graph.model.add(graph.model.root, new mxCell());
                    graph.setDefaultParent(null);
                }
                else if (index > 0 && index <= graph.model.getChildCount(graph.model.root)) {
                    graph.setDefaultParent(graph.model.getChildAt(graph.model.root, index - 1));
                }
                else {
                    graph.setDefaultParent(null);
                }
            }
            finally {
                graph.model.endUpdate();
            }
        }
        mxEvent.consume(evt);
    });
    if (!graph.isEnabled()) {
        removeLink.className = "geButton mxDisabled";
    }
    ldiv.appendChild(removeLink);
    var insertLink = link.cloneNode();
    insertLink.setAttribute("title", mxUtils.trim(mxResources.get("moveSelectionTo", [""])));
    insertLink.innerHTML =
        '<div class="geSprite geSprite-insert" style="display:inline-block;"></div>';
    mxEvent.addListener(insertLink, "click", function (evt) {
        if (graph.isEnabled() && !graph.isSelectionEmpty()) {
            editorUi.editor.graph.popupMenuHandler.hideMenu();
            var menu_1 = new mxPopupMenu(mxUtils.bind(this, function (menu, parent) {
                for (var i = layerCount - 1; i >= 0; i--) {
                    mxUtils.bind(this, function (child) {
                        var item = menu.addItem(graph.convertValueToString(child) || mxResources.get("background"), null, mxUtils.bind(this, function () {
                            graph.moveCells(graph.getSelectionCells(), 0, 0, false, child);
                        }), parent);
                        if (graph.getSelectionCount() == 1 &&
                            graph.model.isAncestor(child, graph.getSelectionCell())) {
                            menu.addCheckmark(item, checkmarkImage);
                        }
                    })(graph.model.getChildAt(graph.model.root, i));
                }
            }));
            menu_1.div.className += " geMenubarMenu";
            menu_1.smartSeparators = true;
            menu_1.showDisabled = true;
            menu_1.autoExpand = true;
            // Disables autoexpand and destroys menu when hidden
            menu_1.hideMenu = mxUtils.bind(this, function () {
                mxPopupMenu.prototype.hideMenu.apply(menu_1, arguments);
                menu_1.destroy();
            });
            var offset = mxUtils.getOffset(insertLink);
            menu_1.popup(offset.x, offset.y + insertLink.offsetHeight, null, evt);
            // Allows hiding by clicking on document
            editorUi.setCurrentMenu(menu_1);
        }
    });
    ldiv.appendChild(insertLink);
    var dataLink = link.cloneNode();
    dataLink.innerHTML = '<div class="geSprite geSprite-dots" style="display:inline-block;"></div>';
    dataLink.setAttribute("title", mxResources.get("rename"));
    mxEvent.addListener(dataLink, "click", function (evt) {
        if (graph.isEnabled()) {
            renameLayer(selectionLayer);
        }
        mxEvent.consume(evt);
    });
    if (!graph.isEnabled()) {
        dataLink.className = "geButton mxDisabled";
    }
    ldiv.appendChild(dataLink);
    function renameLayer(layer) {
        if (graph.isEnabled() && layer != null) {
            var label = graph.convertValueToString(layer);
            var dlg = new FilenameDialog(editorUi, label || mxResources.get("background"), mxResources.get("rename"), mxUtils.bind(this, function (newValue) {
                if (newValue != null) {
                    graph.cellLabelChanged(layer, newValue);
                }
            }), mxResources.get("enterName"));
            editorUi.showDialog(dlg.container, 300, 100, true, true);
            dlg.init();
        }
    }
    var duplicateLink = link.cloneNode();
    duplicateLink.innerHTML =
        '<div class="geSprite geSprite-duplicate" style="display:inline-block;"></div>';
    mxEvent.addListener(duplicateLink, "click", function () {
        if (graph.isEnabled()) {
            var newCell = null;
            graph.model.beginUpdate();
            try {
                newCell = graph.cloneCell(selectionLayer);
                graph.cellLabelChanged(newCell, mxResources.get("untitledLayer"));
                newCell.setVisible(true);
                newCell = graph.addCell(newCell, graph.model.root);
                graph.setDefaultParent(newCell);
            }
            finally {
                graph.model.endUpdate();
            }
            if (newCell != null && !graph.isCellLocked(newCell)) {
                graph.selectAll(newCell);
            }
        }
    });
    if (!graph.isEnabled()) {
        duplicateLink.className = "geButton mxDisabled";
    }
    ldiv.appendChild(duplicateLink);
    var addLink = link.cloneNode();
    addLink.innerHTML = '<div class="geSprite geSprite-plus" style="display:inline-block;"></div>';
    addLink.setAttribute("title", mxResources.get("addLayer"));
    mxEvent.addListener(addLink, "click", function (evt) {
        if (graph.isEnabled()) {
            graph.model.beginUpdate();
            try {
                var cell = graph.addCell(new mxCell(mxResources.get("untitledLayer")), graph.model.root);
                graph.setDefaultParent(cell);
            }
            finally {
                graph.model.endUpdate();
            }
        }
        mxEvent.consume(evt);
    });
    if (!graph.isEnabled()) {
        addLink.className = "geButton mxDisabled";
    }
    ldiv.appendChild(addLink);
    div.appendChild(ldiv);
    function refresh() {
        layerCount = graph.model.getChildCount(graph.model.root);
        listDiv.innerHTML = "";
        function addLayer(index, label, child, defaultParent) {
            var ldiv = document.createElement("div");
            ldiv.className = "geToolbarContainer";
            ldiv.style.overflow = "hidden";
            ldiv.style.position = "relative";
            ldiv.style.padding = "4px";
            ldiv.style.height = "22px";
            ldiv.style.display = "block";
            ldiv.style.backgroundColor =
                DIALOG.BACK_DROP_COLOR == "white" ? "whiteSmoke" : DIALOG.BACK_DROP_COLOR;
            ldiv.style.borderWidth = "0px 0px 1px 0px";
            ldiv.style.borderColor = "#c3c3c3";
            ldiv.style.borderStyle = "solid";
            ldiv.style.whiteSpace = "nowrap";
            ldiv.setAttribute("title", label);
            var left = document.createElement("div");
            left.style.display = "inline-block";
            left.style.width = "100%";
            left.style.textOverflow = "ellipsis";
            left.style.overflow = "hidden";
            mxEvent.addListener(ldiv, "dragover", function (evt) {
                evt.dataTransfer.dropEffect = "move";
                dropIndex = index;
                evt.stopPropagation();
                evt.preventDefault();
            });
            mxEvent.addListener(ldiv, "dragstart", function (evt) {
                dragSource = ldiv;
                // Workaround for no DnD on DIV in FF
                if (mxClient.IS_FF) {
                    // LATER: Check what triggers a parse as XML on this in FF after drop
                    evt.dataTransfer.setData("Text", "<layer/>");
                }
            });
            mxEvent.addListener(ldiv, "dragend", function (evt) {
                if (dragSource != null && dropIndex != null) {
                    graph.addCell(child, graph.model.root, dropIndex);
                }
                dragSource = null;
                dropIndex = null;
                evt.stopPropagation();
                evt.preventDefault();
            });
            var btn = document.createElement("img");
            btn.setAttribute("draggable", "false");
            btn.setAttribute("align", "top");
            btn.setAttribute("border", "0");
            btn.style.padding = "4px";
            btn.setAttribute("title", mxResources.get("lockUnlock"));
            var style = graph.getCurrentCellStyle(child);
            if (mxUtils.getValue(style, "locked", "0") == "1") {
                btn.setAttribute("src", lockedImage);
            }
            else {
                btn.setAttribute("src", unlockedImage);
            }
            if (graph.isEnabled()) {
                btn.style.cursor = "pointer";
            }
            mxEvent.addListener(btn, "click", function (evt) {
                if (graph.isEnabled()) {
                    var value = null;
                    graph.getModel().beginUpdate();
                    try {
                        value = mxUtils.getValue(style, "locked", "0") == "1" ? null : "1";
                        graph.setCellStyles("locked", value, [child]);
                    }
                    finally {
                        graph.getModel().endUpdate();
                    }
                    if (value == "1") {
                        graph.removeSelectionCells(graph.getModel().getDescendants(child));
                    }
                    mxEvent.consume(evt);
                }
            });
            left.appendChild(btn);
            var inp = document.createElement("input");
            inp.setAttribute("type", "checkbox");
            inp.setAttribute("title", mxResources.get("hideIt", [child.value || mxResources.get("background")]));
            inp.style.marginLeft = "4px";
            inp.style.marginRight = "6px";
            inp.style.marginTop = "4px";
            left.appendChild(inp);
            if (graph.model.isVisible(child)) {
                inp.setAttribute("checked", "checked");
                inp.defaultChecked = true;
            }
            mxEvent.addListener(inp, "click", function (evt) {
                graph.model.setVisible(child, !graph.model.isVisible(child));
                mxEvent.consume(evt);
            });
            mxUtils.write(left, label);
            ldiv.appendChild(left);
            if (graph.isEnabled()) {
                // Fallback if no drag and drop is available
                if (mxClient.IS_TOUCH ||
                    mxClient.IS_POINTER ||
                    mxClient.IS_VML ||
                    (mxClient.IS_IE && document.documentMode < 10)) {
                    var right = document.createElement("div");
                    right.style.display = "block";
                    right.style.textAlign = "right";
                    right.style.whiteSpace = "nowrap";
                    right.style.position = "absolute";
                    right.style.right = "6px";
                    right.style.top = "6px";
                    // Poor man's change layer order
                    if (index > 0) {
                        var img2 = document.createElement("a");
                        img2.setAttribute("title", mxResources.get("toBack"));
                        img2.className = "geButton";
                        img2.style.cssFloat = "none";
                        img2.innerHTML = "&#9660;";
                        img2.style.width = "14px";
                        img2.style.height = "14px";
                        img2.style.fontSize = "14px";
                        img2.style.margin = "0px";
                        img2.style.marginTop = "-1px";
                        right.appendChild(img2);
                        mxEvent.addListener(img2, "click", function (evt) {
                            if (graph.isEnabled()) {
                                graph.addCell(child, graph.model.root, index - 1);
                            }
                            mxEvent.consume(evt);
                        });
                    }
                    if (index >= 0 && index < layerCount - 1) {
                        var img1 = document.createElement("a");
                        img1.setAttribute("title", mxResources.get("toFront"));
                        img1.className = "geButton";
                        img1.style.cssFloat = "none";
                        img1.innerHTML = "&#9650;";
                        img1.style.width = "14px";
                        img1.style.height = "14px";
                        img1.style.fontSize = "14px";
                        img1.style.margin = "0px";
                        img1.style.marginTop = "-1px";
                        right.appendChild(img1);
                        mxEvent.addListener(img1, "click", function (evt) {
                            if (graph.isEnabled()) {
                                graph.addCell(child, graph.model.root, index + 1);
                            }
                            mxEvent.consume(evt);
                        });
                    }
                    ldiv.appendChild(right);
                }
                if (!mxClient.IS_IE || document.documentMode >= 10) {
                    ldiv.setAttribute("draggable", "true");
                    ldiv.style.cursor = "move";
                }
            }
            mxEvent.addListener(ldiv, "dblclick", function (evt) {
                var nodeName = mxEvent.getSource(evt).nodeName;
                if (nodeName != "INPUT" && nodeName != "IMG") {
                    renameLayer(child);
                    mxEvent.consume(evt);
                }
            });
            if (graph.getDefaultParent() == child) {
                ldiv.style.background = DIALOG.BACK_DROP_COLOR == "white" ? "#e6eff8" : "#505759";
                ldiv.style.fontWeight = graph.isEnabled() ? "bold" : "";
                selectionLayer = child;
            }
            else {
                mxEvent.addListener(ldiv, "click", function () {
                    if (graph.isEnabled()) {
                        graph.setDefaultParent(defaultParent);
                        graph.view.setCurrentRoot(null);
                        refresh();
                    }
                });
            }
            listDiv.appendChild(ldiv);
        }
        // Cannot be moved or deleted
        for (var i = layerCount - 1; i >= 0; i--) {
            mxUtils.bind(this, function (child) {
                addLayer(i, graph.convertValueToString(child) || mxResources.get("background"), child, child);
            })(graph.model.getChildAt(graph.model.root, i));
        }
        var label = graph.convertValueToString(selectionLayer) || mxResources.get("background");
        removeLink.setAttribute("title", mxResources.get("removeIt", [label]));
        duplicateLink.setAttribute("title", mxResources.get("duplicateIt", [label]));
        dataLink.setAttribute("title", mxResources.get("rename"));
        if (graph.isSelectionEmpty()) {
            insertLink.className = "geButton mxDisabled";
        }
    }
    refresh();
    graph.model.addListener(mxEvent.CHANGE, function () {
        refresh();
    });
    graph.selectionModel.addListener(mxEvent.CHANGE, function () {
        if (graph.isSelectionEmpty()) {
            insertLink.className = "geButton mxDisabled";
        }
        else {
            insertLink.className = "geButton";
        }
    });
    this.window = new mxWindow(mxResources.get("layers"), div, x, y, w, h, true, true);
    this.window.minimumSize = new mxRectangle(0, 0, 120, 120);
    this.window.destroyOnClose = false;
    this.window.setMaximizable(false);
    this.window.setResizable(true);
    this.window.setClosable(true);
    this.window.setVisible(true);
    this.init = function () {
        listDiv.scrollTop = listDiv.scrollHeight - listDiv.clientHeight;
    };
    this.window.addListener(mxEvent.SHOW, mxUtils.bind(this, function () {
        this.window.fit();
    }));
    // Make refresh available via instance
    this.refreshLayers = refresh;
    this.window.setLocation = function (x, y) {
        var iw = window.innerWidth || document.body.clientWidth || document.documentElement.clientWidth;
        var ih = window.innerHeight || document.body.clientHeight || document.documentElement.clientHeight;
        x = Math.max(0, Math.min(x, iw - this.table.clientWidth));
        y = Math.max(0, Math.min(y, ih - this.table.clientHeight - 48));
        if (this.getX() != x || this.getY() != y) {
            mxWindow.prototype.setLocation.apply(this, arguments);
        }
    };
    var resizeListener = mxUtils.bind(this, function () {
        var x = this.window.getX();
        var y = this.window.getY();
        this.window.setLocation(x, y);
    });
    mxEvent.addListener(window, "resize", resizeListener);
    this.destroy = function () {
        mxEvent.removeListener(window, "resize", resizeListener);
        this.window.destroy();
    };
}
/**
 * Constructs a new filename dialog.
 */
function FilenameDialog(editorUi, filename, buttonText, fn, label, validateFn, content, helpLink, closeOnBtn, cancelFn, hints, w) {
    closeOnBtn = closeOnBtn != null ? closeOnBtn : true;
    var row, td;
    var table = document.createElement("table");
    var tbody = document.createElement("tbody");
    table.style.marginTop = "8px";
    row = document.createElement("tr");
    td = document.createElement("td");
    td.style.whiteSpace = "nowrap";
    td.style.fontSize = "10pt";
    td.style.width = hints ? "80px" : "120px";
    mxUtils.write(td, (label || mxResources.get("filename")) + ":");
    row.appendChild(td);
    var nameInput = document.createElement("input");
    nameInput.setAttribute("value", filename || "");
    nameInput.style.marginLeft = "4px";
    nameInput.style.width = w != null ? w + "px" : "180px";
    var genericBtn = mxUtils.button(buttonText, function () {
        if (validateFn == null || validateFn(nameInput.value)) {
            if (closeOnBtn) {
                editorUi.hideDialog();
            }
            fn(nameInput.value);
        }
    });
    genericBtn.className = "geBtn gePrimaryBtn";
    this.init = function () {
        if (label == null && content != null) {
            return;
        }
        nameInput.focus();
        if (mxClient.IS_GC || mxClient.IS_FF || document.documentMode >= 5 || mxClient.IS_QUIRKS) {
            nameInput.select();
        }
        else {
            document.execCommand("selectAll", false, null);
        }
        // Installs drag and drop handler for links
        if (Graph.fileSupport) {
            // Setup the dnd listeners
            var dlg = table.parentNode;
            if (dlg != null) {
                var dropElt_1 = null;
                mxEvent.addListener(dlg, "dragleave", function (evt) {
                    if (dropElt_1 != null) {
                        dropElt_1.style.backgroundColor = "";
                        dropElt_1 = null;
                    }
                    evt.stopPropagation();
                    evt.preventDefault();
                });
                mxEvent.addListener(dlg, "dragover", mxUtils.bind(this, function (evt) {
                    // IE 10 does not implement pointer-events so it can't have a drop highlight
                    if (dropElt_1 == null && (!mxClient.IS_IE || document.documentMode > 10)) {
                        dropElt_1 = nameInput;
                        dropElt_1.style.backgroundColor = "#ebf2f9";
                    }
                    evt.stopPropagation();
                    evt.preventDefault();
                }));
                mxEvent.addListener(dlg, "drop", mxUtils.bind(this, function (evt) {
                    if (dropElt_1 != null) {
                        dropElt_1.style.backgroundColor = "";
                        dropElt_1 = null;
                    }
                    if (mxUtils.indexOf(evt.dataTransfer.types, "text/uri-list") >= 0) {
                        nameInput.value = decodeURIComponent(evt.dataTransfer.getData("text/uri-list"));
                        genericBtn.click();
                    }
                    evt.stopPropagation();
                    evt.preventDefault();
                }));
            }
        }
    };
    td = document.createElement("td");
    td.style.whiteSpace = "nowrap";
    td.appendChild(nameInput);
    row.appendChild(td);
    if (label != null || content == null) {
        tbody.appendChild(row);
        if (hints != null) {
            if (editorUi.editor.diagramFileTypes != null) {
                var typeSelect = FilenameDialog.createFileTypes(editorUi, nameInput, editorUi.editor.diagramFileTypes);
                typeSelect.style.marginLeft = "6px";
                typeSelect.style.width = "74px";
                td.appendChild(typeSelect);
                nameInput.style.width = w != null ? w - 40 + "px" : "140px";
            }
            td.appendChild(FilenameDialog.createTypeHint(editorUi, nameInput, hints));
        }
    }
    if (content != null) {
        row = document.createElement("tr");
        td = document.createElement("td");
        td.colSpan = 2;
        td.appendChild(content);
        row.appendChild(td);
        tbody.appendChild(row);
    }
    row = document.createElement("tr");
    td = document.createElement("td");
    td.colSpan = 2;
    td.style.paddingTop = "20px";
    td.style.whiteSpace = "nowrap";
    td.setAttribute("align", "right");
    var cancelBtn = mxUtils.button(mxResources.get("cancel"), function () {
        editorUi.hideDialog();
        if (cancelFn != null) {
            cancelFn();
        }
    });
    cancelBtn.className = "geBtn";
    if (editorUi.editor.cancelFirst) {
        td.appendChild(cancelBtn);
    }
    if (helpLink != null) {
        var helpBtn = mxUtils.button(mxResources.get("help"), function () {
            editorUi.editor.graph.openLink(helpLink);
        });
        helpBtn.className = "geBtn";
        td.appendChild(helpBtn);
    }
    mxEvent.addListener(nameInput, "keypress", function (e) {
        if (e.keyCode == 13) {
            genericBtn.click();
        }
    });
    td.appendChild(genericBtn);
    if (!editorUi.editor.cancelFirst) {
        td.appendChild(cancelBtn);
    }
    row.appendChild(td);
    tbody.appendChild(row);
    table.appendChild(tbody);
    this.container = table;
}
/**
 *
 */
FilenameDialog.filenameHelpLink = null;
/**
 *
 */
FilenameDialog.createTypeHint = function (ui, nameInput, hints) {
    var hint = document.createElement("img");
    hint.style.cssText =
        "vertical-align:top;height:16px;width:16px;margin-left:4px;background-repeat:no-repeat;background-position:center bottom;cursor:pointer;";
    mxUtils.setOpacity(hint, 70);
    var nameChanged = function () {
        hint.setAttribute("src", helpImage);
        hint.setAttribute("title", mxResources.get("help"));
        for (var i = 0; i < hints.length; i++) {
            if (hints[i].ext.length > 0 &&
                nameInput.value.toLowerCase().substring(nameInput.value.length - hints[i].ext.length - 1) ==
                    "." + hints[i].ext) {
                hint.setAttribute("src", mxClient.imageBasePath + "/warning.png");
                hint.setAttribute("title", mxResources.get(hints[i].title));
                break;
            }
        }
    };
    mxEvent.addListener(nameInput, "keyup", nameChanged);
    mxEvent.addListener(nameInput, "change", nameChanged);
    mxEvent.addListener(hint, "click", function (evt) {
        var title = hint.getAttribute("title");
        if (hint.getAttribute("src") == helpImage) {
            ui.editor.graph.openLink(FilenameDialog.filenameHelpLink);
        }
        else if (title != "") {
            ui.showError(null, title, mxResources.get("help"), function () {
                ui.editor.graph.openLink(FilenameDialog.filenameHelpLink);
            }, null, mxResources.get("ok"), null, null, null, 340, 90);
        }
        mxEvent.consume(evt);
    });
    nameChanged();
    return hint;
};
/**
 *
 */
FilenameDialog.createFileTypes = function (editorUi, nameInput, types) {
    var typeSelect = document.createElement("select");
    for (var i = 0; i < types.length; i++) {
        var typeOption = document.createElement("option");
        typeOption.setAttribute("value", i);
        mxUtils.write(typeOption, mxResources.get(types[i].description) + " (." + types[i].extension + ")");
        typeSelect.appendChild(typeOption);
    }
    mxEvent.addListener(typeSelect, "change", function () {
        var ext = types[typeSelect.value].extension;
        var idx = nameInput.value.lastIndexOf(".");
        if (idx > 0) {
            var ext = types[typeSelect.value].extension;
            nameInput.value = nameInput.value.substring(0, idx + 1) + ext;
        }
        else {
            nameInput.value = nameInput.value + "." + ext;
        }
        if ("createEvent" in document) {
            var changeEvent = document.createEvent("HTMLEvents");
            changeEvent.initEvent("change", false, true);
            nameInput.dispatchEvent(changeEvent);
        }
        else {
            nameInput.fireEvent("onchange");
        }
    });
    var nameInputChanged = function () {
        var idx = nameInput.value.lastIndexOf(".");
        var active = 0;
        // Finds current extension
        if (idx > 0) {
            var ext = nameInput.value.toLowerCase().substring(idx + 1);
            for (var i = 0; i < types.length; i++) {
                if (ext == types[i].extension) {
                    active = i;
                    break;
                }
            }
        }
        typeSelect.value = active;
    };
    mxEvent.addListener(nameInput, "change", nameInputChanged);
    mxEvent.addListener(nameInput, "keyup", nameInputChanged);
    nameInputChanged();
    return typeSelect;
};
