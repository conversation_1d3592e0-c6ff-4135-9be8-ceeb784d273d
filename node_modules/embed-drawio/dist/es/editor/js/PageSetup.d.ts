/**
 * Change types
 */
export function PageSetup(ui: any, color: any, image: any, format: any, pageScale: any): void;
export class PageSetup {
    /**
     * Change types
     */
    constructor(ui: any, color: any, image: any, format: any, pageScale: any);
    ui: any;
    color: any;
    previousColor: any;
    image: any;
    previousImage: any;
    format: any;
    previousFormat: any;
    pageScale: any;
    previousPageScale: any;
    ignoreColor: boolean;
    ignoreImage: boolean;
    /**
     * Implementation of the undoable page rename.
     */
    execute(): void;
    foldingEnabled: any;
}
