import arrows from "./arrows";
import basic from "./basic";
import flowchart from "./flowchart";
import bpmn from "./bpmn";
import Credit_Card_128x128 from "./clipart/Credit_Card_128x128";
import Database_128x128 from "./clipart/Database_128x128";
import Doctor1_128x128 from "./clipart/Doctor1_128x128";
import Earth_globe_128x128 from "./clipart/Earth_globe_128x128";
import Email_128x128 from "./clipart/Email_128x128";
import Empty_Folder_128x128 from "./clipart/Empty_Folder_128x128";
import Firewall_02_128x128 from "./clipart/Firewall_02_128x128";
import Full_Folder_128x128 from "./clipart/Full_Folder_128x128";
import Gear_128x128 from "./clipart/Gear_128x128";
import Graph_128x128 from "./clipart/Graph_128x128";
import iMac_128x128 from "./clipart/iMac_128x128";
import iPad_128x128 from "./clipart/iPad_128x128";
import Laptop_128x128 from "./clipart/Laptop_128x128";
import Lock_128x128 from "./clipart/Lock_128x128";
import MacBook_128x128 from "./clipart/MacBook_128x128";
import Monitor_Tower_128x128 from "./clipart/Monitor_Tower_128x128";
import Piggy_Bank_128x128 from "./clipart/Piggy_Bank_128x128";
import Pilot1_128x128 from "./clipart/Pilot1_128x128";
import Printer_128x128 from "./clipart/Printer_128x128";
import Router_Icon_128x128 from "./clipart/Router_Icon_128x128";
import Safe_128x128 from "./clipart/Safe_128x128";
import Security1_128x128 from "./clipart/Security1_128x128";
import Server_Tower_128x128 from "./clipart/Server_Tower_128x128";
import Shopping_Cart_128x128 from "./clipart/Shopping_Cart_128x128";
import Software_128x128 from "./clipart/Software_128x128";
import Soldier1_128x128 from "./clipart/Soldier1_128x128";
import Suit1_128x128 from "./clipart/Suit1_128x128";
import Suit2_128x128 from "./clipart/Suit2_128x128";
import Suit3_128x128 from "./clipart/Suit3_128x128";
import Tech1_128x128 from "./clipart/Tech1_128x128";
import Telesales1_128x128 from "./clipart/Telesales1_128x128";
import Virtual_Machine_128x128 from "./clipart/Virtual_Machine_128x128";
import Virus_128x128 from "./clipart/Virus_128x128";
import Wireless_Router_N_128x128 from "./clipart/Wireless_Router_N_128x128";
import Worker1_128x128 from "./clipart/Worker1_128x128";
import Workstation_128x128 from "./clipart/Workstation_128x128";
var XML = ".xml";
var PNG = ".png";
var dict = {};
dict["arrows" + XML] = arrows;
dict["basic" + XML] = basic;
dict["flowchart" + XML] = flowchart;
dict["bpmn" + XML] = bpmn;
dict["Credit_Card_128x128" + PNG] = Credit_Card_128x128;
dict["Database_128x128" + PNG] = Database_128x128;
dict["Doctor1_128x128" + PNG] = Doctor1_128x128;
dict["Earth_globe_128x128" + PNG] = Earth_globe_128x128;
dict["Email_128x128" + PNG] = Email_128x128;
dict["Empty_Folder_128x128" + PNG] = Empty_Folder_128x128;
dict["Firewall_02_128x128" + PNG] = Firewall_02_128x128;
dict["Full_Folder_128x128" + PNG] = Full_Folder_128x128;
dict["Gear_128x128" + PNG] = Gear_128x128;
dict["Graph_128x128" + PNG] = Graph_128x128;
dict["iMac_128x128" + PNG] = iMac_128x128;
dict["iPad_128x128" + PNG] = iPad_128x128;
dict["Laptop_128x128" + PNG] = Laptop_128x128;
dict["Lock_128x128" + PNG] = Lock_128x128;
dict["MacBook_128x128" + PNG] = MacBook_128x128;
dict["Monitor_Tower_128x128" + PNG] = Monitor_Tower_128x128;
dict["Piggy_Bank_128x128" + PNG] = Piggy_Bank_128x128;
dict["Pilot1_128x128" + PNG] = Pilot1_128x128;
dict["Printer_128x128" + PNG] = Printer_128x128;
dict["Router_Icon_128x128" + PNG] = Router_Icon_128x128;
dict["Safe_128x128" + PNG] = Safe_128x128;
dict["Security1_128x128" + PNG] = Security1_128x128;
dict["Server_Tower_128x128" + PNG] = Server_Tower_128x128;
dict["Shopping_Cart_128x128" + PNG] = Shopping_Cart_128x128;
dict["Software_128x128" + PNG] = Software_128x128;
dict["Soldier1_128x128" + PNG] = Soldier1_128x128;
dict["Suit1_128x128" + PNG] = Suit1_128x128;
dict["Suit2_128x128" + PNG] = Suit2_128x128;
dict["Suit3_128x128" + PNG] = Suit3_128x128;
dict["Tech1_128x128" + PNG] = Tech1_128x128;
dict["Telesales1_128x128" + PNG] = Telesales1_128x128;
dict["Virtual_Machine_128x128" + PNG] = Virtual_Machine_128x128;
dict["Virus_128x128" + PNG] = Virus_128x128;
dict["Wireless_Router_N_128x128" + PNG] = Wireless_Router_N_128x128;
dict["Worker1_128x128" + PNG] = Worker1_128x128;
dict["Workstation_128x128" + PNG] = Workstation_128x128;
export var getStencil = function (name) { return dict[name]; };
