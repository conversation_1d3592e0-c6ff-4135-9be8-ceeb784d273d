/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
/// <reference types="typed-mxgraph" />
declare global {
    interface Window {
        mxBasePath: string;
        mxLoadResources: boolean;
        mxForceIncludes: boolean;
        mxLoadStylesheets: boolean;
        mxResourceExtension: string;
    }
}
export declare const mxGraph: typeof import("mxgraph").mxGraph, mxCodec: typeof import("mxgraph").mxCodec, mxConstants: typeof import("mxgraph").mxConstants, mxSvgCanvas2D: typeof import("mxgraph").mxSvgCanvas2D, mxImageExport: typeof import("mxgraph").mxImageExport, mxEventSource: typeof import("mxgraph").mxEventSource, mxResources: typeof import("mxgraph").mxResources, mxEventObject: typeof import("mxgraph").mxEventObject, mxEvent: typeof import("mxgraph").mxEvent, mxUtils: typeof import("mxgraph").mxUtils, mxClient: typeof import("mxgraph").mxClient, mxRectangle: typeof import("mxgraph").mxRectangle, mxDivResizer: typeof import("mxgraph").mxDivResizer, mxPopupMenu: typeof import("mxgraph").mxPopupMenu, mxPoint: typeof import("mxgraph").mxPoint, mxGraphView: typeof import("mxgraph").mxGraphView, mxMouseEvent: typeof import("mxgraph").mxMouseEvent, mxPolyline: typeof import("mxgraph").mxPolyline, mxGraphHandler: typeof import("mxgraph").mxGraphHandler, mxConnectionHandler: typeof import("mxgraph").mxConnectionHandler, mxCellMarker: typeof import("mxgraph").mxCellMarker, mxRectangleShape: typeof import("mxgraph").mxRectangleShape, mxPopupMenuHandler: typeof import("mxgraph").mxPopupMenuHandler, mxUndoManager: typeof import("mxgraph").mxUndoManager, mxText: typeof import("mxgraph").mxText, mxRubberband: typeof import("mxgraph").mxRubberband, mxGraphModel: typeof import("mxgraph").mxGraphModel, mxShape: typeof import("mxgraph").mxShape, mxEdgeStyle: typeof import("mxgraph").mxEdgeStyle, mxSelectionCellsHandler: typeof import("mxgraph").mxSelectionCellsHandler, mxClipboard: typeof import("mxgraph").mxClipboard, mxEdgeHandler: typeof import("mxgraph").mxEdgeHandler, mxCellRenderer: typeof import("mxgraph").mxCellRenderer, mxDragSource: typeof import("mxgraph").mxDragSource, mxGuide: typeof import("mxgraph").mxGuide, mxImage: typeof import("mxgraph").mxImage, mxGraphLayout: typeof import("mxgraph").mxGraphLayout, mxObjectCodec: typeof import("mxgraph").mxObjectCodec, mxCellHighlight: typeof import("mxgraph").mxCellHighlight, mxLayoutManager: typeof import("mxgraph").mxLayoutManager, mxCompactTreeLayout: typeof import("mxgraph").mxCompactTreeLayout, mxHierarchicalLayout: typeof import("mxgraph").mxHierarchicalLayout, mxCircleLayout: typeof import("mxgraph").mxCircleLayout, mxFastOrganicLayout: typeof import("mxgraph").mxFastOrganicLayout, mxStencilRegistry: typeof import("mxgraph").mxStencilRegistry, mxStencil: typeof import("mxgraph").mxStencil, mxConstraintHandler: typeof import("mxgraph").mxConstraintHandler, mxEllipse: typeof import("mxgraph").mxEllipse, mxCellState: typeof import("mxgraph").mxCellState, mxObjectIdentity: typeof import("mxgraph").mxObjectIdentity, mxDictionary: typeof import("mxgraph").mxDictionary, mxConnectionConstraint: typeof import("mxgraph").mxConnectionConstraint, mxCellEditor: typeof import("mxgraph").mxCellEditor, mxVertexHandler: typeof import("mxgraph").mxVertexHandler, mxOutline: typeof import("mxgraph").mxOutline, mxPanningHandler: typeof import("mxgraph").mxPanningHandler, mxElbowEdgeHandler: typeof import("mxgraph").mxElbowEdgeHandler, mxImageShape: typeof import("mxgraph").mxImageShape, mxStackLayout: typeof import("mxgraph").mxStackLayout, mxConnector: typeof import("mxgraph").mxConnector, mxStyleRegistry: typeof import("mxgraph").mxStyleRegistry, mxKeyHandler: typeof import("mxgraph").mxKeyHandler, mxCell: typeof import("mxgraph").mxCell, mxGeometry: typeof import("mxgraph").mxGeometry, mxXmlRequest: typeof import("mxgraph").mxXmlRequest, mxXmlCanvas2D: typeof import("mxgraph").mxXmlCanvas2D, mxForm: typeof import("mxgraph").mxForm, mxWindow: typeof import("mxgraph").mxWindow, mxMorphing: typeof import("mxgraph").mxMorphing, mxRadialTreeLayout: typeof import("mxgraph").mxRadialTreeLayout, mxActor: typeof import("mxgraph").mxActor, mxMarker: typeof import("mxgraph").mxMarker, mxCylinder: typeof import("mxgraph").mxCylinder, mxRhombus: typeof import("mxgraph").mxRhombus, mxPerimeter: typeof import("mxgraph").mxPerimeter, mxArrowConnector: typeof import("mxgraph").mxArrowConnector, mxDoubleEllipse: typeof import("mxgraph").mxDoubleEllipse, mxHexagon: typeof import("mxgraph").mxHexagon, mxSwimlane: typeof import("mxgraph").mxSwimlane, mxLabel: typeof import("mxgraph").mxLabel, mxHandle: typeof import("mxgraph").mxHandle, mxLine: typeof import("mxgraph").mxLine, mxTriangle: typeof import("mxgraph").mxTriangle, mxCloud: typeof import("mxgraph").mxCloud, mxArrow: typeof import("mxgraph").mxArrow, mxCodecRegistry: typeof import("mxgraph").mxCodecRegistry;
