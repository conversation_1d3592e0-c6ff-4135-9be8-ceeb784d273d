import factory from "mxgraph";
window.mxBasePath = "static";
window.mxLoadResources = false;
window.mxForceIncludes = false;
window.mxLoadStylesheets = false;
window.mxResourceExtension = ".txt";
// https://github.com/jgraph/mxgraph/issues/479
var mx = factory();
// 需要用到的再引用 实际上还是把所有的包都打进来了
export var mxGraph = mx.mxGraph, mxCodec = mx.mxCodec, mxConstants = mx.mxConstants, mxSvgCanvas2D = mx.mxSvgCanvas2D, mxImageExport = mx.mxImageExport, mxEventSource = mx.mxEventSource, mxResources = mx.mxResources, mxEventObject = mx.mxEventObject, mxEvent = mx.mxEvent, mxUtils = mx.mxUtils, mxClient = mx.mxClient, mxRectangle = mx.mxRectangle, mxDivResizer = mx.mxDivResizer, mxPopupMenu = mx.mxPopupMenu, mxPoint = mx.mxPoint, mxGraphView = mx.mxGraphView, mxMouseEvent = mx.mxMouseEvent, mxPolyline = mx.mxPolyline, mxGraphHandler = mx.mxGraphHandler, mxConnectionHandler = mx.mxConnectionHandler, mxCellMarker = mx.mxCellMarker, mxRectangleShape = mx.mxRectangleShape, mxPopupMenuHandler = mx.mxPopupMenuHandler, mxUndoManager = mx.mxUndoManager, mxText = mx.mxText, mxRubberband = mx.mxRubberband, mxGraphModel = mx.mxGraphModel, mxShape = mx.mxShape, mxEdgeStyle = mx.mxEdgeStyle, mxSelectionCellsHandler = mx.mxSelectionCellsHandler, mxClipboard = mx.mxClipboard, mxEdgeHandler = mx.mxEdgeHandler, mxCellRenderer = mx.mxCellRenderer, mxDragSource = mx.mxDragSource, mxGuide = mx.mxGuide, mxImage = mx.mxImage, mxGraphLayout = mx.mxGraphLayout, mxObjectCodec = mx.mxObjectCodec, mxCellHighlight = mx.mxCellHighlight, mxLayoutManager = mx.mxLayoutManager, mxCompactTreeLayout = mx.mxCompactTreeLayout, mxHierarchicalLayout = mx.mxHierarchicalLayout, mxCircleLayout = mx.mxCircleLayout, mxFastOrganicLayout = mx.mxFastOrganicLayout, mxStencilRegistry = mx.mxStencilRegistry, mxStencil = mx.mxStencil, mxConstraintHandler = mx.mxConstraintHandler, mxEllipse = mx.mxEllipse, mxCellState = mx.mxCellState, mxObjectIdentity = mx.mxObjectIdentity, mxDictionary = mx.mxDictionary, mxConnectionConstraint = mx.mxConnectionConstraint, mxCellEditor = mx.mxCellEditor, mxVertexHandler = mx.mxVertexHandler, mxOutline = mx.mxOutline, mxPanningHandler = mx.mxPanningHandler, mxElbowEdgeHandler = mx.mxElbowEdgeHandler, mxImageShape = mx.mxImageShape, mxStackLayout = mx.mxStackLayout, mxConnector = mx.mxConnector, mxStyleRegistry = mx.mxStyleRegistry, mxKeyHandler = mx.mxKeyHandler, mxCell = mx.mxCell, mxGeometry = mx.mxGeometry, mxXmlRequest = mx.mxXmlRequest, mxXmlCanvas2D = mx.mxXmlCanvas2D, mxForm = mx.mxForm, mxWindow = mx.mxWindow, mxMorphing = mx.mxMorphing, mxRadialTreeLayout = mx.mxRadialTreeLayout, mxActor = mx.mxActor, mxMarker = mx.mxMarker, mxCylinder = mx.mxCylinder, mxRhombus = mx.mxRhombus, mxPerimeter = mx.mxPerimeter, mxArrowConnector = mx.mxArrowConnector, mxDoubleEllipse = mx.mxDoubleEllipse, mxHexagon = mx.mxHexagon, mxSwimlane = mx.mxSwimlane, mxLabel = mx.mxLabel, mxHandle = mx.mxHandle, mxLine = mx.mxLine, mxTriangle = mx.mxTriangle, mxCloud = mx.mxCloud, mxArrow = mx.mxArrow, mxCodecRegistry = mx.mxCodecRegistry;
// https://github.com/maxGraph/maxGraph/issues/102
// https://github.com/jgraph/mxgraph/blob/master/javascript/src/js/io/mxCodec.js#L423
mxCodec.prototype.decode = function (node, into) {
    this.updateElements();
    var obj = null;
    if (node && node.nodeType == mxConstants.NODETYPE_ELEMENT) {
        var ctor = null;
        try {
            // @ts-expect-error 需要处理的 XML Node 可能不在 Window 上
            ctor = mx[node.nodeName] || window[node.nodeName];
        }
        catch (error) {
            console.log("NODE ".concat(node.nodeName, " IS NOT FOUND"), error);
        }
        var dec = mx.mxCodecRegistry.getCodec(ctor);
        if (dec) {
            obj = dec.decode(this, node, into);
        }
        else {
            obj = node.cloneNode(true);
            obj && obj.removeAttribute("as");
        }
    }
    return obj;
};
// https://github.com/jgraph/mxgraph/issues/58
mxUtils.getScrollOrigin = function (node, includeAncestors, includeDocument) {
    includeAncestors = includeAncestors != null ? includeAncestors : false;
    includeDocument = includeDocument != null ? includeDocument : false;
    var doc = node != null ? node.ownerDocument : document;
    var b = doc.body;
    var d = doc.documentElement;
    var result = new mxPoint();
    var fixed = false;
    while (node != null && node != b && node != d) {
        if (!isNaN(node.scrollLeft) && !isNaN(node.scrollTop)) {
            result.x += node.scrollLeft;
            result.y += node.scrollTop;
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        var style = mxUtils.getCurrentStyle(node);
        if (style != null) {
            fixed = fixed || style.position == "fixed";
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        node = includeAncestors ? node.parentNode : null;
    }
    if (!fixed && includeDocument) {
        var origin_1 = mxUtils.getDocumentScrollOrigin(doc);
        result.x += origin_1.x;
        result.y += origin_1.y;
    }
    return result;
};
// https://github.com/WindrunnerMax/FlowChartEditor/issues/4
mxSvgCanvas2D.prototype.createClip = function (x2, y2, w3, h3) {
    x2 = Math.round(x2);
    y2 = Math.round(y2);
    w3 = Math.round(w3);
    h3 = Math.round(h3);
    var id = "mx-clip-".concat(x2, "-").concat(y2, "-").concat(w3, "-").concat(h3);
    var counter = 0;
    var tmp = "".concat(id, "-").concat(counter);
    while (document.getElementById(tmp) != null) {
        tmp = "".concat(id, "-").concat(++counter);
    }
    var clip = this.createElement("clipPath");
    clip.setAttribute("id", tmp);
    var rect = this.createElement("rect");
    rect.setAttribute("x", x2.toString());
    rect.setAttribute("y", y2.toString());
    rect.setAttribute("width", w3.toString());
    rect.setAttribute("height", h3.toString());
    clip.appendChild(rect);
    return clip;
};
// https://github.com/WindrunnerMax/FlowChartEditor/issues/4
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
mxPopupMenu.prototype.createSubmenu = function (parent) {
    parent.table = document.createElement("table");
    parent.table.className = "mxPopupMenu";
    parent.tbody = document.createElement("tbody");
    parent.table.appendChild(parent.tbody);
    parent.div = document.createElement("div");
    parent.div.className = "mxPopupMenu";
    parent.div.style.position = "absolute";
    parent.div.style.display = "inline";
    parent.div.style.zIndex = this.zIndex.toString();
    parent.div.appendChild(parent.table);
    var img = document.createElement("img");
    img.setAttribute("src", this.submenuImage);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    var td = parent.firstChild.nextSibling.nextSibling;
    td.appendChild(img);
};
