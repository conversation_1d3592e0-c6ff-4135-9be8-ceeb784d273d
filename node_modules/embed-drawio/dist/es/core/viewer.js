import "../editor/js/Shapes";
import { stringToXml } from "../utils/xml";
import { DEFAULT_STYLE_XML } from "../styles/default";
import { Graph } from "../editor/js/Graph";
import { mxCodec, mxEvent } from "./mxgraph";
var themes = {};
themes[Graph.prototype.defaultThemeName] = stringToXml(DEFAULT_STYLE_XML).documentElement;
var DiagramViewer = /** @class */ (function () {
    function DiagramViewer(xml) {
        var _this = this;
        this.xml = xml;
        this.renderSVG = function (background, scale, border) {
            if (scale === void 0) { scale = 1; }
            if (border === void 0) { border = 1; }
            if (!_this.graph)
                return null;
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var model = _this.graph.getModel();
            _this.xml && new mxCodec(_this.xml).decode(_this.xml.documentElement, model);
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var svg = _this.graph.getSvg(background, scale, border);
            return svg;
        };
        this.destroy = function () {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            _this.graph && _this.graph.destroy();
            _this.container && mxEvent.removeAllListeners(_this.container);
            _this.graph = null;
            _this.container = null;
        };
        var container = document.createElement("div");
        var graph = new Graph(container, null, null, null, themes, true);
        this.container = container;
        this.graph = graph;
    }
    DiagramViewer.xmlToSvg = function (xml, background, scale, border) {
        if (scale === void 0) { scale = 1; }
        if (border === void 0) { border = 1; }
        if (!xml)
            return null;
        var viewer = new DiagramViewer(xml);
        var svg = viewer.renderSVG(background, scale, border);
        viewer.destroy();
        return svg;
    };
    return DiagramViewer;
}());
export { DiagramViewer };
