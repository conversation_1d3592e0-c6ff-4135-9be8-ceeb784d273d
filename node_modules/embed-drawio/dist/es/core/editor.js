import { stringToXml } from "../utils/xml";
import { DEFAULT_STYLE_XML } from "../styles/default";
import { Editor, EditorUi, Graph } from "../editor";
import { mxEvent, mxResources } from "./mxgraph";
import { getLanguage } from "../editor/i18n";
var themes = {};
themes[Graph.prototype.defaultThemeName] = stringToXml(DEFAULT_STYLE_XML).documentElement;
var DiagramEditor = /** @class */ (function () {
    function DiagramEditor(container, onExit) {
        var _this = this;
        this.container = container;
        this.onExit = onExit;
        this.start = function (lang, init, onXMLChange) {
            _this.container.appendChild(_this.diagramContainer);
            _this.container.style.overflow = "hidden";
            mxResources.parse(lang);
            _this.editor = new Editor(false, themes);
            _this.editorUi = new EditorUi(_this.editor, _this.diagramContainer, null, _this.onExit);
            if (init) {
                _this.editorUi.editor.setGraphXml(init.documentElement);
            }
            _this.editor.graph.getModel().addListener(mxEvent.CHANGE, function (_, event) {
                if (onXMLChange) {
                    var changes = [];
                    if (event && event.properties && event.properties.changes) {
                        changes = event.properties.changes;
                    }
                    onXMLChange(_this.editorUi && _this.editorUi.editor.getGraphXml(), changes);
                }
            });
        };
        this.getXML = function () {
            return _this.editorUi && _this.editorUi.editor.getGraphXml();
        };
        this.exit = function () {
            _this.container.style.overflow = "";
            mxEvent.removeAllListeners(window);
            mxEvent.removeAllListeners(document);
            _this.editor && _this.editor.destroy();
            _this.editorUi && _this.editorUi.destroy();
            _this.container.removeChild(_this.diagramContainer);
        };
        this.editor = null;
        this.editorUi = null;
        this.diagramContainer = document.createElement("div");
        this.diagramContainer.className = "diagram-container geEditor";
    }
    DiagramEditor.getLang = function (lang) {
        return getLanguage(lang);
    };
    return DiagramEditor;
}());
export { DiagramEditor };
