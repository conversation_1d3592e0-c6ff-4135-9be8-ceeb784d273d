import "../editor/js/Shapes";
export declare class DiagramViewer {
    private xml;
    private graph;
    private container;
    constructor(xml: XMLDocument | null);
    renderSVG: (background: string | null, scale?: number, border?: number) => SVGElement | null;
    destroy: () => void;
    static xmlToSvg: (xml: XMLDocument | null, background: string | null, scale?: number, border?: number) => SVGElement | null;
}
