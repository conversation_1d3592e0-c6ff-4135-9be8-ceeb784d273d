{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/lib/drawio.ts"], "sourcesContent": ["// draw.io 相关的工具函数\n\nexport interface DiagramConfig {\n  width?: number;\n  height?: number;\n  toolbar?: boolean;\n  menubar?: boolean;\n  edit?: boolean;\n  zoom?: number;\n}\n\nexport const defaultConfig: DiagramConfig = {\n  width: 800,\n  height: 600,\n  toolbar: false,\n  menubar: false,\n  edit: false,\n  zoom: 1,\n};\n\n/**\n * 验证XML是否为有效的draw.io格式\n */\nexport function validateDrawioXML(xml: string): boolean {\n  try {\n    // 基本检查：是否包含XML内容\n    if (!xml || typeof xml !== 'string') {\n      console.log('XML validation failed: empty or invalid input');\n      return false;\n    }\n\n    // 检查是否包含基本的draw.io标签\n    if (!xml.includes('<mxfile') || !xml.includes('</mxfile>')) {\n      console.log('XML validation failed: missing mxfile tags');\n      return false;\n    }\n\n    // 尝试解析XML\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(xml, 'text/xml');\n\n    // 检查是否有解析错误\n    const parseError = doc.querySelector('parsererror');\n    if (parseError) {\n      console.log('XML validation failed: parse error', parseError.textContent);\n      return false;\n    }\n\n    // 检查是否包含必要的draw.io元素\n    const mxfile = doc.querySelector('mxfile');\n    if (!mxfile) {\n      console.log('XML validation failed: no mxfile element found');\n      return false;\n    }\n\n    console.log('XML validation passed');\n    return true;\n  } catch (error) {\n    console.error('XML验证失败:', error);\n    return false;\n  }\n}\n\n/**\n * 清理和格式化XML内容\n */\nexport function cleanXML(xml: string): string {\n  let cleaned = xml;\n\n  // 移除<think>标签及其内容\n  cleaned = cleaned.replace(/<think>[\\s\\S]*?<\\/think>/gi, '');\n\n  // 移除markdown代码块标记\n  cleaned = cleaned.replace(/```xml\\s*/g, '').replace(/```\\s*/g, '');\n\n  // 查找并提取XML内容（从<mxfile>开始到</mxfile>结束）\n  const xmlMatch = cleaned.match(/<mxfile[\\s\\S]*?<\\/mxfile>/i);\n\n  if (xmlMatch) {\n    cleaned = xmlMatch[0];\n  } else {\n    // 如果没有找到完整的mxfile标签，尝试查找其他可能的XML结构\n    const altMatch = cleaned.match(/<\\?xml[\\s\\S]*?<\\/[^>]+>/i) ||\n                    cleaned.match(/<[^>]+[\\s\\S]*?<\\/[^>]+>/i);\n    if (altMatch) {\n      cleaned = altMatch[0];\n    }\n  }\n\n  // 移除多余的空白字符\n  cleaned = cleaned.trim();\n\n  return cleaned;\n}\n\n/**\n * 生成draw.io查看器的URL参数\n */\nexport function generateViewerParams(config: DiagramConfig = {}): string {\n  const finalConfig = { ...defaultConfig, ...config };\n\n  const params = new URLSearchParams({\n    embed: '1',\n    ui: 'min',\n    spin: '1',\n    modified: 'unsavedChanges',\n    proto: 'json',\n  });\n\n  if (!finalConfig.toolbar) {\n    params.append('toolbar', '0');\n  }\n\n  if (!finalConfig.menubar) {\n    params.append('menubar', '0');\n  }\n\n  if (!finalConfig.edit) {\n    params.append('edit', '_blank');\n  }\n\n  return params.toString();\n}\n\n/**\n * 创建一个简单的示例XML用于测试\n */\nexport function createSampleXML(): string {\n  return `<mxfile host=\"app.diagrams.net\" modified=\"2024-01-01T00:00:00.000Z\" agent=\"5.0\" version=\"24.7.17\" etag=\"sample\">\n  <diagram name=\"Page-1\" id=\"sample-id\">\n    <mxGraphModel dx=\"1422\" dy=\"794\" grid=\"1\" gridSize=\"10\" guides=\"1\" tooltips=\"1\" connect=\"1\" arrows=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\" pageWidth=\"827\" pageHeight=\"1169\" math=\"0\" shadow=\"0\">\n      <root>\n        <mxCell id=\"0\"/>\n        <mxCell id=\"1\" parent=\"0\"/>\n        <mxCell id=\"2\" value=\"开始\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"40\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"3\" value=\"处理\" style=\"rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"140\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"4\" value=\"结束\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"240\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"5\" value=\"\" style=\"endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;\" edge=\"1\" parent=\"1\" source=\"2\" target=\"3\">\n          <mxGeometry width=\"50\" height=\"50\" relative=\"1\" as=\"geometry\">\n            <mxPoint x=\"384\" y=\"180\" as=\"sourcePoint\"/>\n            <mxPoint x=\"434\" y=\"130\" as=\"targetPoint\"/>\n          </mxGeometry>\n        </mxCell>\n        <mxCell id=\"6\" value=\"\" style=\"endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;\" edge=\"1\" parent=\"1\" source=\"3\" target=\"4\">\n          <mxGeometry width=\"50\" height=\"50\" relative=\"1\" as=\"geometry\">\n            <mxPoint x=\"384\" y=\"280\" as=\"sourcePoint\"/>\n            <mxPoint x=\"434\" y=\"230\" as=\"targetPoint\"/>\n          </mxGeometry>\n        </mxCell>\n      </root>\n    </mxGraphModel>\n  </diagram>\n</mxfile>`;\n}\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;AAWX,MAAM,gBAA+B;IAC1C,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,MAAM;IACN,MAAM;AACR;AAKO,SAAS,kBAAkB,GAAW;IAC3C,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;YACnC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,CAAC,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,QAAQ,CAAC,cAAc;YAC1D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,UAAU;QACV,MAAM,SAAS,IAAI;QACnB,MAAM,MAAM,OAAO,eAAe,CAAC,KAAK;QAExC,YAAY;QACZ,MAAM,aAAa,IAAI,aAAa,CAAC;QACrC,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,sCAAsC,WAAW,WAAW;YACxE,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,SAAS,IAAI,aAAa,CAAC;QACjC,IAAI,CAAC,QAAQ;YACX,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;IACT;AACF;AAKO,SAAS,SAAS,GAAW;IAClC,IAAI,UAAU;IAEd,kBAAkB;IAClB,UAAU,QAAQ,OAAO,CAAC,8BAA8B;IAExD,kBAAkB;IAClB,UAAU,QAAQ,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW;IAE/D,sCAAsC;IACtC,MAAM,WAAW,QAAQ,KAAK,CAAC;IAE/B,IAAI,UAAU;QACZ,UAAU,QAAQ,CAAC,EAAE;IACvB,OAAO;QACL,mCAAmC;QACnC,MAAM,WAAW,QAAQ,KAAK,CAAC,+BACf,QAAQ,KAAK,CAAC;QAC9B,IAAI,UAAU;YACZ,UAAU,QAAQ,CAAC,EAAE;QACvB;IACF;IAEA,YAAY;IACZ,UAAU,QAAQ,IAAI;IAEtB,OAAO;AACT;AAKO,SAAS,qBAAqB,SAAwB,CAAC,CAAC;IAC7D,MAAM,cAAc;QAAE,GAAG,aAAa;QAAE,GAAG,MAAM;IAAC;IAElD,MAAM,SAAS,IAAI,gBAAgB;QACjC,OAAO;QACP,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,OAAO,MAAM,CAAC,WAAW;IAC3B;IAEA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,OAAO,MAAM,CAAC,WAAW;IAC3B;IAEA,IAAI,CAAC,YAAY,IAAI,EAAE;QACrB,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA,OAAO,OAAO,QAAQ;AACxB;AAKO,SAAS;IACd,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8BD,CAAC;AACV", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/components/DiagramViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { DiagramConfig, validateDrawioXML } from '@/lib/drawio';\n\ninterface DiagramViewerProps {\n  xml: string;\n  config?: DiagramConfig;\n  className?: string;\n}\n\nexport default function DiagramViewer({ xml, config, className = '' }: DiagramViewerProps) {\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!xml) {\n      setError('没有提供XML数据');\n      setIsLoading(false);\n      return;\n    }\n\n    // 验证XML格式\n    if (!validateDrawioXML(xml)) {\n      setError('XML格式验证失败，请重新生成');\n      setIsLoading(false);\n      return;\n    }\n\n    const iframe = iframeRef.current;\n    if (!iframe) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    // 使用embed模式的正确URL\n    const embedUrl = 'https://embed.diagrams.net/?embed=1&ui=min&spin=1&modified=unsavedChanges&proto=json&noSaveBtn=1&noExitBtn=1';\n\n    iframe.src = embedUrl;\n\n    // 监听来自iframe的消息\n    const handleMessage = (event: MessageEvent) => {\n      if (event.origin !== 'https://embed.diagrams.net') return;\n\n      try {\n        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;\n\n        if (data.event === 'init') {\n          // 发送XML数据到iframe\n          const message = {\n            action: 'load',\n            xml: xml,\n            title: 'Generated Diagram'\n          };\n          iframe.contentWindow?.postMessage(JSON.stringify(message), '*');\n          setIsLoading(false);\n        } else if (data.event === 'load') {\n          // 图表加载完成\n          console.log('Diagram loaded successfully');\n        } else if (data.event === 'save') {\n          // 处理保存事件（如果需要）\n          console.log('Diagram saved');\n        }\n      } catch (err) {\n        console.error('处理iframe消息失败:', err);\n        setError('加载图表时出错');\n        setIsLoading(false);\n      }\n    };\n\n    // 监听iframe加载错误\n    const handleError = () => {\n      setError('无法连接到draw.io服务');\n      setIsLoading(false);\n    };\n\n    window.addEventListener('message', handleMessage);\n    iframe.addEventListener('error', handleError);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleMessage);\n      iframe.removeEventListener('error', handleError);\n    };\n  }, [xml, config]);\n\n  if (error) {\n    return (\n      <div className={`flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-8 ${className}`}>\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-lg font-medium mb-2\">加载失败</div>\n          <div className=\"text-red-500 text-sm\">{error}</div>\n          <button\n            onClick={() => window.open(`https://app.diagrams.net/`, '_blank')}\n            className=\"mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\"\n          >\n            在draw.io中打开\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`relative bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>\n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-gray-50 z-10\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n            <span className=\"text-gray-600\">加载中...</span>\n          </div>\n        </div>\n      )}\n      <iframe\n        ref={iframeRef}\n        className=\"w-full h-full border-0\"\n        style={{ minHeight: '500px' }}\n        title=\"Draw.io Diagram Viewer\"\n        sandbox=\"allow-scripts allow-same-origin allow-forms allow-popups\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAWe,SAAS,cAAc,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,EAAsB;;IACvF,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,KAAK;gBACR,SAAS;gBACT,aAAa;gBACb;YACF;YAEA,UAAU;YACV,IAAI,CAAC,CAAA,GAAA,gHAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;gBAC3B,SAAS;gBACT,aAAa;gBACb;YACF;YAEA,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,aAAa;YACb,SAAS;YAET,kBAAkB;YAClB,MAAM,WAAW;YAEjB,OAAO,GAAG,GAAG;YAEb,gBAAgB;YAChB,MAAM;yDAAgB,CAAC;oBACrB,IAAI,MAAM,MAAM,KAAK,8BAA8B;oBAEnD,IAAI;wBACF,MAAM,OAAO,OAAO,MAAM,IAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI;wBAEjF,IAAI,KAAK,KAAK,KAAK,QAAQ;4BACzB,iBAAiB;4BACjB,MAAM,UAAU;gCACd,QAAQ;gCACR,KAAK;gCACL,OAAO;4BACT;4BACA,OAAO,aAAa,EAAE,YAAY,KAAK,SAAS,CAAC,UAAU;4BAC3D,aAAa;wBACf,OAAO,IAAI,KAAK,KAAK,KAAK,QAAQ;4BAChC,SAAS;4BACT,QAAQ,GAAG,CAAC;wBACd,OAAO,IAAI,KAAK,KAAK,KAAK,QAAQ;4BAChC,eAAe;4BACf,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,SAAS;wBACT,aAAa;oBACf;gBACF;;YAEA,eAAe;YACf,MAAM;uDAAc;oBAClB,SAAS;oBACT,aAAa;gBACf;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC,OAAO,gBAAgB,CAAC,SAAS;YAEjC,OAAO;YACP;2CAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,SAAS;gBACtC;;QACF;kCAAG;QAAC;QAAK;KAAO;IAEhB,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,CAAC,gFAAgF,EAAE,WAAW;sBAC5G,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;kCACvC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;wBACxD,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,oEAAoE,EAAE,WAAW;;YAC/F,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAQ;gBAC5B,OAAM;gBACN,SAAQ;;;;;;;;;;;;AAIhB;GAhHwB;KAAA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/components/DiagramGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './DiagramViewer';\nimport { cleanXML, createSampleXML } from '@/lib/drawio';\n\nexport default function DiagramGenerator() {\n  const [description, setDescription] = useState('');\n  const [generatedXML, setGeneratedXML] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleGenerate = async () => {\n    if (!description.trim()) {\n      setError('请输入流程图描述');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/generate-diagram', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ description }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || '生成失败');\n      }\n\n      if (data.success && data.xml) {\n        console.log('Raw XML from API:', data.xml);\n        const cleanedXML = cleanXML(data.xml);\n        console.log('Cleaned XML:', cleanedXML);\n        setGeneratedXML(cleanedXML);\n      } else {\n        throw new Error('生成的XML无效');\n      }\n    } catch (err) {\n      console.error('生成流程图失败:', err);\n      setError(err instanceof Error ? err.message : '生成失败');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLoadSample = () => {\n    const sampleXML = createSampleXML();\n    setGeneratedXML(sampleXML);\n    setDescription('示例流程图：开始 -> 处理 -> 结束');\n  };\n\n  const handleClearAll = () => {\n    setDescription('');\n    setGeneratedXML('');\n    setError(null);\n  };\n\n  const handleDownloadXML = () => {\n    if (!generatedXML) return;\n\n    const blob = new Blob([generatedXML], { type: 'application/xml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'diagram.drawio';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleOpenInDrawio = () => {\n    if (!generatedXML) return;\n\n    const encodedXML = encodeURIComponent(generatedXML);\n    const drawioUrl = `https://app.diagrams.net/?lightbox=1#R${encodedXML}`;\n    window.open(drawioUrl, '_blank');\n  };\n\n  const handleTestAPI = async () => {\n    try {\n      const response = await fetch('/api/generate-diagram', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ description: '简单的测试流程：开始->处理->结束' }),\n      });\n\n      const data = await response.json();\n      console.log('API Test Response:', data);\n\n      if (data.success && data.xml) {\n        console.log('Raw XML length:', data.xml.length);\n        console.log('Raw XML preview:', data.xml.substring(0, 300));\n\n        // 直接使用原始XML，不经过cleanXML处理\n        setGeneratedXML(data.xml);\n        setDescription('API测试流程');\n      }\n    } catch (err) {\n      console.error('API测试失败:', err);\n    }\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto p-6 space-y-6\">\n      {/* 标题 */}\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          AI 流程图生成器\n        </h1>\n        <p className=\"text-gray-600\">\n          描述你的流程，AI 将自动生成专业的流程图\n        </p>\n      </div>\n\n      {/* 输入区域 */}\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          流程描述\n        </label>\n        <textarea\n          id=\"description\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          placeholder=\"请描述你想要创建的流程图，例如：用户登录流程、订单处理流程、数据处理管道等...\"\n          className=\"w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n          disabled={isLoading}\n        />\n\n        {/* 示例提示 */}\n        <div className=\"mt-2\">\n          <p className=\"text-sm text-gray-600 mb-2\">💡 示例描述：</p>\n          <div className=\"flex flex-wrap gap-2\">\n            {[\n              \"用户注册和登录流程\",\n              \"在线购物订单处理流程\",\n              \"软件开发生命周期\",\n              \"客户服务处理流程\",\n              \"数据备份和恢复流程\"\n            ].map((example, index) => (\n              <button\n                key={index}\n                onClick={() => setDescription(example)}\n                disabled={isLoading}\n                className=\"px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {example}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* 错误提示 */}\n        {error && (\n          <div className=\"mt-2 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        )}\n\n        {/* 按钮组 */}\n        <div className=\"mt-4 flex flex-wrap gap-3\">\n          <button\n            onClick={handleGenerate}\n            disabled={isLoading || !description.trim()}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n          >\n            {isLoading && (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n            )}\n            <span>{isLoading ? '生成中...' : '生成流程图'}</span>\n          </button>\n\n          <button\n            onClick={handleLoadSample}\n            disabled={isLoading}\n            className=\"px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            加载示例\n          </button>\n\n          <button\n            onClick={handleClearAll}\n            disabled={isLoading}\n            className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            清空\n          </button>\n\n          <button\n            onClick={handleTestAPI}\n            disabled={isLoading}\n            className=\"px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            测试API\n          </button>\n        </div>\n      </div>\n\n      {/* 流程图显示区域 */}\n      {generatedXML && (\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">生成的流程图</h2>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={handleDownloadXML}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm\"\n              >\n                下载 XML\n              </button>\n              <button\n                onClick={handleOpenInDrawio}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm\"\n              >\n                在 Draw.io 中编辑\n              </button>\n            </div>\n          </div>\n\n          <DiagramViewer\n            xml={generatedXML}\n            className=\"w-full\"\n            config={{\n              width: 800,\n              height: 600,\n              toolbar: false,\n              menubar: false,\n              edit: false,\n            }}\n          />\n\n          {/* XML 代码显示（可选） */}\n          <details className=\"mt-4\">\n            <summary className=\"cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900\">\n              查看 XML 代码\n            </summary>\n            <pre className=\"mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-xs overflow-x-auto\">\n              <code>{generatedXML}</code>\n            </pre>\n          </details>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,EAAE;gBAC5B,QAAQ,GAAG,CAAC,qBAAqB,KAAK,GAAG;gBACzC,MAAM,aAAa,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;gBACpC,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,YAAY;YAC1B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,YAAY,CAAA,GAAA,gHAAA,CAAA,kBAAe,AAAD;QAChC,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc;QAEnB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAa,EAAE;YAAE,MAAM;QAAkB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;QAEnB,MAAM,aAAa,mBAAmB;QACtC,MAAM,YAAY,CAAC,sCAAsC,EAAE,YAAY;QACvE,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,aAAa;gBAAqB;YAC3D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,EAAE;gBAC5B,QAAQ,GAAG,CAAC,mBAAmB,KAAK,GAAG,CAAC,MAAM;gBAC9C,QAAQ,GAAG,CAAC,oBAAoB,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG;gBAEtD,0BAA0B;gBAC1B,gBAAgB,KAAK,GAAG;gBACxB,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,YAAY;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAA+C;;;;;;kCAGtF,6LAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,aAAY;wBACZ,WAAU;wBACV,UAAU;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,6LAAC;gCAAI,WAAU;0CACZ;oCACC;oCACA;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAEC,SAAS,IAAM,eAAe;wCAC9B,UAAU;wCACV,WAAU;kDAET;uCALI;;;;;;;;;;;;;;;;oBAYZ,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,CAAC,YAAY,IAAI;gCACxC,WAAU;;oCAET,2BACC,6LAAC;wCAAI,WAAU;;;;;;kDAEjB,6LAAC;kDAAM,YAAY,WAAW;;;;;;;;;;;;0CAGhC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAML,6LAAC,+HAAA,CAAA,UAAa;wBACZ,KAAK;wBACL,WAAU;wBACV,QAAQ;4BACN,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,SAAS;4BACT,MAAM;wBACR;;;;;;kCAIF,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAAuE;;;;;;0CAG1F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB;GAvPwB;KAAA", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}