{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/app/api/generate-diagram/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport OpenAI from 'openai';\n\nconst openai = new OpenAI({\n  apiKey: process.env.NEXT_PUBLIC_LLM_API_KEY,\n  baseURL: process.env.NEXT_PUBLIC_LLM_BASE_URL,\n});\n\n// 修复XML中的重复属性\nfunction fixDuplicateAttributes(xml: string): string {\n  // 匹配所有的XML标签\n  return xml.replace(/<([^>]+)>/g, (match, content) => {\n    // 如果是闭合标签或注释，直接返回\n    if (content.startsWith('/') || content.startsWith('!')) {\n      return match;\n    }\n\n    // 分离标签名和属性\n    const parts = content.trim().split(/\\s+/);\n    const tagName = parts[0];\n    const attributes = parts.slice(1);\n\n    // 收集唯一的属性\n    const uniqueAttrs = new Map<string, string>();\n\n    for (const attr of attributes) {\n      if (attr.includes('=')) {\n        const [key, ...valueParts] = attr.split('=');\n        const value = valueParts.join('=');\n        // 保留最后一个出现的属性值\n        uniqueAttrs.set(key, value);\n      }\n    }\n\n    // 重建标签\n    const rebuiltAttrs = Array.from(uniqueAttrs.entries())\n      .map(([key, value]) => `${key}=${value}`)\n      .join(' ');\n\n    return rebuiltAttrs ? `<${tagName} ${rebuiltAttrs}>` : `<${tagName}>`;\n  });\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { description } = await request.json();\n\n    if (!description) {\n      return NextResponse.json(\n        { error: '请提供流程图描述' },\n        { status: 400 }\n      );\n    }\n\n    const prompt = `\n请根据以下描述生成一个draw.io格式的XML流程图。\n\n重要要求：\n1. 使用标准的draw.io XML格式\n2. 每个XML元素的属性不能重复\n3. 所有的mxCell元素必须有唯一的id\n4. 使用vertex=\"1\"表示节点，edge=\"1\"表示连接线\n5. 布局清晰，坐标合理分布\n\n描述：${description}\n\n请直接返回XML代码，不要包含任何其他文本或解释。\n\n标准格式示例：\n<mxfile host=\"app.diagrams.net\" modified=\"2024-01-01T00:00:00.000Z\" agent=\"5.0\" version=\"24.7.17\" etag=\"example\">\n  <diagram name=\"Page-1\" id=\"example-id\">\n    <mxGraphModel dx=\"1422\" dy=\"794\" grid=\"1\" gridSize=\"10\" guides=\"1\" tooltips=\"1\" connect=\"1\" arrows=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\" pageWidth=\"827\" pageHeight=\"1169\" math=\"0\" shadow=\"0\">\n      <root>\n        <mxCell id=\"0\"/>\n        <mxCell id=\"1\" parent=\"0\"/>\n        <mxCell id=\"2\" value=\"开始\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"100\" y=\"100\" width=\"80\" height=\"40\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"3\" value=\"处理\" style=\"rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"100\" y=\"200\" width=\"80\" height=\"40\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"4\" value=\"\" style=\"endArrow=classic;html=1;rounded=0;\" edge=\"1\" parent=\"1\" source=\"2\" target=\"3\">\n          <mxGeometry width=\"50\" height=\"50\" relative=\"1\" as=\"geometry\"/>\n        </mxCell>\n      </root>\n    </mxGraphModel>\n  </diagram>\n</mxfile>\n`;\n\n    const completion = await openai.chat.completions.create({\n      model: process.env.NEXT_PUBLIC_LLM_DEFAULT_MODEL || \"Qwen/Qwen3-14B\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"你是一个专业的流程图设计师，擅长创建draw.io格式的XML流程图。请严格按照draw.io的XML格式标准生成流程图。\"\n        },\n        {\n          role: \"user\",\n          content: prompt\n        }\n      ],\n      temperature: 0.7,\n      max_tokens: 8192,\n    });\n\n    const rawContent = completion.choices[0]?.message?.content;\n\n    if (!rawContent) {\n      return NextResponse.json(\n        { error: '生成流程图失败' },\n        { status: 500 }\n      );\n    }\n\n    // 提取XML内容，去掉think标签和其他内容\n    let xmlContent = rawContent;\n\n    // 移除<think>标签及其内容\n    xmlContent = xmlContent.replace(/<think>[\\s\\S]*?<\\/think>/gi, '');\n\n    // 移除markdown代码块标记\n    xmlContent = xmlContent.replace(/```xml\\s*/g, '').replace(/```\\s*/g, '');\n\n    // 查找XML内容（从<mxfile>开始到</mxfile>结束）\n    const xmlMatch = xmlContent.match(/<mxfile[\\s\\S]*?<\\/mxfile>/i);\n\n    if (xmlMatch) {\n      xmlContent = xmlMatch[0];\n    } else {\n      // 如果没有找到完整的mxfile标签，尝试查找其他可能的XML结构\n      const altMatch = xmlContent.match(/<\\?xml[\\s\\S]*?<\\/[^>]+>/i) ||\n                      xmlContent.match(/<[^>]+[\\s\\S]*?<\\/[^>]+>/i);\n      if (altMatch) {\n        xmlContent = altMatch[0];\n      }\n    }\n\n    // 清理多余的空白字符\n    xmlContent = xmlContent.trim();\n\n    // 解码可能的HTML实体\n    xmlContent = xmlContent\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#x27;/g, \"'\")\n      .replace(/&amp;/g, '&');\n\n    // 修复可能的重复属性问题\n    xmlContent = fixDuplicateAttributes(xmlContent);\n\n    if (!xmlContent || !xmlContent.includes('<mxfile')) {\n      console.error('Invalid XML generated:', xmlContent.substring(0, 200));\n      return NextResponse.json(\n        { error: '生成的XML格式无效，请重试' },\n        { status: 500 }\n      );\n    }\n\n    console.log('Generated XML preview:', xmlContent.substring(0, 200));\n\n    return NextResponse.json({\n      xml: xmlContent,\n      success: true\n    });\n\n  } catch (error) {\n    console.error('生成流程图时出错:', error);\n    return NextResponse.json(\n      { error: '服务器内部错误' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAM,CAAC;IACxB,MAAM;IACN,OAAO;AACT;AAEA,cAAc;AACd,SAAS,uBAAuB,GAAW;IACzC,aAAa;IACb,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO;QACvC,kBAAkB;QAClB,IAAI,QAAQ,UAAU,CAAC,QAAQ,QAAQ,UAAU,CAAC,MAAM;YACtD,OAAO;QACT;QAEA,WAAW;QACX,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,MAAM,UAAU,KAAK,CAAC,EAAE;QACxB,MAAM,aAAa,MAAM,KAAK,CAAC;QAE/B,UAAU;QACV,MAAM,cAAc,IAAI;QAExB,KAAK,MAAM,QAAQ,WAAY;YAC7B,IAAI,KAAK,QAAQ,CAAC,MAAM;gBACtB,MAAM,CAAC,KAAK,GAAG,WAAW,GAAG,KAAK,KAAK,CAAC;gBACxC,MAAM,QAAQ,WAAW,IAAI,CAAC;gBAC9B,eAAe;gBACf,YAAY,GAAG,CAAC,KAAK;YACvB;QACF;QAEA,OAAO;QACP,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY,OAAO,IAChD,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EACvC,IAAI,CAAC;QAER,OAAO,eAAe,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IACvE;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE1C,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,CAAC;;;;;;;;;;GAUjB,EAAE,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAwBjB,CAAC;QAEG,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD,OAAO,sDAA6C;YACpD,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;QAEnD,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,IAAI,aAAa;QAEjB,kBAAkB;QAClB,aAAa,WAAW,OAAO,CAAC,8BAA8B;QAE9D,kBAAkB;QAClB,aAAa,WAAW,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW;QAErE,mCAAmC;QACnC,MAAM,WAAW,WAAW,KAAK,CAAC;QAElC,IAAI,UAAU;YACZ,aAAa,QAAQ,CAAC,EAAE;QAC1B,OAAO;YACL,mCAAmC;YACnC,MAAM,WAAW,WAAW,KAAK,CAAC,+BAClB,WAAW,KAAK,CAAC;YACjC,IAAI,UAAU;gBACZ,aAAa,QAAQ,CAAC,EAAE;YAC1B;QACF;QAEA,YAAY;QACZ,aAAa,WAAW,IAAI;QAE5B,cAAc;QACd,aAAa,WACV,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,WAAW,KACnB,OAAO,CAAC,UAAU;QAErB,cAAc;QACd,aAAa,uBAAuB;QAEpC,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,YAAY;YAClD,QAAQ,KAAK,CAAC,0BAA0B,WAAW,SAAS,CAAC,GAAG;YAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,0BAA0B,WAAW,SAAS,CAAC,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,KAAK;YACL,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAU,GACnB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}