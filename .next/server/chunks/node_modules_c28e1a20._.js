module.exports = {

"[project]/node_modules/webidl-conversions/lib/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var conversions = {};
module.exports = conversions;
function sign(x) {
    return x < 0 ? -1 : 1;
}
function evenRound(x) {
    // Round x to the nearest integer, choosing the even integer if it lies halfway between two.
    if (x % 1 === 0.5 && (x & 1) === 0) {
        return Math.floor(x);
    } else {
        return Math.round(x);
    }
}
function createNumberConversion(bitLength, typeOpts) {
    if (!typeOpts.unsigned) {
        --bitLength;
    }
    const lowerBound = typeOpts.unsigned ? 0 : -Math.pow(2, bitLength);
    const upperBound = Math.pow(2, bitLength) - 1;
    const moduloVal = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength) : Math.pow(2, bitLength);
    const moduloBound = typeOpts.moduloBitLength ? Math.pow(2, typeOpts.moduloBitLength - 1) : Math.pow(2, bitLength - 1);
    return function(V, opts) {
        if (!opts) opts = {};
        let x = +V;
        if (opts.enforceRange) {
            if (!Number.isFinite(x)) {
                throw new TypeError("Argument is not a finite number");
            }
            x = sign(x) * Math.floor(Math.abs(x));
            if (x < lowerBound || x > upperBound) {
                throw new TypeError("Argument is not in byte range");
            }
            return x;
        }
        if (!isNaN(x) && opts.clamp) {
            x = evenRound(x);
            if (x < lowerBound) x = lowerBound;
            if (x > upperBound) x = upperBound;
            return x;
        }
        if (!Number.isFinite(x) || x === 0) {
            return 0;
        }
        x = sign(x) * Math.floor(Math.abs(x));
        x = x % moduloVal;
        if (!typeOpts.unsigned && x >= moduloBound) {
            return x - moduloVal;
        } else if (typeOpts.unsigned) {
            if (x < 0) {
                x += moduloVal;
            } else if (x === -0) {
                return 0;
            }
        }
        return x;
    };
}
conversions["void"] = function() {
    return undefined;
};
conversions["boolean"] = function(val) {
    return !!val;
};
conversions["byte"] = createNumberConversion(8, {
    unsigned: false
});
conversions["octet"] = createNumberConversion(8, {
    unsigned: true
});
conversions["short"] = createNumberConversion(16, {
    unsigned: false
});
conversions["unsigned short"] = createNumberConversion(16, {
    unsigned: true
});
conversions["long"] = createNumberConversion(32, {
    unsigned: false
});
conversions["unsigned long"] = createNumberConversion(32, {
    unsigned: true
});
conversions["long long"] = createNumberConversion(32, {
    unsigned: false,
    moduloBitLength: 64
});
conversions["unsigned long long"] = createNumberConversion(32, {
    unsigned: true,
    moduloBitLength: 64
});
conversions["double"] = function(V) {
    const x = +V;
    if (!Number.isFinite(x)) {
        throw new TypeError("Argument is not a finite floating-point value");
    }
    return x;
};
conversions["unrestricted double"] = function(V) {
    const x = +V;
    if (isNaN(x)) {
        throw new TypeError("Argument is NaN");
    }
    return x;
};
// not quite valid, but good enough for JS
conversions["float"] = conversions["double"];
conversions["unrestricted float"] = conversions["unrestricted double"];
conversions["DOMString"] = function(V, opts) {
    if (!opts) opts = {};
    if (opts.treatNullAsEmptyString && V === null) {
        return "";
    }
    return String(V);
};
conversions["ByteString"] = function(V, opts) {
    const x = String(V);
    let c = undefined;
    for(let i = 0; (c = x.codePointAt(i)) !== undefined; ++i){
        if (c > 255) {
            throw new TypeError("Argument is not a valid bytestring");
        }
    }
    return x;
};
conversions["USVString"] = function(V) {
    const S = String(V);
    const n = S.length;
    const U = [];
    for(let i = 0; i < n; ++i){
        const c = S.charCodeAt(i);
        if (c < 0xD800 || c > 0xDFFF) {
            U.push(String.fromCodePoint(c));
        } else if (0xDC00 <= c && c <= 0xDFFF) {
            U.push(String.fromCodePoint(0xFFFD));
        } else {
            if (i === n - 1) {
                U.push(String.fromCodePoint(0xFFFD));
            } else {
                const d = S.charCodeAt(i + 1);
                if (0xDC00 <= d && d <= 0xDFFF) {
                    const a = c & 0x3FF;
                    const b = d & 0x3FF;
                    U.push(String.fromCodePoint((2 << 15) + (2 << 9) * a + b));
                    ++i;
                } else {
                    U.push(String.fromCodePoint(0xFFFD));
                }
            }
        }
    }
    return U.join('');
};
conversions["Date"] = function(V, opts) {
    if (!(V instanceof Date)) {
        throw new TypeError("Argument is not a Date object");
    }
    if (isNaN(V)) {
        return undefined;
    }
    return V;
};
conversions["RegExp"] = function(V, opts) {
    if (!(V instanceof RegExp)) {
        V = new RegExp(V);
    }
    return V;
};
}}),
"[project]/node_modules/whatwg-url/lib/utils.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports.mixin = function mixin(target, source) {
    const keys = Object.getOwnPropertyNames(source);
    for(let i = 0; i < keys.length; ++i){
        Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));
    }
};
module.exports.wrapperSymbol = Symbol("wrapper");
module.exports.implSymbol = Symbol("impl");
module.exports.wrapperForImpl = function(impl) {
    return impl[module.exports.wrapperSymbol];
};
module.exports.implForWrapper = function(wrapper) {
    return wrapper[module.exports.implSymbol];
};
}}),
"[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const punycode = __turbopack_context__.r("[externals]/punycode [external] (punycode, cjs)");
const tr46 = __turbopack_context__.r("[project]/node_modules/tr46/index.js [app-route] (ecmascript)");
const specialSchemes = {
    ftp: 21,
    file: null,
    gopher: 70,
    http: 80,
    https: 443,
    ws: 80,
    wss: 443
};
const failure = Symbol("failure");
function countSymbols(str) {
    return punycode.ucs2.decode(str).length;
}
function at(input, idx) {
    const c = input[idx];
    return isNaN(c) ? undefined : String.fromCodePoint(c);
}
function isASCIIDigit(c) {
    return c >= 0x30 && c <= 0x39;
}
function isASCIIAlpha(c) {
    return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;
}
function isASCIIAlphanumeric(c) {
    return isASCIIAlpha(c) || isASCIIDigit(c);
}
function isASCIIHex(c) {
    return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;
}
function isSingleDot(buffer) {
    return buffer === "." || buffer.toLowerCase() === "%2e";
}
function isDoubleDot(buffer) {
    buffer = buffer.toLowerCase();
    return buffer === ".." || buffer === "%2e." || buffer === ".%2e" || buffer === "%2e%2e";
}
function isWindowsDriveLetterCodePoints(cp1, cp2) {
    return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);
}
function isWindowsDriveLetterString(string) {
    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === ":" || string[1] === "|");
}
function isNormalizedWindowsDriveLetterString(string) {
    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === ":";
}
function containsForbiddenHostCodePoint(string) {
    return string.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|%|\/|:|\?|@|\[|\\|\]/) !== -1;
}
function containsForbiddenHostCodePointExcludingPercent(string) {
    return string.search(/\u0000|\u0009|\u000A|\u000D|\u0020|#|\/|:|\?|@|\[|\\|\]/) !== -1;
}
function isSpecialScheme(scheme) {
    return specialSchemes[scheme] !== undefined;
}
function isSpecial(url) {
    return isSpecialScheme(url.scheme);
}
function defaultPort(scheme) {
    return specialSchemes[scheme];
}
function percentEncode(c) {
    let hex = c.toString(16).toUpperCase();
    if (hex.length === 1) {
        hex = "0" + hex;
    }
    return "%" + hex;
}
function utf8PercentEncode(c) {
    const buf = new Buffer(c);
    let str = "";
    for(let i = 0; i < buf.length; ++i){
        str += percentEncode(buf[i]);
    }
    return str;
}
function utf8PercentDecode(str) {
    const input = new Buffer(str);
    const output = [];
    for(let i = 0; i < input.length; ++i){
        if (input[i] !== 37) {
            output.push(input[i]);
        } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {
            output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));
            i += 2;
        } else {
            output.push(input[i]);
        }
    }
    return new Buffer(output).toString();
}
function isC0ControlPercentEncode(c) {
    return c <= 0x1F || c > 0x7E;
}
const extraPathPercentEncodeSet = new Set([
    32,
    34,
    35,
    60,
    62,
    63,
    96,
    123,
    125
]);
function isPathPercentEncode(c) {
    return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);
}
const extraUserinfoPercentEncodeSet = new Set([
    47,
    58,
    59,
    61,
    64,
    91,
    92,
    93,
    94,
    124
]);
function isUserinfoPercentEncode(c) {
    return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);
}
function percentEncodeChar(c, encodeSetPredicate) {
    const cStr = String.fromCodePoint(c);
    if (encodeSetPredicate(c)) {
        return utf8PercentEncode(cStr);
    }
    return cStr;
}
function parseIPv4Number(input) {
    let R = 10;
    if (input.length >= 2 && input.charAt(0) === "0" && input.charAt(1).toLowerCase() === "x") {
        input = input.substring(2);
        R = 16;
    } else if (input.length >= 2 && input.charAt(0) === "0") {
        input = input.substring(1);
        R = 8;
    }
    if (input === "") {
        return 0;
    }
    const regex = R === 10 ? /[^0-9]/ : R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/;
    if (regex.test(input)) {
        return failure;
    }
    return parseInt(input, R);
}
function parseIPv4(input) {
    const parts = input.split(".");
    if (parts[parts.length - 1] === "") {
        if (parts.length > 1) {
            parts.pop();
        }
    }
    if (parts.length > 4) {
        return input;
    }
    const numbers = [];
    for (const part of parts){
        if (part === "") {
            return input;
        }
        const n = parseIPv4Number(part);
        if (n === failure) {
            return input;
        }
        numbers.push(n);
    }
    for(let i = 0; i < numbers.length - 1; ++i){
        if (numbers[i] > 255) {
            return failure;
        }
    }
    if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {
        return failure;
    }
    let ipv4 = numbers.pop();
    let counter = 0;
    for (const n of numbers){
        ipv4 += n * Math.pow(256, 3 - counter);
        ++counter;
    }
    return ipv4;
}
function serializeIPv4(address) {
    let output = "";
    let n = address;
    for(let i = 1; i <= 4; ++i){
        output = String(n % 256) + output;
        if (i !== 4) {
            output = "." + output;
        }
        n = Math.floor(n / 256);
    }
    return output;
}
function parseIPv6(input) {
    const address = [
        0,
        0,
        0,
        0,
        0,
        0,
        0,
        0
    ];
    let pieceIndex = 0;
    let compress = null;
    let pointer = 0;
    input = punycode.ucs2.decode(input);
    if (input[pointer] === 58) {
        if (input[pointer + 1] !== 58) {
            return failure;
        }
        pointer += 2;
        ++pieceIndex;
        compress = pieceIndex;
    }
    while(pointer < input.length){
        if (pieceIndex === 8) {
            return failure;
        }
        if (input[pointer] === 58) {
            if (compress !== null) {
                return failure;
            }
            ++pointer;
            ++pieceIndex;
            compress = pieceIndex;
            continue;
        }
        let value = 0;
        let length = 0;
        while(length < 4 && isASCIIHex(input[pointer])){
            value = value * 0x10 + parseInt(at(input, pointer), 16);
            ++pointer;
            ++length;
        }
        if (input[pointer] === 46) {
            if (length === 0) {
                return failure;
            }
            pointer -= length;
            if (pieceIndex > 6) {
                return failure;
            }
            let numbersSeen = 0;
            while(input[pointer] !== undefined){
                let ipv4Piece = null;
                if (numbersSeen > 0) {
                    if (input[pointer] === 46 && numbersSeen < 4) {
                        ++pointer;
                    } else {
                        return failure;
                    }
                }
                if (!isASCIIDigit(input[pointer])) {
                    return failure;
                }
                while(isASCIIDigit(input[pointer])){
                    const number = parseInt(at(input, pointer));
                    if (ipv4Piece === null) {
                        ipv4Piece = number;
                    } else if (ipv4Piece === 0) {
                        return failure;
                    } else {
                        ipv4Piece = ipv4Piece * 10 + number;
                    }
                    if (ipv4Piece > 255) {
                        return failure;
                    }
                    ++pointer;
                }
                address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;
                ++numbersSeen;
                if (numbersSeen === 2 || numbersSeen === 4) {
                    ++pieceIndex;
                }
            }
            if (numbersSeen !== 4) {
                return failure;
            }
            break;
        } else if (input[pointer] === 58) {
            ++pointer;
            if (input[pointer] === undefined) {
                return failure;
            }
        } else if (input[pointer] !== undefined) {
            return failure;
        }
        address[pieceIndex] = value;
        ++pieceIndex;
    }
    if (compress !== null) {
        let swaps = pieceIndex - compress;
        pieceIndex = 7;
        while(pieceIndex !== 0 && swaps > 0){
            const temp = address[compress + swaps - 1];
            address[compress + swaps - 1] = address[pieceIndex];
            address[pieceIndex] = temp;
            --pieceIndex;
            --swaps;
        }
    } else if (compress === null && pieceIndex !== 8) {
        return failure;
    }
    return address;
}
function serializeIPv6(address) {
    let output = "";
    const seqResult = findLongestZeroSequence(address);
    const compress = seqResult.idx;
    let ignore0 = false;
    for(let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex){
        if (ignore0 && address[pieceIndex] === 0) {
            continue;
        } else if (ignore0) {
            ignore0 = false;
        }
        if (compress === pieceIndex) {
            const separator = pieceIndex === 0 ? "::" : ":";
            output += separator;
            ignore0 = true;
            continue;
        }
        output += address[pieceIndex].toString(16);
        if (pieceIndex !== 7) {
            output += ":";
        }
    }
    return output;
}
function parseHost(input, isSpecialArg) {
    if (input[0] === "[") {
        if (input[input.length - 1] !== "]") {
            return failure;
        }
        return parseIPv6(input.substring(1, input.length - 1));
    }
    if (!isSpecialArg) {
        return parseOpaqueHost(input);
    }
    const domain = utf8PercentDecode(input);
    const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);
    if (asciiDomain === null) {
        return failure;
    }
    if (containsForbiddenHostCodePoint(asciiDomain)) {
        return failure;
    }
    const ipv4Host = parseIPv4(asciiDomain);
    if (typeof ipv4Host === "number" || ipv4Host === failure) {
        return ipv4Host;
    }
    return asciiDomain;
}
function parseOpaqueHost(input) {
    if (containsForbiddenHostCodePointExcludingPercent(input)) {
        return failure;
    }
    let output = "";
    const decoded = punycode.ucs2.decode(input);
    for(let i = 0; i < decoded.length; ++i){
        output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);
    }
    return output;
}
function findLongestZeroSequence(arr) {
    let maxIdx = null;
    let maxLen = 1; // only find elements > 1
    let currStart = null;
    let currLen = 0;
    for(let i = 0; i < arr.length; ++i){
        if (arr[i] !== 0) {
            if (currLen > maxLen) {
                maxIdx = currStart;
                maxLen = currLen;
            }
            currStart = null;
            currLen = 0;
        } else {
            if (currStart === null) {
                currStart = i;
            }
            ++currLen;
        }
    }
    // if trailing zeros
    if (currLen > maxLen) {
        maxIdx = currStart;
        maxLen = currLen;
    }
    return {
        idx: maxIdx,
        len: maxLen
    };
}
function serializeHost(host) {
    if (typeof host === "number") {
        return serializeIPv4(host);
    }
    // IPv6 serializer
    if (host instanceof Array) {
        return "[" + serializeIPv6(host) + "]";
    }
    return host;
}
function trimControlChars(url) {
    return url.replace(/^[\u0000-\u001F\u0020]+|[\u0000-\u001F\u0020]+$/g, "");
}
function trimTabAndNewline(url) {
    return url.replace(/\u0009|\u000A|\u000D/g, "");
}
function shortenPath(url) {
    const path = url.path;
    if (path.length === 0) {
        return;
    }
    if (url.scheme === "file" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {
        return;
    }
    path.pop();
}
function includesCredentials(url) {
    return url.username !== "" || url.password !== "";
}
function cannotHaveAUsernamePasswordPort(url) {
    return url.host === null || url.host === "" || url.cannotBeABaseURL || url.scheme === "file";
}
function isNormalizedWindowsDriveLetter(string) {
    return /^[A-Za-z]:$/.test(string);
}
function URLStateMachine(input, base, encodingOverride, url, stateOverride) {
    this.pointer = 0;
    this.input = input;
    this.base = base || null;
    this.encodingOverride = encodingOverride || "utf-8";
    this.stateOverride = stateOverride;
    this.url = url;
    this.failure = false;
    this.parseError = false;
    if (!this.url) {
        this.url = {
            scheme: "",
            username: "",
            password: "",
            host: null,
            port: null,
            path: [],
            query: null,
            fragment: null,
            cannotBeABaseURL: false
        };
        const res = trimControlChars(this.input);
        if (res !== this.input) {
            this.parseError = true;
        }
        this.input = res;
    }
    const res = trimTabAndNewline(this.input);
    if (res !== this.input) {
        this.parseError = true;
    }
    this.input = res;
    this.state = stateOverride || "scheme start";
    this.buffer = "";
    this.atFlag = false;
    this.arrFlag = false;
    this.passwordTokenSeenFlag = false;
    this.input = punycode.ucs2.decode(this.input);
    for(; this.pointer <= this.input.length; ++this.pointer){
        const c = this.input[this.pointer];
        const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);
        // exec state machine
        const ret = this["parse " + this.state](c, cStr);
        if (!ret) {
            break; // terminate algorithm
        } else if (ret === failure) {
            this.failure = true;
            break;
        }
    }
}
URLStateMachine.prototype["parse scheme start"] = function parseSchemeStart(c, cStr) {
    if (isASCIIAlpha(c)) {
        this.buffer += cStr.toLowerCase();
        this.state = "scheme";
    } else if (!this.stateOverride) {
        this.state = "no scheme";
        --this.pointer;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
URLStateMachine.prototype["parse scheme"] = function parseScheme(c, cStr) {
    if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {
        this.buffer += cStr.toLowerCase();
    } else if (c === 58) {
        if (this.stateOverride) {
            if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {
                return false;
            }
            if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {
                return false;
            }
            if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === "file") {
                return false;
            }
            if (this.url.scheme === "file" && (this.url.host === "" || this.url.host === null)) {
                return false;
            }
        }
        this.url.scheme = this.buffer;
        this.buffer = "";
        if (this.stateOverride) {
            return false;
        }
        if (this.url.scheme === "file") {
            if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {
                this.parseError = true;
            }
            this.state = "file";
        } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {
            this.state = "special relative or authority";
        } else if (isSpecial(this.url)) {
            this.state = "special authority slashes";
        } else if (this.input[this.pointer + 1] === 47) {
            this.state = "path or authority";
            ++this.pointer;
        } else {
            this.url.cannotBeABaseURL = true;
            this.url.path.push("");
            this.state = "cannot-be-a-base-URL path";
        }
    } else if (!this.stateOverride) {
        this.buffer = "";
        this.state = "no scheme";
        this.pointer = -1;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
URLStateMachine.prototype["parse no scheme"] = function parseNoScheme(c) {
    if (this.base === null || this.base.cannotBeABaseURL && c !== 35) {
        return failure;
    } else if (this.base.cannotBeABaseURL && c === 35) {
        this.url.scheme = this.base.scheme;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
        this.url.fragment = "";
        this.url.cannotBeABaseURL = true;
        this.state = "fragment";
    } else if (this.base.scheme === "file") {
        this.state = "file";
        --this.pointer;
    } else {
        this.state = "relative";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special relative or authority"] = function parseSpecialRelativeOrAuthority(c) {
    if (c === 47 && this.input[this.pointer + 1] === 47) {
        this.state = "special authority ignore slashes";
        ++this.pointer;
    } else {
        this.parseError = true;
        this.state = "relative";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse path or authority"] = function parsePathOrAuthority(c) {
    if (c === 47) {
        this.state = "authority";
    } else {
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse relative"] = function parseRelative(c) {
    this.url.scheme = this.base.scheme;
    if (isNaN(c)) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
    } else if (c === 47) {
        this.state = "relative slash";
    } else if (c === 63) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = "";
        this.state = "query";
    } else if (c === 35) {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice();
        this.url.query = this.base.query;
        this.url.fragment = "";
        this.state = "fragment";
    } else if (isSpecial(this.url) && c === 92) {
        this.parseError = true;
        this.state = "relative slash";
    } else {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.url.path = this.base.path.slice(0, this.base.path.length - 1);
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse relative slash"] = function parseRelativeSlash(c) {
    if (isSpecial(this.url) && (c === 47 || c === 92)) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "special authority ignore slashes";
    } else if (c === 47) {
        this.state = "authority";
    } else {
        this.url.username = this.base.username;
        this.url.password = this.base.password;
        this.url.host = this.base.host;
        this.url.port = this.base.port;
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special authority slashes"] = function parseSpecialAuthoritySlashes(c) {
    if (c === 47 && this.input[this.pointer + 1] === 47) {
        this.state = "special authority ignore slashes";
        ++this.pointer;
    } else {
        this.parseError = true;
        this.state = "special authority ignore slashes";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse special authority ignore slashes"] = function parseSpecialAuthorityIgnoreSlashes(c) {
    if (c !== 47 && c !== 92) {
        this.state = "authority";
        --this.pointer;
    } else {
        this.parseError = true;
    }
    return true;
};
URLStateMachine.prototype["parse authority"] = function parseAuthority(c, cStr) {
    if (c === 64) {
        this.parseError = true;
        if (this.atFlag) {
            this.buffer = "%40" + this.buffer;
        }
        this.atFlag = true;
        // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars
        const len = countSymbols(this.buffer);
        for(let pointer = 0; pointer < len; ++pointer){
            const codePoint = this.buffer.codePointAt(pointer);
            if (codePoint === 58 && !this.passwordTokenSeenFlag) {
                this.passwordTokenSeenFlag = true;
                continue;
            }
            const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);
            if (this.passwordTokenSeenFlag) {
                this.url.password += encodedCodePoints;
            } else {
                this.url.username += encodedCodePoints;
            }
        }
        this.buffer = "";
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {
        if (this.atFlag && this.buffer === "") {
            this.parseError = true;
            return failure;
        }
        this.pointer -= countSymbols(this.buffer) + 1;
        this.buffer = "";
        this.state = "host";
    } else {
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse hostname"] = URLStateMachine.prototype["parse host"] = function parseHostName(c, cStr) {
    if (this.stateOverride && this.url.scheme === "file") {
        --this.pointer;
        this.state = "file host";
    } else if (c === 58 && !this.arrFlag) {
        if (this.buffer === "") {
            this.parseError = true;
            return failure;
        }
        const host = parseHost(this.buffer, isSpecial(this.url));
        if (host === failure) {
            return failure;
        }
        this.url.host = host;
        this.buffer = "";
        this.state = "port";
        if (this.stateOverride === "hostname") {
            return false;
        }
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {
        --this.pointer;
        if (isSpecial(this.url) && this.buffer === "") {
            this.parseError = true;
            return failure;
        } else if (this.stateOverride && this.buffer === "" && (includesCredentials(this.url) || this.url.port !== null)) {
            this.parseError = true;
            return false;
        }
        const host = parseHost(this.buffer, isSpecial(this.url));
        if (host === failure) {
            return failure;
        }
        this.url.host = host;
        this.buffer = "";
        this.state = "path start";
        if (this.stateOverride) {
            return false;
        }
    } else {
        if (c === 91) {
            this.arrFlag = true;
        } else if (c === 93) {
            this.arrFlag = false;
        }
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse port"] = function parsePort(c, cStr) {
    if (isASCIIDigit(c)) {
        this.buffer += cStr;
    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92 || this.stateOverride) {
        if (this.buffer !== "") {
            const port = parseInt(this.buffer);
            if (port > Math.pow(2, 16) - 1) {
                this.parseError = true;
                return failure;
            }
            this.url.port = port === defaultPort(this.url.scheme) ? null : port;
            this.buffer = "";
        }
        if (this.stateOverride) {
            return false;
        }
        this.state = "path start";
        --this.pointer;
    } else {
        this.parseError = true;
        return failure;
    }
    return true;
};
const fileOtherwiseCodePoints = new Set([
    47,
    92,
    63,
    35
]);
URLStateMachine.prototype["parse file"] = function parseFile(c) {
    this.url.scheme = "file";
    if (c === 47 || c === 92) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "file slash";
    } else if (this.base !== null && this.base.scheme === "file") {
        if (isNaN(c)) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = this.base.query;
        } else if (c === 63) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = "";
            this.state = "query";
        } else if (c === 35) {
            this.url.host = this.base.host;
            this.url.path = this.base.path.slice();
            this.url.query = this.base.query;
            this.url.fragment = "";
            this.state = "fragment";
        } else {
            if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points
            !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) || this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points
            !fileOtherwiseCodePoints.has(this.input[this.pointer + 2])) {
                this.url.host = this.base.host;
                this.url.path = this.base.path.slice();
                shortenPath(this.url);
            } else {
                this.parseError = true;
            }
            this.state = "path";
            --this.pointer;
        }
    } else {
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse file slash"] = function parseFileSlash(c) {
    if (c === 47 || c === 92) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "file host";
    } else {
        if (this.base !== null && this.base.scheme === "file") {
            if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {
                this.url.path.push(this.base.path[0]);
            } else {
                this.url.host = this.base.host;
            }
        }
        this.state = "path";
        --this.pointer;
    }
    return true;
};
URLStateMachine.prototype["parse file host"] = function parseFileHost(c, cStr) {
    if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {
        --this.pointer;
        if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {
            this.parseError = true;
            this.state = "path";
        } else if (this.buffer === "") {
            this.url.host = "";
            if (this.stateOverride) {
                return false;
            }
            this.state = "path start";
        } else {
            let host = parseHost(this.buffer, isSpecial(this.url));
            if (host === failure) {
                return failure;
            }
            if (host === "localhost") {
                host = "";
            }
            this.url.host = host;
            if (this.stateOverride) {
                return false;
            }
            this.buffer = "";
            this.state = "path start";
        }
    } else {
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse path start"] = function parsePathStart(c) {
    if (isSpecial(this.url)) {
        if (c === 92) {
            this.parseError = true;
        }
        this.state = "path";
        if (c !== 47 && c !== 92) {
            --this.pointer;
        }
    } else if (!this.stateOverride && c === 63) {
        this.url.query = "";
        this.state = "query";
    } else if (!this.stateOverride && c === 35) {
        this.url.fragment = "";
        this.state = "fragment";
    } else if (c !== undefined) {
        this.state = "path";
        if (c !== 47) {
            --this.pointer;
        }
    }
    return true;
};
URLStateMachine.prototype["parse path"] = function parsePath(c) {
    if (isNaN(c) || c === 47 || isSpecial(this.url) && c === 92 || !this.stateOverride && (c === 63 || c === 35)) {
        if (isSpecial(this.url) && c === 92) {
            this.parseError = true;
        }
        if (isDoubleDot(this.buffer)) {
            shortenPath(this.url);
            if (c !== 47 && !(isSpecial(this.url) && c === 92)) {
                this.url.path.push("");
            }
        } else if (isSingleDot(this.buffer) && c !== 47 && !(isSpecial(this.url) && c === 92)) {
            this.url.path.push("");
        } else if (!isSingleDot(this.buffer)) {
            if (this.url.scheme === "file" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {
                if (this.url.host !== "" && this.url.host !== null) {
                    this.parseError = true;
                    this.url.host = "";
                }
                this.buffer = this.buffer[0] + ":";
            }
            this.url.path.push(this.buffer);
        }
        this.buffer = "";
        if (this.url.scheme === "file" && (c === undefined || c === 63 || c === 35)) {
            while(this.url.path.length > 1 && this.url.path[0] === ""){
                this.parseError = true;
                this.url.path.shift();
            }
        }
        if (c === 63) {
            this.url.query = "";
            this.state = "query";
        }
        if (c === 35) {
            this.url.fragment = "";
            this.state = "fragment";
        }
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.buffer += percentEncodeChar(c, isPathPercentEncode);
    }
    return true;
};
URLStateMachine.prototype["parse cannot-be-a-base-URL path"] = function parseCannotBeABaseURLPath(c) {
    if (c === 63) {
        this.url.query = "";
        this.state = "query";
    } else if (c === 35) {
        this.url.fragment = "";
        this.state = "fragment";
    } else {
        // TODO: Add: not a URL code point
        if (!isNaN(c) && c !== 37) {
            this.parseError = true;
        }
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        if (!isNaN(c)) {
            this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);
        }
    }
    return true;
};
URLStateMachine.prototype["parse query"] = function parseQuery(c, cStr) {
    if (isNaN(c) || !this.stateOverride && c === 35) {
        if (!isSpecial(this.url) || this.url.scheme === "ws" || this.url.scheme === "wss") {
            this.encodingOverride = "utf-8";
        }
        const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead
        for(let i = 0; i < buffer.length; ++i){
            if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 || buffer[i] === 0x3C || buffer[i] === 0x3E) {
                this.url.query += percentEncode(buffer[i]);
            } else {
                this.url.query += String.fromCodePoint(buffer[i]);
            }
        }
        this.buffer = "";
        if (c === 35) {
            this.url.fragment = "";
            this.state = "fragment";
        }
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.buffer += cStr;
    }
    return true;
};
URLStateMachine.prototype["parse fragment"] = function parseFragment(c) {
    if (isNaN(c)) {} else if (c === 0x0) {
        this.parseError = true;
    } else {
        // TODO: If c is not a URL code point and not "%", parse error.
        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {
            this.parseError = true;
        }
        this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);
    }
    return true;
};
function serializeURL(url, excludeFragment) {
    let output = url.scheme + ":";
    if (url.host !== null) {
        output += "//";
        if (url.username !== "" || url.password !== "") {
            output += url.username;
            if (url.password !== "") {
                output += ":" + url.password;
            }
            output += "@";
        }
        output += serializeHost(url.host);
        if (url.port !== null) {
            output += ":" + url.port;
        }
    } else if (url.host === null && url.scheme === "file") {
        output += "//";
    }
    if (url.cannotBeABaseURL) {
        output += url.path[0];
    } else {
        for (const string of url.path){
            output += "/" + string;
        }
    }
    if (url.query !== null) {
        output += "?" + url.query;
    }
    if (!excludeFragment && url.fragment !== null) {
        output += "#" + url.fragment;
    }
    return output;
}
function serializeOrigin(tuple) {
    let result = tuple.scheme + "://";
    result += serializeHost(tuple.host);
    if (tuple.port !== null) {
        result += ":" + tuple.port;
    }
    return result;
}
module.exports.serializeURL = serializeURL;
module.exports.serializeURLOrigin = function(url) {
    // https://url.spec.whatwg.org/#concept-url-origin
    switch(url.scheme){
        case "blob":
            try {
                return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));
            } catch (e) {
                // serializing an opaque origin returns "null"
                return "null";
            }
        case "ftp":
        case "gopher":
        case "http":
        case "https":
        case "ws":
        case "wss":
            return serializeOrigin({
                scheme: url.scheme,
                host: url.host,
                port: url.port
            });
        case "file":
            // spec says "exercise to the reader", chrome says "file://"
            return "file://";
        default:
            // serializing an opaque origin returns "null"
            return "null";
    }
};
module.exports.basicURLParse = function(input, options) {
    if (options === undefined) {
        options = {};
    }
    const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);
    if (usm.failure) {
        return "failure";
    }
    return usm.url;
};
module.exports.setTheUsername = function(url, username) {
    url.username = "";
    const decoded = punycode.ucs2.decode(username);
    for(let i = 0; i < decoded.length; ++i){
        url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);
    }
};
module.exports.setThePassword = function(url, password) {
    url.password = "";
    const decoded = punycode.ucs2.decode(password);
    for(let i = 0; i < decoded.length; ++i){
        url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);
    }
};
module.exports.serializeHost = serializeHost;
module.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;
module.exports.serializeInteger = function(integer) {
    return String(integer);
};
module.exports.parseURL = function(input, options) {
    if (options === undefined) {
        options = {};
    }
    // We don't handle blobs, so this just delegates:
    return module.exports.basicURLParse(input, {
        baseURL: options.baseURL,
        encodingOverride: options.encodingOverride
    });
};
}}),
"[project]/node_modules/whatwg-url/lib/URL-impl.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const usm = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)");
exports.implementation = class URLImpl {
    constructor(constructorArgs){
        const url = constructorArgs[0];
        const base = constructorArgs[1];
        let parsedBase = null;
        if (base !== undefined) {
            parsedBase = usm.basicURLParse(base);
            if (parsedBase === "failure") {
                throw new TypeError("Invalid base URL");
            }
        }
        const parsedURL = usm.basicURLParse(url, {
            baseURL: parsedBase
        });
        if (parsedURL === "failure") {
            throw new TypeError("Invalid URL");
        }
        this._url = parsedURL;
    // TODO: query stuff
    }
    get href() {
        return usm.serializeURL(this._url);
    }
    set href(v) {
        const parsedURL = usm.basicURLParse(v);
        if (parsedURL === "failure") {
            throw new TypeError("Invalid URL");
        }
        this._url = parsedURL;
    }
    get origin() {
        return usm.serializeURLOrigin(this._url);
    }
    get protocol() {
        return this._url.scheme + ":";
    }
    set protocol(v) {
        usm.basicURLParse(v + ":", {
            url: this._url,
            stateOverride: "scheme start"
        });
    }
    get username() {
        return this._url.username;
    }
    set username(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        usm.setTheUsername(this._url, v);
    }
    get password() {
        return this._url.password;
    }
    set password(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        usm.setThePassword(this._url, v);
    }
    get host() {
        const url = this._url;
        if (url.host === null) {
            return "";
        }
        if (url.port === null) {
            return usm.serializeHost(url.host);
        }
        return usm.serializeHost(url.host) + ":" + usm.serializeInteger(url.port);
    }
    set host(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "host"
        });
    }
    get hostname() {
        if (this._url.host === null) {
            return "";
        }
        return usm.serializeHost(this._url.host);
    }
    set hostname(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "hostname"
        });
    }
    get port() {
        if (this._url.port === null) {
            return "";
        }
        return usm.serializeInteger(this._url.port);
    }
    set port(v) {
        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {
            return;
        }
        if (v === "") {
            this._url.port = null;
        } else {
            usm.basicURLParse(v, {
                url: this._url,
                stateOverride: "port"
            });
        }
    }
    get pathname() {
        if (this._url.cannotBeABaseURL) {
            return this._url.path[0];
        }
        if (this._url.path.length === 0) {
            return "";
        }
        return "/" + this._url.path.join("/");
    }
    set pathname(v) {
        if (this._url.cannotBeABaseURL) {
            return;
        }
        this._url.path = [];
        usm.basicURLParse(v, {
            url: this._url,
            stateOverride: "path start"
        });
    }
    get search() {
        if (this._url.query === null || this._url.query === "") {
            return "";
        }
        return "?" + this._url.query;
    }
    set search(v) {
        // TODO: query stuff
        const url = this._url;
        if (v === "") {
            url.query = null;
            return;
        }
        const input = v[0] === "?" ? v.substring(1) : v;
        url.query = "";
        usm.basicURLParse(input, {
            url,
            stateOverride: "query"
        });
    }
    get hash() {
        if (this._url.fragment === null || this._url.fragment === "") {
            return "";
        }
        return "#" + this._url.fragment;
    }
    set hash(v) {
        if (v === "") {
            this._url.fragment = null;
            return;
        }
        const input = v[0] === "#" ? v.substring(1) : v;
        this._url.fragment = "";
        usm.basicURLParse(input, {
            url: this._url,
            stateOverride: "fragment"
        });
    }
    toJSON() {
        return this.href;
    }
};
}}),
"[project]/node_modules/whatwg-url/lib/URL.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const conversions = __turbopack_context__.r("[project]/node_modules/webidl-conversions/lib/index.js [app-route] (ecmascript)");
const utils = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/utils.js [app-route] (ecmascript)");
const Impl = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/URL-impl.js [app-route] (ecmascript)");
const impl = utils.implSymbol;
function URL(url) {
    if (!this || this[impl] || !(this instanceof URL)) {
        throw new TypeError("Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");
    }
    if (arguments.length < 1) {
        throw new TypeError("Failed to construct 'URL': 1 argument required, but only " + arguments.length + " present.");
    }
    const args = [];
    for(let i = 0; i < arguments.length && i < 2; ++i){
        args[i] = arguments[i];
    }
    args[0] = conversions["USVString"](args[0]);
    if (args[1] !== undefined) {
        args[1] = conversions["USVString"](args[1]);
    }
    module.exports.setup(this, args);
}
URL.prototype.toJSON = function toJSON() {
    if (!this || !module.exports.is(this)) {
        throw new TypeError("Illegal invocation");
    }
    const args = [];
    for(let i = 0; i < arguments.length && i < 0; ++i){
        args[i] = arguments[i];
    }
    return this[impl].toJSON.apply(this[impl], args);
};
Object.defineProperty(URL.prototype, "href", {
    get () {
        return this[impl].href;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].href = V;
    },
    enumerable: true,
    configurable: true
});
URL.prototype.toString = function() {
    if (!this || !module.exports.is(this)) {
        throw new TypeError("Illegal invocation");
    }
    return this.href;
};
Object.defineProperty(URL.prototype, "origin", {
    get () {
        return this[impl].origin;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "protocol", {
    get () {
        return this[impl].protocol;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].protocol = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "username", {
    get () {
        return this[impl].username;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].username = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "password", {
    get () {
        return this[impl].password;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].password = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "host", {
    get () {
        return this[impl].host;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].host = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "hostname", {
    get () {
        return this[impl].hostname;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].hostname = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "port", {
    get () {
        return this[impl].port;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].port = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "pathname", {
    get () {
        return this[impl].pathname;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].pathname = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "search", {
    get () {
        return this[impl].search;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].search = V;
    },
    enumerable: true,
    configurable: true
});
Object.defineProperty(URL.prototype, "hash", {
    get () {
        return this[impl].hash;
    },
    set (V) {
        V = conversions["USVString"](V);
        this[impl].hash = V;
    },
    enumerable: true,
    configurable: true
});
module.exports = {
    is (obj) {
        return !!obj && obj[impl] instanceof Impl.implementation;
    },
    create (constructorArgs, privateData) {
        let obj = Object.create(URL.prototype);
        this.setup(obj, constructorArgs, privateData);
        return obj;
    },
    setup (obj, constructorArgs, privateData) {
        if (!privateData) privateData = {};
        privateData.wrapper = obj;
        obj[impl] = new Impl.implementation(constructorArgs, privateData);
        obj[impl][utils.wrapperSymbol] = obj;
    },
    interface: URL,
    expose: {
        Window: {
            URL: URL
        },
        Worker: {
            URL: URL
        }
    }
};
}}),
"[project]/node_modules/whatwg-url/lib/public-api.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
exports.URL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/URL.js [app-route] (ecmascript)").interface;
exports.serializeURL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeURL;
exports.serializeURLOrigin = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeURLOrigin;
exports.basicURLParse = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").basicURLParse;
exports.setTheUsername = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").setTheUsername;
exports.setThePassword = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").setThePassword;
exports.serializeHost = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeHost;
exports.serializeInteger = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").serializeInteger;
exports.parseURL = __turbopack_context__.r("[project]/node_modules/whatwg-url/lib/url-state-machine.js [app-route] (ecmascript)").parseURL;
}}),
"[project]/node_modules/node-fetch/lib/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AbortError": (()=>AbortError),
    "FetchError": (()=>FetchError),
    "Headers": (()=>Headers),
    "Request": (()=>Request),
    "Response": (()=>Response),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/stream [external] (stream, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/http [external] (http, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/url [external] (url, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/whatwg-url/lib/public-api.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/https [external] (https, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/zlib [external] (zlib, cjs)");
;
;
;
;
;
;
// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js
// fix for "Readable" isn't a named export issue
const Readable = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable;
const BUFFER = Symbol('buffer');
const TYPE = Symbol('type');
class Blob {
    constructor(){
        this[TYPE] = '';
        const blobParts = arguments[0];
        const options = arguments[1];
        const buffers = [];
        let size = 0;
        if (blobParts) {
            const a = blobParts;
            const length = Number(a.length);
            for(let i = 0; i < length; i++){
                const element = a[i];
                let buffer;
                if (element instanceof Buffer) {
                    buffer = element;
                } else if (ArrayBuffer.isView(element)) {
                    buffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);
                } else if (element instanceof ArrayBuffer) {
                    buffer = Buffer.from(element);
                } else if (element instanceof Blob) {
                    buffer = element[BUFFER];
                } else {
                    buffer = Buffer.from(typeof element === 'string' ? element : String(element));
                }
                size += buffer.length;
                buffers.push(buffer);
            }
        }
        this[BUFFER] = Buffer.concat(buffers);
        let type = options && options.type !== undefined && String(options.type).toLowerCase();
        if (type && !/[^\u0020-\u007E]/.test(type)) {
            this[TYPE] = type;
        }
    }
    get size() {
        return this[BUFFER].length;
    }
    get type() {
        return this[TYPE];
    }
    text() {
        return Promise.resolve(this[BUFFER].toString());
    }
    arrayBuffer() {
        const buf = this[BUFFER];
        const ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);
        return Promise.resolve(ab);
    }
    stream() {
        const readable = new Readable();
        readable._read = function() {};
        readable.push(this[BUFFER]);
        readable.push(null);
        return readable;
    }
    toString() {
        return '[object Blob]';
    }
    slice() {
        const size = this.size;
        const start = arguments[0];
        const end = arguments[1];
        let relativeStart, relativeEnd;
        if (start === undefined) {
            relativeStart = 0;
        } else if (start < 0) {
            relativeStart = Math.max(size + start, 0);
        } else {
            relativeStart = Math.min(start, size);
        }
        if (end === undefined) {
            relativeEnd = size;
        } else if (end < 0) {
            relativeEnd = Math.max(size + end, 0);
        } else {
            relativeEnd = Math.min(end, size);
        }
        const span = Math.max(relativeEnd - relativeStart, 0);
        const buffer = this[BUFFER];
        const slicedBuffer = buffer.slice(relativeStart, relativeStart + span);
        const blob = new Blob([], {
            type: arguments[2]
        });
        blob[BUFFER] = slicedBuffer;
        return blob;
    }
}
Object.defineProperties(Blob.prototype, {
    size: {
        enumerable: true
    },
    type: {
        enumerable: true
    },
    slice: {
        enumerable: true
    }
});
Object.defineProperty(Blob.prototype, Symbol.toStringTag, {
    value: 'Blob',
    writable: false,
    enumerable: false,
    configurable: true
});
/**
 * fetch-error.js
 *
 * FetchError interface for operational errors
 */ /**
 * Create FetchError instance
 *
 * @param   String      message      Error message for human
 * @param   String      type         Error type for machine
 * @param   String      systemError  For Node.js system error
 * @return  FetchError
 */ function FetchError(message, type, systemError) {
    Error.call(this, message);
    this.message = message;
    this.type = type;
    // when err.type is `system`, err.code contains system error code
    if (systemError) {
        this.code = this.errno = systemError.code;
    }
    // hide custom error implementation details from end-users
    Error.captureStackTrace(this, this.constructor);
}
FetchError.prototype = Object.create(Error.prototype);
FetchError.prototype.constructor = FetchError;
FetchError.prototype.name = 'FetchError';
let convert;
try {
    convert = (()=>{
        const e = new Error("Cannot find module 'encoding'");
        e.code = 'MODULE_NOT_FOUND';
        throw e;
    })().convert;
} catch (e) {}
const INTERNALS = Symbol('Body internals');
// fix an issue where "PassThrough" isn't a named export for node <10
const PassThrough = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].PassThrough;
/**
 * Body mixin
 *
 * Ref: https://fetch.spec.whatwg.org/#body
 *
 * @param   Stream  body  Readable stream
 * @param   Object  opts  Response options
 * @return  Void
 */ function Body(body) {
    var _this = this;
    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, _ref$size = _ref.size;
    let size = _ref$size === undefined ? 0 : _ref$size;
    var _ref$timeout = _ref.timeout;
    let timeout = _ref$timeout === undefined ? 0 : _ref$timeout;
    if (body == null) {
        // body is undefined or null
        body = null;
    } else if (isURLSearchParams(body)) {
        // body is a URLSearchParams
        body = Buffer.from(body.toString());
    } else if (isBlob(body)) ;
    else if (Buffer.isBuffer(body)) ;
    else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {
        // body is ArrayBuffer
        body = Buffer.from(body);
    } else if (ArrayBuffer.isView(body)) {
        // body is ArrayBufferView
        body = Buffer.from(body.buffer, body.byteOffset, body.byteLength);
    } else if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) ;
    else {
        // none of the above
        // coerce to string then buffer
        body = Buffer.from(String(body));
    }
    this[INTERNALS] = {
        body,
        disturbed: false,
        error: null
    };
    this.size = size;
    this.timeout = timeout;
    if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) {
        body.on('error', function(err) {
            const error = err.name === 'AbortError' ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, 'system', err);
            _this[INTERNALS].error = error;
        });
    }
}
Body.prototype = {
    get body () {
        return this[INTERNALS].body;
    },
    get bodyUsed () {
        return this[INTERNALS].disturbed;
    },
    /**
  * Decode response as ArrayBuffer
  *
  * @return  Promise
  */ arrayBuffer () {
        return consumeBody.call(this).then(function(buf) {
            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);
        });
    },
    /**
  * Return raw response as Blob
  *
  * @return Promise
  */ blob () {
        let ct = this.headers && this.headers.get('content-type') || '';
        return consumeBody.call(this).then(function(buf) {
            return Object.assign(// Prevent copying
            new Blob([], {
                type: ct.toLowerCase()
            }), {
                [BUFFER]: buf
            });
        });
    },
    /**
  * Decode response as json
  *
  * @return  Promise
  */ json () {
        var _this2 = this;
        return consumeBody.call(this).then(function(buffer) {
            try {
                return JSON.parse(buffer.toString());
            } catch (err) {
                return Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, 'invalid-json'));
            }
        });
    },
    /**
  * Decode response as text
  *
  * @return  Promise
  */ text () {
        return consumeBody.call(this).then(function(buffer) {
            return buffer.toString();
        });
    },
    /**
  * Decode response as buffer (non-spec api)
  *
  * @return  Promise
  */ buffer () {
        return consumeBody.call(this);
    },
    /**
  * Decode response as text, while automatically detecting the encoding and
  * trying to decode to UTF-8 (non-spec api)
  *
  * @return  Promise
  */ textConverted () {
        var _this3 = this;
        return consumeBody.call(this).then(function(buffer) {
            return convertBody(buffer, _this3.headers);
        });
    }
};
// In browsers, all properties are enumerable.
Object.defineProperties(Body.prototype, {
    body: {
        enumerable: true
    },
    bodyUsed: {
        enumerable: true
    },
    arrayBuffer: {
        enumerable: true
    },
    blob: {
        enumerable: true
    },
    json: {
        enumerable: true
    },
    text: {
        enumerable: true
    }
});
Body.mixIn = function(proto) {
    for (const name of Object.getOwnPropertyNames(Body.prototype)){
        // istanbul ignore else: future proof
        if (!(name in proto)) {
            const desc = Object.getOwnPropertyDescriptor(Body.prototype, name);
            Object.defineProperty(proto, name, desc);
        }
    }
};
/**
 * Consume and convert an entire Body to a Buffer.
 *
 * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body
 *
 * @return  Promise
 */ function consumeBody() {
    var _this4 = this;
    if (this[INTERNALS].disturbed) {
        return Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));
    }
    this[INTERNALS].disturbed = true;
    if (this[INTERNALS].error) {
        return Body.Promise.reject(this[INTERNALS].error);
    }
    let body = this.body;
    // body is null
    if (body === null) {
        return Body.Promise.resolve(Buffer.alloc(0));
    }
    // body is blob
    if (isBlob(body)) {
        body = body.stream();
    }
    // body is buffer
    if (Buffer.isBuffer(body)) {
        return Body.Promise.resolve(body);
    }
    // istanbul ignore if: should never happen
    if (!(body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"])) {
        return Body.Promise.resolve(Buffer.alloc(0));
    }
    // body is stream
    // get ready to actually consume the body
    let accum = [];
    let accumBytes = 0;
    let abort = false;
    return new Body.Promise(function(resolve, reject) {
        let resTimeout;
        // allow timeout on slow response body
        if (_this4.timeout) {
            resTimeout = setTimeout(function() {
                abort = true;
                reject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, 'body-timeout'));
            }, _this4.timeout);
        }
        // handle stream errors
        body.on('error', function(err) {
            if (err.name === 'AbortError') {
                // if the request was aborted, reject with this Error
                abort = true;
                reject(err);
            } else {
                // other errors, such as incorrect content-encoding
                reject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, 'system', err));
            }
        });
        body.on('data', function(chunk) {
            if (abort || chunk === null) {
                return;
            }
            if (_this4.size && accumBytes + chunk.length > _this4.size) {
                abort = true;
                reject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, 'max-size'));
                return;
            }
            accumBytes += chunk.length;
            accum.push(chunk);
        });
        body.on('end', function() {
            if (abort) {
                return;
            }
            clearTimeout(resTimeout);
            try {
                resolve(Buffer.concat(accum, accumBytes));
            } catch (err) {
                // handle streams that have accumulated too much data (issue #414)
                reject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, 'system', err));
            }
        });
    });
}
/**
 * Detect buffer encoding and convert to target encoding
 * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding
 *
 * @param   Buffer  buffer    Incoming buffer
 * @param   String  encoding  Target encoding
 * @return  String
 */ function convertBody(buffer, headers) {
    if (typeof convert !== 'function') {
        throw new Error('The package `encoding` must be installed to use the textConverted() function');
    }
    const ct = headers.get('content-type');
    let charset = 'utf-8';
    let res, str;
    // header
    if (ct) {
        res = /charset=([^;]*)/i.exec(ct);
    }
    // no charset in content type, peek at response body for at most 1024 bytes
    str = buffer.slice(0, 1024).toString();
    // html5
    if (!res && str) {
        res = /<meta.+?charset=(['"])(.+?)\1/i.exec(str);
    }
    // html4
    if (!res && str) {
        res = /<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(str);
        if (!res) {
            res = /<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(str);
            if (res) {
                res.pop(); // drop last quote
            }
        }
        if (res) {
            res = /charset=(.*)/i.exec(res.pop());
        }
    }
    // xml
    if (!res && str) {
        res = /<\?xml.+?encoding=(['"])(.+?)\1/i.exec(str);
    }
    // found charset
    if (res) {
        charset = res.pop();
        // prevent decode issues when sites use incorrect encoding
        // ref: https://hsivonen.fi/encoding-menu/
        if (charset === 'gb2312' || charset === 'gbk') {
            charset = 'gb18030';
        }
    }
    // turn raw buffers into a single utf-8 buffer
    return convert(buffer, 'UTF-8', charset).toString();
}
/**
 * Detect a URLSearchParams object
 * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143
 *
 * @param   Object  obj     Object to detect by type or brand
 * @return  String
 */ function isURLSearchParams(obj) {
    // Duck-typing as a necessary condition.
    if (typeof obj !== 'object' || typeof obj.append !== 'function' || typeof obj.delete !== 'function' || typeof obj.get !== 'function' || typeof obj.getAll !== 'function' || typeof obj.has !== 'function' || typeof obj.set !== 'function') {
        return false;
    }
    // Brand-checking and more duck-typing as optional condition.
    return obj.constructor.name === 'URLSearchParams' || Object.prototype.toString.call(obj) === '[object URLSearchParams]' || typeof obj.sort === 'function';
}
/**
 * Check if `obj` is a W3C `Blob` object (which `File` inherits from)
 * @param  {*} obj
 * @return {boolean}
 */ function isBlob(obj) {
    return typeof obj === 'object' && typeof obj.arrayBuffer === 'function' && typeof obj.type === 'string' && typeof obj.stream === 'function' && typeof obj.constructor === 'function' && typeof obj.constructor.name === 'string' && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);
}
/**
 * Clone body given Res/Req instance
 *
 * @param   Mixed  instance  Response or Request instance
 * @return  Mixed
 */ function clone(instance) {
    let p1, p2;
    let body = instance.body;
    // don't allow cloning a used body
    if (instance.bodyUsed) {
        throw new Error('cannot clone body after it is used');
    }
    // check that body is a stream and not form-data object
    // note: we can't clone the form-data object without having it as a dependency
    if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"] && typeof body.getBoundary !== 'function') {
        // tee instance body
        p1 = new PassThrough();
        p2 = new PassThrough();
        body.pipe(p1);
        body.pipe(p2);
        // set instance body to teed body and return the other teed body
        instance[INTERNALS].body = p1;
        body = p2;
    }
    return body;
}
/**
 * Performs the operation "extract a `Content-Type` value from |object|" as
 * specified in the specification:
 * https://fetch.spec.whatwg.org/#concept-bodyinit-extract
 *
 * This function assumes that instance.body is present.
 *
 * @param   Mixed  instance  Any options.body input
 */ function extractContentType(body) {
    if (body === null) {
        // body is null
        return null;
    } else if (typeof body === 'string') {
        // body is string
        return 'text/plain;charset=UTF-8';
    } else if (isURLSearchParams(body)) {
        // body is a URLSearchParams
        return 'application/x-www-form-urlencoded;charset=UTF-8';
    } else if (isBlob(body)) {
        // body is blob
        return body.type || null;
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        return null;
    } else if (Object.prototype.toString.call(body) === '[object ArrayBuffer]') {
        // body is ArrayBuffer
        return null;
    } else if (ArrayBuffer.isView(body)) {
        // body is ArrayBufferView
        return null;
    } else if (typeof body.getBoundary === 'function') {
        // detect form data input from form-data module
        return `multipart/form-data;boundary=${body.getBoundary()}`;
    } else if (body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"]) {
        // body is stream
        // can't really do much about this
        return null;
    } else {
        // Body constructor defaults other things to string
        return 'text/plain;charset=UTF-8';
    }
}
/**
 * The Fetch Standard treats this as if "total bytes" is a property on the body.
 * For us, we have to explicitly get it with a function.
 *
 * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes
 *
 * @param   Body    instance   Instance of Body
 * @return  Number?            Number of bytes, or null if not possible
 */ function getTotalBytes(instance) {
    const body = instance.body;
    if (body === null) {
        // body is null
        return 0;
    } else if (isBlob(body)) {
        return body.size;
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        return body.length;
    } else if (body && typeof body.getLengthSync === 'function') {
        // detect form data input from form-data module
        if (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x
        body.hasKnownLength && body.hasKnownLength()) {
            // 2.x
            return body.getLengthSync();
        }
        return null;
    } else {
        // body is stream
        return null;
    }
}
/**
 * Write a Body to a Node.js WritableStream (e.g. http.Request) object.
 *
 * @param   Body    instance   Instance of Body
 * @return  Void
 */ function writeToStream(dest, instance) {
    const body = instance.body;
    if (body === null) {
        // body is null
        dest.end();
    } else if (isBlob(body)) {
        body.stream().pipe(dest);
    } else if (Buffer.isBuffer(body)) {
        // body is buffer
        dest.write(body);
        dest.end();
    } else {
        // body is stream
        body.pipe(dest);
    }
}
// expose Promise
Body.Promise = global.Promise;
/**
 * headers.js
 *
 * Headers class offers convenient helpers
 */ const invalidTokenRegex = /[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/;
const invalidHeaderCharRegex = /[^\t\x20-\x7e\x80-\xff]/;
function validateName(name) {
    name = `${name}`;
    if (invalidTokenRegex.test(name) || name === '') {
        throw new TypeError(`${name} is not a legal HTTP header name`);
    }
}
function validateValue(value) {
    value = `${value}`;
    if (invalidHeaderCharRegex.test(value)) {
        throw new TypeError(`${value} is not a legal HTTP header value`);
    }
}
/**
 * Find the key in the map object given a header name.
 *
 * Returns undefined if not found.
 *
 * @param   String  name  Header name
 * @return  String|Undefined
 */ function find(map, name) {
    name = name.toLowerCase();
    for(const key in map){
        if (key.toLowerCase() === name) {
            return key;
        }
    }
    return undefined;
}
const MAP = Symbol('map');
class Headers {
    /**
  * Headers class
  *
  * @param   Object  headers  Response headers
  * @return  Void
  */ constructor(){
        let init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;
        this[MAP] = Object.create(null);
        if (init instanceof Headers) {
            const rawHeaders = init.raw();
            const headerNames = Object.keys(rawHeaders);
            for (const headerName of headerNames){
                for (const value of rawHeaders[headerName]){
                    this.append(headerName, value);
                }
            }
            return;
        }
        // We don't worry about converting prop to ByteString here as append()
        // will handle it.
        if (init == null) ;
        else if (typeof init === 'object') {
            const method = init[Symbol.iterator];
            if (method != null) {
                if (typeof method !== 'function') {
                    throw new TypeError('Header pairs must be iterable');
                }
                // sequence<sequence<ByteString>>
                // Note: per spec we have to first exhaust the lists then process them
                const pairs = [];
                for (const pair of init){
                    if (typeof pair !== 'object' || typeof pair[Symbol.iterator] !== 'function') {
                        throw new TypeError('Each header pair must be iterable');
                    }
                    pairs.push(Array.from(pair));
                }
                for (const pair of pairs){
                    if (pair.length !== 2) {
                        throw new TypeError('Each header pair must be a name/value tuple');
                    }
                    this.append(pair[0], pair[1]);
                }
            } else {
                // record<ByteString, ByteString>
                for (const key of Object.keys(init)){
                    const value = init[key];
                    this.append(key, value);
                }
            }
        } else {
            throw new TypeError('Provided initializer must be an object');
        }
    }
    /**
  * Return combined header value given name
  *
  * @param   String  name  Header name
  * @return  Mixed
  */ get(name) {
        name = `${name}`;
        validateName(name);
        const key = find(this[MAP], name);
        if (key === undefined) {
            return null;
        }
        return this[MAP][key].join(', ');
    }
    /**
  * Iterate over all headers
  *
  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)
  * @param   Boolean   thisArg   `this` context for callback function
  * @return  Void
  */ forEach(callback) {
        let thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;
        let pairs = getHeaders(this);
        let i = 0;
        while(i < pairs.length){
            var _pairs$i = pairs[i];
            const name = _pairs$i[0], value = _pairs$i[1];
            callback.call(thisArg, value, name, this);
            pairs = getHeaders(this);
            i++;
        }
    }
    /**
  * Overwrite header values given name
  *
  * @param   String  name   Header name
  * @param   String  value  Header value
  * @return  Void
  */ set(name, value) {
        name = `${name}`;
        value = `${value}`;
        validateName(name);
        validateValue(value);
        const key = find(this[MAP], name);
        this[MAP][key !== undefined ? key : name] = [
            value
        ];
    }
    /**
  * Append a value onto existing header
  *
  * @param   String  name   Header name
  * @param   String  value  Header value
  * @return  Void
  */ append(name, value) {
        name = `${name}`;
        value = `${value}`;
        validateName(name);
        validateValue(value);
        const key = find(this[MAP], name);
        if (key !== undefined) {
            this[MAP][key].push(value);
        } else {
            this[MAP][name] = [
                value
            ];
        }
    }
    /**
  * Check for header name existence
  *
  * @param   String   name  Header name
  * @return  Boolean
  */ has(name) {
        name = `${name}`;
        validateName(name);
        return find(this[MAP], name) !== undefined;
    }
    /**
  * Delete all header values given name
  *
  * @param   String  name  Header name
  * @return  Void
  */ delete(name) {
        name = `${name}`;
        validateName(name);
        const key = find(this[MAP], name);
        if (key !== undefined) {
            delete this[MAP][key];
        }
    }
    /**
  * Return raw headers (non-spec api)
  *
  * @return  Object
  */ raw() {
        return this[MAP];
    }
    /**
  * Get an iterator on keys.
  *
  * @return  Iterator
  */ keys() {
        return createHeadersIterator(this, 'key');
    }
    /**
  * Get an iterator on values.
  *
  * @return  Iterator
  */ values() {
        return createHeadersIterator(this, 'value');
    }
    /**
  * Get an iterator on entries.
  *
  * This is the default iterator of the Headers object.
  *
  * @return  Iterator
  */ [Symbol.iterator]() {
        return createHeadersIterator(this, 'key+value');
    }
}
Headers.prototype.entries = Headers.prototype[Symbol.iterator];
Object.defineProperty(Headers.prototype, Symbol.toStringTag, {
    value: 'Headers',
    writable: false,
    enumerable: false,
    configurable: true
});
Object.defineProperties(Headers.prototype, {
    get: {
        enumerable: true
    },
    forEach: {
        enumerable: true
    },
    set: {
        enumerable: true
    },
    append: {
        enumerable: true
    },
    has: {
        enumerable: true
    },
    delete: {
        enumerable: true
    },
    keys: {
        enumerable: true
    },
    values: {
        enumerable: true
    },
    entries: {
        enumerable: true
    }
});
function getHeaders(headers) {
    let kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key+value';
    const keys = Object.keys(headers[MAP]).sort();
    return keys.map(kind === 'key' ? function(k) {
        return k.toLowerCase();
    } : kind === 'value' ? function(k) {
        return headers[MAP][k].join(', ');
    } : function(k) {
        return [
            k.toLowerCase(),
            headers[MAP][k].join(', ')
        ];
    });
}
const INTERNAL = Symbol('internal');
function createHeadersIterator(target, kind) {
    const iterator = Object.create(HeadersIteratorPrototype);
    iterator[INTERNAL] = {
        target,
        kind,
        index: 0
    };
    return iterator;
}
const HeadersIteratorPrototype = Object.setPrototypeOf({
    next () {
        // istanbul ignore if
        if (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {
            throw new TypeError('Value of `this` is not a HeadersIterator');
        }
        var _INTERNAL = this[INTERNAL];
        const target = _INTERNAL.target, kind = _INTERNAL.kind, index = _INTERNAL.index;
        const values = getHeaders(target, kind);
        const len = values.length;
        if (index >= len) {
            return {
                value: undefined,
                done: true
            };
        }
        this[INTERNAL].index = index + 1;
        return {
            value: values[index],
            done: false
        };
    }
}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));
Object.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {
    value: 'HeadersIterator',
    writable: false,
    enumerable: false,
    configurable: true
});
/**
 * Export the Headers object in a form that Node.js can consume.
 *
 * @param   Headers  headers
 * @return  Object
 */ function exportNodeCompatibleHeaders(headers) {
    const obj = Object.assign({
        __proto__: null
    }, headers[MAP]);
    // http.request() only supports string as Host header. This hack makes
    // specifying custom Host header possible.
    const hostHeaderKey = find(headers[MAP], 'Host');
    if (hostHeaderKey !== undefined) {
        obj[hostHeaderKey] = obj[hostHeaderKey][0];
    }
    return obj;
}
/**
 * Create a Headers object from an object of headers, ignoring those that do
 * not conform to HTTP grammar productions.
 *
 * @param   Object  obj  Object of headers
 * @return  Headers
 */ function createHeadersLenient(obj) {
    const headers = new Headers();
    for (const name of Object.keys(obj)){
        if (invalidTokenRegex.test(name)) {
            continue;
        }
        if (Array.isArray(obj[name])) {
            for (const val of obj[name]){
                if (invalidHeaderCharRegex.test(val)) {
                    continue;
                }
                if (headers[MAP][name] === undefined) {
                    headers[MAP][name] = [
                        val
                    ];
                } else {
                    headers[MAP][name].push(val);
                }
            }
        } else if (!invalidHeaderCharRegex.test(obj[name])) {
            headers[MAP][name] = [
                obj[name]
            ];
        }
    }
    return headers;
}
const INTERNALS$1 = Symbol('Response internals');
// fix an issue where "STATUS_CODES" aren't a named export for node <10
const STATUS_CODES = __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["default"].STATUS_CODES;
/**
 * Response class
 *
 * @param   Stream  body  Readable stream
 * @param   Object  opts  Response options
 * @return  Void
 */ class Response {
    constructor(){
        let body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
        let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        Body.call(this, body, opts);
        const status = opts.status || 200;
        const headers = new Headers(opts.headers);
        if (body != null && !headers.has('Content-Type')) {
            const contentType = extractContentType(body);
            if (contentType) {
                headers.append('Content-Type', contentType);
            }
        }
        this[INTERNALS$1] = {
            url: opts.url,
            status,
            statusText: opts.statusText || STATUS_CODES[status],
            headers,
            counter: opts.counter
        };
    }
    get url() {
        return this[INTERNALS$1].url || '';
    }
    get status() {
        return this[INTERNALS$1].status;
    }
    /**
  * Convenience property representing if the request ended normally
  */ get ok() {
        return this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;
    }
    get redirected() {
        return this[INTERNALS$1].counter > 0;
    }
    get statusText() {
        return this[INTERNALS$1].statusText;
    }
    get headers() {
        return this[INTERNALS$1].headers;
    }
    /**
  * Clone this response
  *
  * @return  Response
  */ clone() {
        return new Response(clone(this), {
            url: this.url,
            status: this.status,
            statusText: this.statusText,
            headers: this.headers,
            ok: this.ok,
            redirected: this.redirected
        });
    }
}
Body.mixIn(Response.prototype);
Object.defineProperties(Response.prototype, {
    url: {
        enumerable: true
    },
    status: {
        enumerable: true
    },
    ok: {
        enumerable: true
    },
    redirected: {
        enumerable: true
    },
    statusText: {
        enumerable: true
    },
    headers: {
        enumerable: true
    },
    clone: {
        enumerable: true
    }
});
Object.defineProperty(Response.prototype, Symbol.toStringTag, {
    value: 'Response',
    writable: false,
    enumerable: false,
    configurable: true
});
const INTERNALS$2 = Symbol('Request internals');
const URL = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].URL || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].URL;
// fix an issue where "format", "parse" aren't a named export for node <10
const parse_url = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].parse;
const format_url = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].format;
/**
 * Wrapper around `new URL` to handle arbitrary URLs
 *
 * @param  {string} urlStr
 * @return {void}
 */ function parseURL(urlStr) {
    /*
 	Check whether the URL is absolute or not
 		Scheme: https://tools.ietf.org/html/rfc3986#section-3.1
 	Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3
 */ if (/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(urlStr)) {
        urlStr = new URL(urlStr).toString();
    }
    // Fallback to old implementation for arbitrary URLs
    return parse_url(urlStr);
}
const streamDestructionSupported = 'destroy' in __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable.prototype;
/**
 * Check if a value is an instance of Request.
 *
 * @param   Mixed   input
 * @return  Boolean
 */ function isRequest(input) {
    return typeof input === 'object' && typeof input[INTERNALS$2] === 'object';
}
function isAbortSignal(signal) {
    const proto = signal && typeof signal === 'object' && Object.getPrototypeOf(signal);
    return !!(proto && proto.constructor.name === 'AbortSignal');
}
/**
 * Request class
 *
 * @param   Mixed   input  Url or Request instance
 * @param   Object  init   Custom options
 * @return  Void
 */ class Request {
    constructor(input){
        let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        let parsedURL;
        // normalize input
        if (!isRequest(input)) {
            if (input && input.href) {
                // in order to support Node.js' Url objects; though WHATWG's URL objects
                // will fall into this branch also (since their `toString()` will return
                // `href` property anyway)
                parsedURL = parseURL(input.href);
            } else {
                // coerce input to a string before attempting to parse
                parsedURL = parseURL(`${input}`);
            }
            input = {};
        } else {
            parsedURL = parseURL(input.url);
        }
        let method = init.method || input.method || 'GET';
        method = method.toUpperCase();
        if ((init.body != null || isRequest(input) && input.body !== null) && (method === 'GET' || method === 'HEAD')) {
            throw new TypeError('Request with GET/HEAD method cannot have body');
        }
        let inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;
        Body.call(this, inputBody, {
            timeout: init.timeout || input.timeout || 0,
            size: init.size || input.size || 0
        });
        const headers = new Headers(init.headers || input.headers || {});
        if (inputBody != null && !headers.has('Content-Type')) {
            const contentType = extractContentType(inputBody);
            if (contentType) {
                headers.append('Content-Type', contentType);
            }
        }
        let signal = isRequest(input) ? input.signal : null;
        if ('signal' in init) signal = init.signal;
        if (signal != null && !isAbortSignal(signal)) {
            throw new TypeError('Expected signal to be an instanceof AbortSignal');
        }
        this[INTERNALS$2] = {
            method,
            redirect: init.redirect || input.redirect || 'follow',
            headers,
            parsedURL,
            signal
        };
        // node-fetch-only options
        this.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;
        this.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;
        this.counter = init.counter || input.counter || 0;
        this.agent = init.agent || input.agent;
    }
    get method() {
        return this[INTERNALS$2].method;
    }
    get url() {
        return format_url(this[INTERNALS$2].parsedURL);
    }
    get headers() {
        return this[INTERNALS$2].headers;
    }
    get redirect() {
        return this[INTERNALS$2].redirect;
    }
    get signal() {
        return this[INTERNALS$2].signal;
    }
    /**
  * Clone this request
  *
  * @return  Request
  */ clone() {
        return new Request(this);
    }
}
Body.mixIn(Request.prototype);
Object.defineProperty(Request.prototype, Symbol.toStringTag, {
    value: 'Request',
    writable: false,
    enumerable: false,
    configurable: true
});
Object.defineProperties(Request.prototype, {
    method: {
        enumerable: true
    },
    url: {
        enumerable: true
    },
    headers: {
        enumerable: true
    },
    redirect: {
        enumerable: true
    },
    clone: {
        enumerable: true
    },
    signal: {
        enumerable: true
    }
});
/**
 * Convert a Request to Node.js http request options.
 *
 * @param   Request  A Request instance
 * @return  Object   The options object to be passed to http.request
 */ function getNodeRequestOptions(request) {
    const parsedURL = request[INTERNALS$2].parsedURL;
    const headers = new Headers(request[INTERNALS$2].headers);
    // fetch step 1.3
    if (!headers.has('Accept')) {
        headers.set('Accept', '*/*');
    }
    // Basic fetch
    if (!parsedURL.protocol || !parsedURL.hostname) {
        throw new TypeError('Only absolute URLs are supported');
    }
    if (!/^https?:$/.test(parsedURL.protocol)) {
        throw new TypeError('Only HTTP(S) protocols are supported');
    }
    if (request.signal && request.body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable && !streamDestructionSupported) {
        throw new Error('Cancellation of streamed requests with AbortSignal is not supported in node < 8');
    }
    // HTTP-network-or-cache fetch steps 2.4-2.7
    let contentLengthValue = null;
    if (request.body == null && /^(POST|PUT)$/i.test(request.method)) {
        contentLengthValue = '0';
    }
    if (request.body != null) {
        const totalBytes = getTotalBytes(request);
        if (typeof totalBytes === 'number') {
            contentLengthValue = String(totalBytes);
        }
    }
    if (contentLengthValue) {
        headers.set('Content-Length', contentLengthValue);
    }
    // HTTP-network-or-cache fetch step 2.11
    if (!headers.has('User-Agent')) {
        headers.set('User-Agent', 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)');
    }
    // HTTP-network-or-cache fetch step 2.15
    if (request.compress && !headers.has('Accept-Encoding')) {
        headers.set('Accept-Encoding', 'gzip,deflate');
    }
    let agent = request.agent;
    if (typeof agent === 'function') {
        agent = agent(parsedURL);
    }
    // HTTP-network fetch step 4.2
    // chunked encoding is handled by Node.js
    return Object.assign({}, parsedURL, {
        method: request.method,
        headers: exportNodeCompatibleHeaders(headers),
        agent
    });
}
/**
 * abort-error.js
 *
 * AbortError interface for cancelled requests
 */ /**
 * Create AbortError instance
 *
 * @param   String      message      Error message for human
 * @return  AbortError
 */ function AbortError(message) {
    Error.call(this, message);
    this.type = 'aborted';
    this.message = message;
    // hide custom error implementation details from end-users
    Error.captureStackTrace(this, this.constructor);
}
AbortError.prototype = Object.create(Error.prototype);
AbortError.prototype.constructor = AbortError;
AbortError.prototype.name = 'AbortError';
const URL$1 = __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["default"].URL || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$url$2f$lib$2f$public$2d$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].URL;
// fix an issue where "PassThrough", "resolve" aren't a named export for node <10
const PassThrough$1 = __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].PassThrough;
const isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {
    const orig = new URL$1(original).hostname;
    const dest = new URL$1(destination).hostname;
    return orig === dest || orig[orig.length - dest.length - 1] === '.' && orig.endsWith(dest);
};
/**
 * isSameProtocol reports whether the two provided URLs use the same protocol.
 *
 * Both domains must already be in canonical form.
 * @param {string|URL} original
 * @param {string|URL} destination
 */ const isSameProtocol = function isSameProtocol(destination, original) {
    const orig = new URL$1(original).protocol;
    const dest = new URL$1(destination).protocol;
    return orig === dest;
};
/**
 * Fetch function
 *
 * @param   Mixed    url   Absolute url or Request instance
 * @param   Object   opts  Fetch options
 * @return  Promise
 */ function fetch(url, opts) {
    // allow custom promise
    if (!fetch.Promise) {
        throw new Error('native promise missing, set fetch.Promise to your favorite alternative');
    }
    Body.Promise = fetch.Promise;
    // wrap http.request into fetch
    return new fetch.Promise(function(resolve, reject) {
        // build request object
        const request = new Request(url, opts);
        const options = getNodeRequestOptions(request);
        const send = (options.protocol === 'https:' ? __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__["default"] : __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["default"]).request;
        const signal = request.signal;
        let response = null;
        const abort = function abort() {
            let error = new AbortError('The user aborted a request.');
            reject(error);
            if (request.body && request.body instanceof __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["default"].Readable) {
                destroyStream(request.body, error);
            }
            if (!response || !response.body) return;
            response.body.emit('error', error);
        };
        if (signal && signal.aborted) {
            abort();
            return;
        }
        const abortAndFinalize = function abortAndFinalize() {
            abort();
            finalize();
        };
        // send request
        const req = send(options);
        let reqTimeout;
        if (signal) {
            signal.addEventListener('abort', abortAndFinalize);
        }
        function finalize() {
            req.abort();
            if (signal) signal.removeEventListener('abort', abortAndFinalize);
            clearTimeout(reqTimeout);
        }
        if (request.timeout) {
            req.once('socket', function(socket) {
                reqTimeout = setTimeout(function() {
                    reject(new FetchError(`network timeout at: ${request.url}`, 'request-timeout'));
                    finalize();
                }, request.timeout);
            });
        }
        req.on('error', function(err) {
            reject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, 'system', err));
            if (response && response.body) {
                destroyStream(response.body, err);
            }
            finalize();
        });
        fixResponseChunkedTransferBadEnding(req, function(err) {
            if (signal && signal.aborted) {
                return;
            }
            if (response && response.body) {
                destroyStream(response.body, err);
            }
        });
        /* c8 ignore next 18 */ if (parseInt(process.version.substring(1)) < 14) {
            // Before Node.js 14, pipeline() does not fully support async iterators and does not always
            // properly handle when the socket close/end events are out of order.
            req.on('socket', function(s) {
                s.addListener('close', function(hadError) {
                    // if a data listener is still present we didn't end cleanly
                    const hasDataListener = s.listenerCount('data') > 0;
                    // if end happened before close but the socket didn't emit an error, do it now
                    if (response && hasDataListener && !hadError && !(signal && signal.aborted)) {
                        const err = new Error('Premature close');
                        err.code = 'ERR_STREAM_PREMATURE_CLOSE';
                        response.body.emit('error', err);
                    }
                });
            });
        }
        req.on('response', function(res) {
            clearTimeout(reqTimeout);
            const headers = createHeadersLenient(res.headers);
            // HTTP fetch step 5
            if (fetch.isRedirect(res.statusCode)) {
                // HTTP fetch step 5.2
                const location = headers.get('Location');
                // HTTP fetch step 5.3
                let locationURL = null;
                try {
                    locationURL = location === null ? null : new URL$1(location, request.url).toString();
                } catch (err) {
                    // error here can only be invalid URL in Location: header
                    // do not throw when options.redirect == manual
                    // let the user extract the errorneous redirect URL
                    if (request.redirect !== 'manual') {
                        reject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, 'invalid-redirect'));
                        finalize();
                        return;
                    }
                }
                // HTTP fetch step 5.5
                switch(request.redirect){
                    case 'error':
                        reject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, 'no-redirect'));
                        finalize();
                        return;
                    case 'manual':
                        // node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.
                        if (locationURL !== null) {
                            // handle corrupted header
                            try {
                                headers.set('Location', locationURL);
                            } catch (err) {
                                // istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request
                                reject(err);
                            }
                        }
                        break;
                    case 'follow':
                        // HTTP-redirect fetch step 2
                        if (locationURL === null) {
                            break;
                        }
                        // HTTP-redirect fetch step 5
                        if (request.counter >= request.follow) {
                            reject(new FetchError(`maximum redirect reached at: ${request.url}`, 'max-redirect'));
                            finalize();
                            return;
                        }
                        // HTTP-redirect fetch step 6 (counter increment)
                        // Create a new Request object.
                        const requestOpts = {
                            headers: new Headers(request.headers),
                            follow: request.follow,
                            counter: request.counter + 1,
                            agent: request.agent,
                            compress: request.compress,
                            method: request.method,
                            body: request.body,
                            signal: request.signal,
                            timeout: request.timeout,
                            size: request.size
                        };
                        if (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {
                            for (const name of [
                                'authorization',
                                'www-authenticate',
                                'cookie',
                                'cookie2'
                            ]){
                                requestOpts.headers.delete(name);
                            }
                        }
                        // HTTP-redirect fetch step 9
                        if (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {
                            reject(new FetchError('Cannot follow redirect with body being a readable stream', 'unsupported-redirect'));
                            finalize();
                            return;
                        }
                        // HTTP-redirect fetch step 11
                        if (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === 'POST') {
                            requestOpts.method = 'GET';
                            requestOpts.body = undefined;
                            requestOpts.headers.delete('content-length');
                        }
                        // HTTP-redirect fetch step 15
                        resolve(fetch(new Request(locationURL, requestOpts)));
                        finalize();
                        return;
                }
            }
            // prepare response
            res.once('end', function() {
                if (signal) signal.removeEventListener('abort', abortAndFinalize);
            });
            let body = res.pipe(new PassThrough$1());
            const response_options = {
                url: request.url,
                status: res.statusCode,
                statusText: res.statusMessage,
                headers: headers,
                size: request.size,
                timeout: request.timeout,
                counter: request.counter
            };
            // HTTP-network fetch step ********
            const codings = headers.get('Content-Encoding');
            // HTTP-network fetch step ********: handle content codings
            // in following scenarios we ignore compression support
            // 1. compression support is disabled
            // 2. HEAD request
            // 3. no Content-Encoding header
            // 4. no content response (204)
            // 5. content not modified response (304)
            if (!request.compress || request.method === 'HEAD' || codings === null || res.statusCode === 204 || res.statusCode === 304) {
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // For Node v6+
            // Be less strict when decoding compressed responses, since sometimes
            // servers send slightly invalid responses that are still accepted
            // by common browsers.
            // Always using Z_SYNC_FLUSH is what cURL does.
            const zlibOptions = {
                flush: __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].Z_SYNC_FLUSH,
                finishFlush: __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].Z_SYNC_FLUSH
            };
            // for gzip
            if (codings == 'gzip' || codings == 'x-gzip') {
                body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createGunzip(zlibOptions));
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // for deflate
            if (codings == 'deflate' || codings == 'x-deflate') {
                // handle the infamous raw deflate response from old servers
                // a hack for old IIS and Apache servers
                const raw = res.pipe(new PassThrough$1());
                raw.once('data', function(chunk) {
                    // see http://stackoverflow.com/questions/37519828
                    if ((chunk[0] & 0x0F) === 0x08) {
                        body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createInflate());
                    } else {
                        body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createInflateRaw());
                    }
                    response = new Response(body, response_options);
                    resolve(response);
                });
                raw.on('end', function() {
                    // some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.
                    if (!response) {
                        response = new Response(body, response_options);
                        resolve(response);
                    }
                });
                return;
            }
            // for br
            if (codings == 'br' && typeof __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createBrotliDecompress === 'function') {
                body = body.pipe(__TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["default"].createBrotliDecompress());
                response = new Response(body, response_options);
                resolve(response);
                return;
            }
            // otherwise, use response as-is
            response = new Response(body, response_options);
            resolve(response);
        });
        writeToStream(req, request);
    });
}
function fixResponseChunkedTransferBadEnding(request, errorCallback) {
    let socket;
    request.on('socket', function(s) {
        socket = s;
    });
    request.on('response', function(response) {
        const headers = response.headers;
        if (headers['transfer-encoding'] === 'chunked' && !headers['content-length']) {
            response.once('close', function(hadError) {
                // tests for socket presence, as in some situations the
                // the 'socket' event is not triggered for the request
                // (happens in deno), avoids `TypeError`
                // if a data listener is still present we didn't end cleanly
                const hasDataListener = socket && socket.listenerCount('data') > 0;
                if (hasDataListener && !hadError) {
                    const err = new Error('Premature close');
                    err.code = 'ERR_STREAM_PREMATURE_CLOSE';
                    errorCallback(err);
                }
            });
        }
    });
}
function destroyStream(stream, err) {
    if (stream.destroy) {
        stream.destroy(err);
    } else {
        // node < 8
        stream.emit('error', err);
        stream.end();
    }
}
/**
 * Redirect code matching
 *
 * @param   Number   code  Status code
 * @return  Boolean
 */ fetch.isRedirect = function(code) {
    return code === 301 || code === 302 || code === 303 || code === 307 || code === 308;
};
// expose Promise
fetch.Promise = global.Promise;
const __TURBOPACK__default__export__ = fetch;
;
}}),
"[project]/node_modules/web-streams-polyfill/dist/ponyfill.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license
 * web-streams-polyfill v4.0.0-beta.3
 * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */ __turbopack_context__.s({
    "ByteLengthQueuingStrategy": (()=>ByteLengthQueuingStrategy),
    "CountQueuingStrategy": (()=>CountQueuingStrategy),
    "ReadableByteStreamController": (()=>ReadableByteStreamController),
    "ReadableStream": (()=>ReadableStream),
    "ReadableStreamBYOBReader": (()=>ReadableStreamBYOBReader),
    "ReadableStreamBYOBRequest": (()=>ReadableStreamBYOBRequest),
    "ReadableStreamDefaultController": (()=>ReadableStreamDefaultController),
    "ReadableStreamDefaultReader": (()=>ReadableStreamDefaultReader),
    "TransformStream": (()=>TransformStream),
    "TransformStreamDefaultController": (()=>TransformStreamDefaultController),
    "WritableStream": (()=>WritableStream),
    "WritableStreamDefaultController": (()=>WritableStreamDefaultController),
    "WritableStreamDefaultWriter": (()=>WritableStreamDefaultWriter)
});
const e = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? Symbol : (e)=>`Symbol(${e})`;
function t() {}
function r(e) {
    return "object" == typeof e && null !== e || "function" == typeof e;
}
const o = t;
function n(e, t) {
    try {
        Object.defineProperty(e, "name", {
            value: t,
            configurable: !0
        });
    } catch (e) {}
}
const a = Promise, i = Promise.prototype.then, l = Promise.resolve.bind(a), s = Promise.reject.bind(a);
function u(e) {
    return new a(e);
}
function c(e) {
    return l(e);
}
function d(e) {
    return s(e);
}
function f(e, t, r) {
    return i.call(e, t, r);
}
function b(e, t, r) {
    f(f(e, t, r), void 0, o);
}
function h(e, t) {
    b(e, t);
}
function _(e, t) {
    b(e, void 0, t);
}
function p(e, t, r) {
    return f(e, t, r);
}
function m(e) {
    f(e, void 0, o);
}
let y = (e)=>{
    if ("function" == typeof queueMicrotask) y = queueMicrotask;
    else {
        const e = c(void 0);
        y = (t)=>f(e, t);
    }
    return y(e);
};
function g(e, t, r) {
    if ("function" != typeof e) throw new TypeError("Argument is not a function");
    return Function.prototype.apply.call(e, t, r);
}
function w(e, t, r) {
    try {
        return c(g(e, t, r));
    } catch (e) {
        return d(e);
    }
}
class S {
    constructor(){
        this._cursor = 0, this._size = 0, this._front = {
            _elements: [],
            _next: void 0
        }, this._back = this._front, this._cursor = 0, this._size = 0;
    }
    get length() {
        return this._size;
    }
    push(e) {
        const t = this._back;
        let r = t;
        16383 === t._elements.length && (r = {
            _elements: [],
            _next: void 0
        }), t._elements.push(e), r !== t && (this._back = r, t._next = r), ++this._size;
    }
    shift() {
        const e = this._front;
        let t = e;
        const r = this._cursor;
        let o = r + 1;
        const n = e._elements, a = n[r];
        return 16384 === o && (t = e._next, o = 0), --this._size, this._cursor = o, e !== t && (this._front = t), n[r] = void 0, a;
    }
    forEach(e) {
        let t = this._cursor, r = this._front, o = r._elements;
        for(; !(t === o.length && void 0 === r._next || t === o.length && (r = r._next, o = r._elements, t = 0, 0 === o.length));)e(o[t]), ++t;
    }
    peek() {
        const e = this._front, t = this._cursor;
        return e._elements[t];
    }
}
const v = e("[[AbortSteps]]"), R = e("[[ErrorSteps]]"), T = e("[[CancelSteps]]"), q = e("[[PullSteps]]"), C = e("[[ReleaseSteps]]");
function E(e, t) {
    e._ownerReadableStream = t, t._reader = e, "readable" === t._state ? O(e) : "closed" === t._state ? function(e) {
        O(e), j(e);
    }(e) : B(e, t._storedError);
}
function P(e, t) {
    return Gt(e._ownerReadableStream, t);
}
function W(e) {
    const t = e._ownerReadableStream;
    "readable" === t._state ? A(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")) : function(e, t) {
        B(e, t);
    }(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")), t._readableStreamController[C](), t._reader = void 0, e._ownerReadableStream = void 0;
}
function k(e) {
    return new TypeError("Cannot " + e + " a stream using a released reader");
}
function O(e) {
    e._closedPromise = u((t, r)=>{
        e._closedPromise_resolve = t, e._closedPromise_reject = r;
    });
}
function B(e, t) {
    O(e), A(e, t);
}
function A(e, t) {
    void 0 !== e._closedPromise_reject && (m(e._closedPromise), e._closedPromise_reject(t), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0);
}
function j(e) {
    void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0);
}
const z = Number.isFinite || function(e) {
    return "number" == typeof e && isFinite(e);
}, L = Math.trunc || function(e) {
    return e < 0 ? Math.ceil(e) : Math.floor(e);
};
function F(e, t) {
    if (void 0 !== e && "object" != typeof (r = e) && "function" != typeof r) throw new TypeError(`${t} is not an object.`);
    var r;
}
function I(e, t) {
    if ("function" != typeof e) throw new TypeError(`${t} is not a function.`);
}
function D(e, t) {
    if (!function(e) {
        return "object" == typeof e && null !== e || "function" == typeof e;
    }(e)) throw new TypeError(`${t} is not an object.`);
}
function $(e, t, r) {
    if (void 0 === e) throw new TypeError(`Parameter ${t} is required in '${r}'.`);
}
function M(e, t, r) {
    if (void 0 === e) throw new TypeError(`${t} is required in '${r}'.`);
}
function Y(e) {
    return Number(e);
}
function Q(e) {
    return 0 === e ? 0 : e;
}
function N(e, t) {
    const r = Number.MAX_SAFE_INTEGER;
    let o = Number(e);
    if (o = Q(o), !z(o)) throw new TypeError(`${t} is not a finite number`);
    if (o = function(e) {
        return Q(L(e));
    }(o), o < 0 || o > r) throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);
    return z(o) && 0 !== o ? o : 0;
}
function H(e) {
    if (!r(e)) return !1;
    if ("function" != typeof e.getReader) return !1;
    try {
        return "boolean" == typeof e.locked;
    } catch (e) {
        return !1;
    }
}
function x(e) {
    if (!r(e)) return !1;
    if ("function" != typeof e.getWriter) return !1;
    try {
        return "boolean" == typeof e.locked;
    } catch (e) {
        return !1;
    }
}
function V(e, t) {
    if (!Vt(e)) throw new TypeError(`${t} is not a ReadableStream.`);
}
function U(e, t) {
    e._reader._readRequests.push(t);
}
function G(e, t, r) {
    const o = e._reader._readRequests.shift();
    r ? o._closeSteps() : o._chunkSteps(t);
}
function X(e) {
    return e._reader._readRequests.length;
}
function J(e) {
    const t = e._reader;
    return void 0 !== t && !!K(t);
}
class ReadableStreamDefaultReader {
    constructor(e){
        if ($(e, 1, "ReadableStreamDefaultReader"), V(e, "First parameter"), Ut(e)) throw new TypeError("This stream has already been locked for exclusive reading by another reader");
        E(this, e), this._readRequests = new S;
    }
    get closed() {
        return K(this) ? this._closedPromise : d(ee("closed"));
    }
    cancel(e) {
        return K(this) ? void 0 === this._ownerReadableStream ? d(k("cancel")) : P(this, e) : d(ee("cancel"));
    }
    read() {
        if (!K(this)) return d(ee("read"));
        if (void 0 === this._ownerReadableStream) return d(k("read from"));
        let e, t;
        const r = u((r, o)=>{
            e = r, t = o;
        });
        return function(e, t) {
            const r = e._ownerReadableStream;
            r._disturbed = !0, "closed" === r._state ? t._closeSteps() : "errored" === r._state ? t._errorSteps(r._storedError) : r._readableStreamController[q](t);
        }(this, {
            _chunkSteps: (t)=>e({
                    value: t,
                    done: !1
                }),
            _closeSteps: ()=>e({
                    value: void 0,
                    done: !0
                }),
            _errorSteps: (e)=>t(e)
        }), r;
    }
    releaseLock() {
        if (!K(this)) throw ee("releaseLock");
        void 0 !== this._ownerReadableStream && function(e) {
            W(e);
            const t = new TypeError("Reader was released");
            Z(e, t);
        }(this);
    }
}
function K(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_readRequests") && e instanceof ReadableStreamDefaultReader;
}
function Z(e, t) {
    const r = e._readRequests;
    e._readRequests = new S, r.forEach((e)=>{
        e._errorSteps(t);
    });
}
function ee(e) {
    return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`);
}
Object.defineProperties(ReadableStreamDefaultReader.prototype, {
    cancel: {
        enumerable: !0
    },
    read: {
        enumerable: !0
    },
    releaseLock: {
        enumerable: !0
    },
    closed: {
        enumerable: !0
    }
}), n(ReadableStreamDefaultReader.prototype.cancel, "cancel"), n(ReadableStreamDefaultReader.prototype.read, "read"), n(ReadableStreamDefaultReader.prototype.releaseLock, "releaseLock"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableStreamDefaultReader.prototype, e.toStringTag, {
    value: "ReadableStreamDefaultReader",
    configurable: !0
});
class te {
    constructor(e, t){
        this._ongoingPromise = void 0, this._isFinished = !1, this._reader = e, this._preventCancel = t;
    }
    next() {
        const e = ()=>this._nextSteps();
        return this._ongoingPromise = this._ongoingPromise ? p(this._ongoingPromise, e, e) : e(), this._ongoingPromise;
    }
    return(e) {
        const t = ()=>this._returnSteps(e);
        return this._ongoingPromise ? p(this._ongoingPromise, t, t) : t();
    }
    _nextSteps() {
        if (this._isFinished) return Promise.resolve({
            value: void 0,
            done: !0
        });
        const e = this._reader;
        return void 0 === e ? d(k("iterate")) : f(e.read(), (e)=>{
            var t;
            return this._ongoingPromise = void 0, e.done && (this._isFinished = !0, null === (t = this._reader) || void 0 === t || t.releaseLock(), this._reader = void 0), e;
        }, (e)=>{
            var t;
            throw this._ongoingPromise = void 0, this._isFinished = !0, null === (t = this._reader) || void 0 === t || t.releaseLock(), this._reader = void 0, e;
        });
    }
    _returnSteps(e) {
        if (this._isFinished) return Promise.resolve({
            value: e,
            done: !0
        });
        this._isFinished = !0;
        const t = this._reader;
        if (void 0 === t) return d(k("finish iterating"));
        if (this._reader = void 0, !this._preventCancel) {
            const r = t.cancel(e);
            return t.releaseLock(), p(r, ()=>({
                    value: e,
                    done: !0
                }));
        }
        return t.releaseLock(), c({
            value: e,
            done: !0
        });
    }
}
const re = {
    next () {
        return oe(this) ? this._asyncIteratorImpl.next() : d(ne("next"));
    },
    return (e) {
        return oe(this) ? this._asyncIteratorImpl.return(e) : d(ne("return"));
    }
};
function oe(e) {
    if (!r(e)) return !1;
    if (!Object.prototype.hasOwnProperty.call(e, "_asyncIteratorImpl")) return !1;
    try {
        return e._asyncIteratorImpl instanceof te;
    } catch (e) {
        return !1;
    }
}
function ne(e) {
    return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`);
}
"symbol" == typeof e.asyncIterator && Object.defineProperty(re, e.asyncIterator, {
    value () {
        return this;
    },
    writable: !0,
    configurable: !0
});
const ae = Number.isNaN || function(e) {
    return e != e;
};
function ie(e, t, r, o, n) {
    new Uint8Array(e).set(new Uint8Array(r, o, n), t);
}
function le(e) {
    const t = function(e, t, r) {
        if (e.slice) return e.slice(t, r);
        const o = r - t, n = new ArrayBuffer(o);
        return ie(n, 0, e, t, o), n;
    }(e.buffer, e.byteOffset, e.byteOffset + e.byteLength);
    return new Uint8Array(t);
}
function se(e) {
    const t = e._queue.shift();
    return e._queueTotalSize -= t.size, e._queueTotalSize < 0 && (e._queueTotalSize = 0), t.value;
}
function ue(e, t, r) {
    if ("number" != typeof (o = r) || ae(o) || o < 0 || r === 1 / 0) throw new RangeError("Size must be a finite, non-NaN, non-negative number.");
    var o;
    e._queue.push({
        value: t,
        size: r
    }), e._queueTotalSize += r;
}
function ce(e) {
    e._queue = new S, e._queueTotalSize = 0;
}
class ReadableStreamBYOBRequest {
    constructor(){
        throw new TypeError("Illegal constructor");
    }
    get view() {
        if (!fe(this)) throw Be("view");
        return this._view;
    }
    respond(e) {
        if (!fe(this)) throw Be("respond");
        if ($(e, 1, "respond"), e = N(e, "First parameter"), void 0 === this._associatedReadableByteStreamController) throw new TypeError("This BYOB request has been invalidated");
        this._view.buffer, function(e, t) {
            const r = e._pendingPullIntos.peek();
            if ("closed" === e._controlledReadableByteStream._state) {
                if (0 !== t) throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");
            } else {
                if (0 === t) throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");
                if (r.bytesFilled + t > r.byteLength) throw new RangeError("bytesWritten out of range");
            }
            r.buffer = r.buffer, qe(e, t);
        }(this._associatedReadableByteStreamController, e);
    }
    respondWithNewView(e) {
        if (!fe(this)) throw Be("respondWithNewView");
        if ($(e, 1, "respondWithNewView"), !ArrayBuffer.isView(e)) throw new TypeError("You can only respond with array buffer views");
        if (void 0 === this._associatedReadableByteStreamController) throw new TypeError("This BYOB request has been invalidated");
        e.buffer, function(e, t) {
            const r = e._pendingPullIntos.peek();
            if ("closed" === e._controlledReadableByteStream._state) {
                if (0 !== t.byteLength) throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream");
            } else if (0 === t.byteLength) throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");
            if (r.byteOffset + r.bytesFilled !== t.byteOffset) throw new RangeError("The region specified by view does not match byobRequest");
            if (r.bufferByteLength !== t.buffer.byteLength) throw new RangeError("The buffer of view has different capacity than byobRequest");
            if (r.bytesFilled + t.byteLength > r.byteLength) throw new RangeError("The region specified by view is larger than byobRequest");
            const o = t.byteLength;
            r.buffer = t.buffer, qe(e, o);
        }(this._associatedReadableByteStreamController, e);
    }
}
Object.defineProperties(ReadableStreamBYOBRequest.prototype, {
    respond: {
        enumerable: !0
    },
    respondWithNewView: {
        enumerable: !0
    },
    view: {
        enumerable: !0
    }
}), n(ReadableStreamBYOBRequest.prototype.respond, "respond"), n(ReadableStreamBYOBRequest.prototype.respondWithNewView, "respondWithNewView"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableStreamBYOBRequest.prototype, e.toStringTag, {
    value: "ReadableStreamBYOBRequest",
    configurable: !0
});
class ReadableByteStreamController {
    constructor(){
        throw new TypeError("Illegal constructor");
    }
    get byobRequest() {
        if (!de(this)) throw Ae("byobRequest");
        return function(e) {
            if (null === e._byobRequest && e._pendingPullIntos.length > 0) {
                const t = e._pendingPullIntos.peek(), r = new Uint8Array(t.buffer, t.byteOffset + t.bytesFilled, t.byteLength - t.bytesFilled), o = Object.create(ReadableStreamBYOBRequest.prototype);
                !function(e, t, r) {
                    e._associatedReadableByteStreamController = t, e._view = r;
                }(o, e, r), e._byobRequest = o;
            }
            return e._byobRequest;
        }(this);
    }
    get desiredSize() {
        if (!de(this)) throw Ae("desiredSize");
        return ke(this);
    }
    close() {
        if (!de(this)) throw Ae("close");
        if (this._closeRequested) throw new TypeError("The stream has already been closed; do not close it again!");
        const e = this._controlledReadableByteStream._state;
        if ("readable" !== e) throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);
        !function(e) {
            const t = e._controlledReadableByteStream;
            if (e._closeRequested || "readable" !== t._state) return;
            if (e._queueTotalSize > 0) return void (e._closeRequested = !0);
            if (e._pendingPullIntos.length > 0) {
                if (e._pendingPullIntos.peek().bytesFilled > 0) {
                    const t = new TypeError("Insufficient bytes to fill elements in the given buffer");
                    throw Pe(e, t), t;
                }
            }
            Ee(e), Xt(t);
        }(this);
    }
    enqueue(e) {
        if (!de(this)) throw Ae("enqueue");
        if ($(e, 1, "enqueue"), !ArrayBuffer.isView(e)) throw new TypeError("chunk must be an array buffer view");
        if (0 === e.byteLength) throw new TypeError("chunk must have non-zero byteLength");
        if (0 === e.buffer.byteLength) throw new TypeError("chunk's buffer must have non-zero byteLength");
        if (this._closeRequested) throw new TypeError("stream is closed or draining");
        const t = this._controlledReadableByteStream._state;
        if ("readable" !== t) throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);
        !function(e, t) {
            const r = e._controlledReadableByteStream;
            if (e._closeRequested || "readable" !== r._state) return;
            const o = t.buffer, n = t.byteOffset, a = t.byteLength, i = o;
            if (e._pendingPullIntos.length > 0) {
                const t = e._pendingPullIntos.peek();
                t.buffer, Re(e), t.buffer = t.buffer, "none" === t.readerType && ge(e, t);
            }
            if (J(r)) if (function(e) {
                const t = e._controlledReadableByteStream._reader;
                for(; t._readRequests.length > 0;){
                    if (0 === e._queueTotalSize) return;
                    We(e, t._readRequests.shift());
                }
            }(e), 0 === X(r)) me(e, i, n, a);
            else {
                e._pendingPullIntos.length > 0 && Ce(e);
                G(r, new Uint8Array(i, n, a), !1);
            }
            else Le(r) ? (me(e, i, n, a), Te(e)) : me(e, i, n, a);
            be(e);
        }(this, e);
    }
    error(e) {
        if (!de(this)) throw Ae("error");
        Pe(this, e);
    }
    [T](e) {
        he(this), ce(this);
        const t = this._cancelAlgorithm(e);
        return Ee(this), t;
    }
    [q](e) {
        const t = this._controlledReadableByteStream;
        if (this._queueTotalSize > 0) return void We(this, e);
        const r = this._autoAllocateChunkSize;
        if (void 0 !== r) {
            let t;
            try {
                t = new ArrayBuffer(r);
            } catch (t) {
                return void e._errorSteps(t);
            }
            const o = {
                buffer: t,
                bufferByteLength: r,
                byteOffset: 0,
                byteLength: r,
                bytesFilled: 0,
                elementSize: 1,
                viewConstructor: Uint8Array,
                readerType: "default"
            };
            this._pendingPullIntos.push(o);
        }
        U(t, e), be(this);
    }
    [C]() {
        if (this._pendingPullIntos.length > 0) {
            const e = this._pendingPullIntos.peek();
            e.readerType = "none", this._pendingPullIntos = new S, this._pendingPullIntos.push(e);
        }
    }
}
function de(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_controlledReadableByteStream") && e instanceof ReadableByteStreamController;
}
function fe(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_associatedReadableByteStreamController") && e instanceof ReadableStreamBYOBRequest;
}
function be(e) {
    const t = function(e) {
        const t = e._controlledReadableByteStream;
        if ("readable" !== t._state) return !1;
        if (e._closeRequested) return !1;
        if (!e._started) return !1;
        if (J(t) && X(t) > 0) return !0;
        if (Le(t) && ze(t) > 0) return !0;
        if (ke(e) > 0) return !0;
        return !1;
    }(e);
    if (!t) return;
    if (e._pulling) return void (e._pullAgain = !0);
    e._pulling = !0;
    b(e._pullAlgorithm(), ()=>(e._pulling = !1, e._pullAgain && (e._pullAgain = !1, be(e)), null), (t)=>(Pe(e, t), null));
}
function he(e) {
    Re(e), e._pendingPullIntos = new S;
}
function _e(e, t) {
    let r = !1;
    "closed" === e._state && (r = !0);
    const o = pe(t);
    "default" === t.readerType ? G(e, o, r) : function(e, t, r) {
        const o = e._reader._readIntoRequests.shift();
        r ? o._closeSteps(t) : o._chunkSteps(t);
    }(e, o, r);
}
function pe(e) {
    const t = e.bytesFilled, r = e.elementSize;
    return new e.viewConstructor(e.buffer, e.byteOffset, t / r);
}
function me(e, t, r, o) {
    e._queue.push({
        buffer: t,
        byteOffset: r,
        byteLength: o
    }), e._queueTotalSize += o;
}
function ye(e, t, r, o) {
    let n;
    try {
        n = t.slice(r, r + o);
    } catch (t) {
        throw Pe(e, t), t;
    }
    me(e, n, 0, o);
}
function ge(e, t) {
    t.bytesFilled > 0 && ye(e, t.buffer, t.byteOffset, t.bytesFilled), Ce(e);
}
function we(e, t) {
    const r = t.elementSize, o = t.bytesFilled - t.bytesFilled % r, n = Math.min(e._queueTotalSize, t.byteLength - t.bytesFilled), a = t.bytesFilled + n, i = a - a % r;
    let l = n, s = !1;
    i > o && (l = i - t.bytesFilled, s = !0);
    const u = e._queue;
    for(; l > 0;){
        const r = u.peek(), o = Math.min(l, r.byteLength), n = t.byteOffset + t.bytesFilled;
        ie(t.buffer, n, r.buffer, r.byteOffset, o), r.byteLength === o ? u.shift() : (r.byteOffset += o, r.byteLength -= o), e._queueTotalSize -= o, Se(e, o, t), l -= o;
    }
    return s;
}
function Se(e, t, r) {
    r.bytesFilled += t;
}
function ve(e) {
    0 === e._queueTotalSize && e._closeRequested ? (Ee(e), Xt(e._controlledReadableByteStream)) : be(e);
}
function Re(e) {
    null !== e._byobRequest && (e._byobRequest._associatedReadableByteStreamController = void 0, e._byobRequest._view = null, e._byobRequest = null);
}
function Te(e) {
    for(; e._pendingPullIntos.length > 0;){
        if (0 === e._queueTotalSize) return;
        const t = e._pendingPullIntos.peek();
        we(e, t) && (Ce(e), _e(e._controlledReadableByteStream, t));
    }
}
function qe(e, t) {
    const r = e._pendingPullIntos.peek();
    Re(e);
    "closed" === e._controlledReadableByteStream._state ? function(e, t) {
        "none" === t.readerType && Ce(e);
        const r = e._controlledReadableByteStream;
        if (Le(r)) for(; ze(r) > 0;)_e(r, Ce(e));
    }(e, r) : function(e, t, r) {
        if (Se(0, t, r), "none" === r.readerType) return ge(e, r), void Te(e);
        if (r.bytesFilled < r.elementSize) return;
        Ce(e);
        const o = r.bytesFilled % r.elementSize;
        if (o > 0) {
            const t = r.byteOffset + r.bytesFilled;
            ye(e, r.buffer, t - o, o);
        }
        r.bytesFilled -= o, _e(e._controlledReadableByteStream, r), Te(e);
    }(e, t, r), be(e);
}
function Ce(e) {
    return e._pendingPullIntos.shift();
}
function Ee(e) {
    e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0;
}
function Pe(e, t) {
    const r = e._controlledReadableByteStream;
    "readable" === r._state && (he(e), ce(e), Ee(e), Jt(r, t));
}
function We(e, t) {
    const r = e._queue.shift();
    e._queueTotalSize -= r.byteLength, ve(e);
    const o = new Uint8Array(r.buffer, r.byteOffset, r.byteLength);
    t._chunkSteps(o);
}
function ke(e) {
    const t = e._controlledReadableByteStream._state;
    return "errored" === t ? null : "closed" === t ? 0 : e._strategyHWM - e._queueTotalSize;
}
function Oe(e, t, r) {
    const o = Object.create(ReadableByteStreamController.prototype);
    let n, a, i;
    n = void 0 !== t.start ? ()=>t.start(o) : ()=>{}, a = void 0 !== t.pull ? ()=>t.pull(o) : ()=>c(void 0), i = void 0 !== t.cancel ? (e)=>t.cancel(e) : ()=>c(void 0);
    const l = t.autoAllocateChunkSize;
    if (0 === l) throw new TypeError("autoAllocateChunkSize must be greater than 0");
    !function(e, t, r, o, n, a, i) {
        t._controlledReadableByteStream = e, t._pullAgain = !1, t._pulling = !1, t._byobRequest = null, t._queue = t._queueTotalSize = void 0, ce(t), t._closeRequested = !1, t._started = !1, t._strategyHWM = a, t._pullAlgorithm = o, t._cancelAlgorithm = n, t._autoAllocateChunkSize = i, t._pendingPullIntos = new S, e._readableStreamController = t, b(c(r()), ()=>(t._started = !0, be(t), null), (e)=>(Pe(t, e), null));
    }(e, o, n, a, i, r, l);
}
function Be(e) {
    return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`);
}
function Ae(e) {
    return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`);
}
function je(e, t) {
    e._reader._readIntoRequests.push(t);
}
function ze(e) {
    return e._reader._readIntoRequests.length;
}
function Le(e) {
    const t = e._reader;
    return void 0 !== t && !!Fe(t);
}
Object.defineProperties(ReadableByteStreamController.prototype, {
    close: {
        enumerable: !0
    },
    enqueue: {
        enumerable: !0
    },
    error: {
        enumerable: !0
    },
    byobRequest: {
        enumerable: !0
    },
    desiredSize: {
        enumerable: !0
    }
}), n(ReadableByteStreamController.prototype.close, "close"), n(ReadableByteStreamController.prototype.enqueue, "enqueue"), n(ReadableByteStreamController.prototype.error, "error"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableByteStreamController.prototype, e.toStringTag, {
    value: "ReadableByteStreamController",
    configurable: !0
});
class ReadableStreamBYOBReader {
    constructor(e){
        if ($(e, 1, "ReadableStreamBYOBReader"), V(e, "First parameter"), Ut(e)) throw new TypeError("This stream has already been locked for exclusive reading by another reader");
        if (!de(e._readableStreamController)) throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");
        E(this, e), this._readIntoRequests = new S;
    }
    get closed() {
        return Fe(this) ? this._closedPromise : d(De("closed"));
    }
    cancel(e) {
        return Fe(this) ? void 0 === this._ownerReadableStream ? d(k("cancel")) : P(this, e) : d(De("cancel"));
    }
    read(e) {
        if (!Fe(this)) return d(De("read"));
        if (!ArrayBuffer.isView(e)) return d(new TypeError("view must be an array buffer view"));
        if (0 === e.byteLength) return d(new TypeError("view must have non-zero byteLength"));
        if (0 === e.buffer.byteLength) return d(new TypeError("view's buffer must have non-zero byteLength"));
        if (e.buffer, void 0 === this._ownerReadableStream) return d(k("read from"));
        let t, r;
        const o = u((e, o)=>{
            t = e, r = o;
        });
        return function(e, t, r) {
            const o = e._ownerReadableStream;
            o._disturbed = !0, "errored" === o._state ? r._errorSteps(o._storedError) : function(e, t, r) {
                const o = e._controlledReadableByteStream;
                let n = 1;
                t.constructor !== DataView && (n = t.constructor.BYTES_PER_ELEMENT);
                const a = t.constructor, i = t.buffer, l = {
                    buffer: i,
                    bufferByteLength: i.byteLength,
                    byteOffset: t.byteOffset,
                    byteLength: t.byteLength,
                    bytesFilled: 0,
                    elementSize: n,
                    viewConstructor: a,
                    readerType: "byob"
                };
                if (e._pendingPullIntos.length > 0) return e._pendingPullIntos.push(l), void je(o, r);
                if ("closed" !== o._state) {
                    if (e._queueTotalSize > 0) {
                        if (we(e, l)) {
                            const t = pe(l);
                            return ve(e), void r._chunkSteps(t);
                        }
                        if (e._closeRequested) {
                            const t = new TypeError("Insufficient bytes to fill elements in the given buffer");
                            return Pe(e, t), void r._errorSteps(t);
                        }
                    }
                    e._pendingPullIntos.push(l), je(o, r), be(e);
                } else {
                    const e = new a(l.buffer, l.byteOffset, 0);
                    r._closeSteps(e);
                }
            }(o._readableStreamController, t, r);
        }(this, e, {
            _chunkSteps: (e)=>t({
                    value: e,
                    done: !1
                }),
            _closeSteps: (e)=>t({
                    value: e,
                    done: !0
                }),
            _errorSteps: (e)=>r(e)
        }), o;
    }
    releaseLock() {
        if (!Fe(this)) throw De("releaseLock");
        void 0 !== this._ownerReadableStream && function(e) {
            W(e);
            const t = new TypeError("Reader was released");
            Ie(e, t);
        }(this);
    }
}
function Fe(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_readIntoRequests") && e instanceof ReadableStreamBYOBReader;
}
function Ie(e, t) {
    const r = e._readIntoRequests;
    e._readIntoRequests = new S, r.forEach((e)=>{
        e._errorSteps(t);
    });
}
function De(e) {
    return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`);
}
function $e(e, t) {
    const { highWaterMark: r } = e;
    if (void 0 === r) return t;
    if (ae(r) || r < 0) throw new RangeError("Invalid highWaterMark");
    return r;
}
function Me(e) {
    const { size: t } = e;
    return t || (()=>1);
}
function Ye(e, t) {
    F(e, t);
    const r = null == e ? void 0 : e.highWaterMark, o = null == e ? void 0 : e.size;
    return {
        highWaterMark: void 0 === r ? void 0 : Y(r),
        size: void 0 === o ? void 0 : Qe(o, `${t} has member 'size' that`)
    };
}
function Qe(e, t) {
    return I(e, t), (t)=>Y(e(t));
}
function Ne(e, t, r) {
    return I(e, r), (r)=>w(e, t, [
            r
        ]);
}
function He(e, t, r) {
    return I(e, r), ()=>w(e, t, []);
}
function xe(e, t, r) {
    return I(e, r), (r)=>g(e, t, [
            r
        ]);
}
function Ve(e, t, r) {
    return I(e, r), (r, o)=>w(e, t, [
            r,
            o
        ]);
}
Object.defineProperties(ReadableStreamBYOBReader.prototype, {
    cancel: {
        enumerable: !0
    },
    read: {
        enumerable: !0
    },
    releaseLock: {
        enumerable: !0
    },
    closed: {
        enumerable: !0
    }
}), n(ReadableStreamBYOBReader.prototype.cancel, "cancel"), n(ReadableStreamBYOBReader.prototype.read, "read"), n(ReadableStreamBYOBReader.prototype.releaseLock, "releaseLock"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableStreamBYOBReader.prototype, e.toStringTag, {
    value: "ReadableStreamBYOBReader",
    configurable: !0
});
const Ue = "function" == typeof AbortController;
class WritableStream {
    constructor(e = {}, t = {}){
        void 0 === e ? e = null : D(e, "First parameter");
        const r = Ye(t, "Second parameter"), o = function(e, t) {
            F(e, t);
            const r = null == e ? void 0 : e.abort, o = null == e ? void 0 : e.close, n = null == e ? void 0 : e.start, a = null == e ? void 0 : e.type, i = null == e ? void 0 : e.write;
            return {
                abort: void 0 === r ? void 0 : Ne(r, e, `${t} has member 'abort' that`),
                close: void 0 === o ? void 0 : He(o, e, `${t} has member 'close' that`),
                start: void 0 === n ? void 0 : xe(n, e, `${t} has member 'start' that`),
                write: void 0 === i ? void 0 : Ve(i, e, `${t} has member 'write' that`),
                type: a
            };
        }(e, "First parameter");
        var n;
        (n = this)._state = "writable", n._storedError = void 0, n._writer = void 0, n._writableStreamController = void 0, n._writeRequests = new S, n._inFlightWriteRequest = void 0, n._closeRequest = void 0, n._inFlightCloseRequest = void 0, n._pendingAbortRequest = void 0, n._backpressure = !1;
        if (void 0 !== o.type) throw new RangeError("Invalid type is specified");
        const a = Me(r);
        !function(e, t, r, o) {
            const n = Object.create(WritableStreamDefaultController.prototype);
            let a, i, l, s;
            a = void 0 !== t.start ? ()=>t.start(n) : ()=>{};
            i = void 0 !== t.write ? (e)=>t.write(e, n) : ()=>c(void 0);
            l = void 0 !== t.close ? ()=>t.close() : ()=>c(void 0);
            s = void 0 !== t.abort ? (e)=>t.abort(e) : ()=>c(void 0);
            !function(e, t, r, o, n, a, i, l) {
                t._controlledWritableStream = e, e._writableStreamController = t, t._queue = void 0, t._queueTotalSize = void 0, ce(t), t._abortReason = void 0, t._abortController = function() {
                    if (Ue) return new AbortController;
                }(), t._started = !1, t._strategySizeAlgorithm = l, t._strategyHWM = i, t._writeAlgorithm = o, t._closeAlgorithm = n, t._abortAlgorithm = a;
                const s = bt(t);
                nt(e, s);
                const u = r();
                b(c(u), ()=>(t._started = !0, dt(t), null), (r)=>(t._started = !0, Ze(e, r), null));
            }(e, n, a, i, l, s, r, o);
        }(this, o, $e(r, 1), a);
    }
    get locked() {
        if (!Ge(this)) throw _t("locked");
        return Xe(this);
    }
    abort(e) {
        return Ge(this) ? Xe(this) ? d(new TypeError("Cannot abort a stream that already has a writer")) : Je(this, e) : d(_t("abort"));
    }
    close() {
        return Ge(this) ? Xe(this) ? d(new TypeError("Cannot close a stream that already has a writer")) : rt(this) ? d(new TypeError("Cannot close an already-closing stream")) : Ke(this) : d(_t("close"));
    }
    getWriter() {
        if (!Ge(this)) throw _t("getWriter");
        return new WritableStreamDefaultWriter(this);
    }
}
function Ge(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_writableStreamController") && e instanceof WritableStream;
}
function Xe(e) {
    return void 0 !== e._writer;
}
function Je(e, t) {
    var r;
    if ("closed" === e._state || "errored" === e._state) return c(void 0);
    e._writableStreamController._abortReason = t, null === (r = e._writableStreamController._abortController) || void 0 === r || r.abort(t);
    const o = e._state;
    if ("closed" === o || "errored" === o) return c(void 0);
    if (void 0 !== e._pendingAbortRequest) return e._pendingAbortRequest._promise;
    let n = !1;
    "erroring" === o && (n = !0, t = void 0);
    const a = u((r, o)=>{
        e._pendingAbortRequest = {
            _promise: void 0,
            _resolve: r,
            _reject: o,
            _reason: t,
            _wasAlreadyErroring: n
        };
    });
    return e._pendingAbortRequest._promise = a, n || et(e, t), a;
}
function Ke(e) {
    const t = e._state;
    if ("closed" === t || "errored" === t) return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));
    const r = u((t, r)=>{
        const o = {
            _resolve: t,
            _reject: r
        };
        e._closeRequest = o;
    }), o = e._writer;
    var n;
    return void 0 !== o && e._backpressure && "writable" === t && Et(o), ue(n = e._writableStreamController, lt, 0), dt(n), r;
}
function Ze(e, t) {
    "writable" !== e._state ? tt(e) : et(e, t);
}
function et(e, t) {
    const r = e._writableStreamController;
    e._state = "erroring", e._storedError = t;
    const o = e._writer;
    void 0 !== o && it(o, t), !function(e) {
        if (void 0 === e._inFlightWriteRequest && void 0 === e._inFlightCloseRequest) return !1;
        return !0;
    }(e) && r._started && tt(e);
}
function tt(e) {
    e._state = "errored", e._writableStreamController[R]();
    const t = e._storedError;
    if (e._writeRequests.forEach((e)=>{
        e._reject(t);
    }), e._writeRequests = new S, void 0 === e._pendingAbortRequest) return void ot(e);
    const r = e._pendingAbortRequest;
    if (e._pendingAbortRequest = void 0, r._wasAlreadyErroring) return r._reject(t), void ot(e);
    b(e._writableStreamController[v](r._reason), ()=>(r._resolve(), ot(e), null), (t)=>(r._reject(t), ot(e), null));
}
function rt(e) {
    return void 0 !== e._closeRequest || void 0 !== e._inFlightCloseRequest;
}
function ot(e) {
    void 0 !== e._closeRequest && (e._closeRequest._reject(e._storedError), e._closeRequest = void 0);
    const t = e._writer;
    void 0 !== t && St(t, e._storedError);
}
function nt(e, t) {
    const r = e._writer;
    void 0 !== r && t !== e._backpressure && (t ? function(e) {
        Rt(e);
    }(r) : Et(r)), e._backpressure = t;
}
Object.defineProperties(WritableStream.prototype, {
    abort: {
        enumerable: !0
    },
    close: {
        enumerable: !0
    },
    getWriter: {
        enumerable: !0
    },
    locked: {
        enumerable: !0
    }
}), n(WritableStream.prototype.abort, "abort"), n(WritableStream.prototype.close, "close"), n(WritableStream.prototype.getWriter, "getWriter"), "symbol" == typeof e.toStringTag && Object.defineProperty(WritableStream.prototype, e.toStringTag, {
    value: "WritableStream",
    configurable: !0
});
class WritableStreamDefaultWriter {
    constructor(e){
        if ($(e, 1, "WritableStreamDefaultWriter"), function(e, t) {
            if (!Ge(e)) throw new TypeError(`${t} is not a WritableStream.`);
        }(e, "First parameter"), Xe(e)) throw new TypeError("This stream has already been locked for exclusive writing by another writer");
        this._ownerWritableStream = e, e._writer = this;
        const t = e._state;
        if ("writable" === t) !rt(e) && e._backpressure ? Rt(this) : qt(this), gt(this);
        else if ("erroring" === t) Tt(this, e._storedError), gt(this);
        else if ("closed" === t) qt(this), gt(r = this), vt(r);
        else {
            const t = e._storedError;
            Tt(this, t), wt(this, t);
        }
        var r;
    }
    get closed() {
        return at(this) ? this._closedPromise : d(mt("closed"));
    }
    get desiredSize() {
        if (!at(this)) throw mt("desiredSize");
        if (void 0 === this._ownerWritableStream) throw yt("desiredSize");
        return function(e) {
            const t = e._ownerWritableStream, r = t._state;
            if ("errored" === r || "erroring" === r) return null;
            if ("closed" === r) return 0;
            return ct(t._writableStreamController);
        }(this);
    }
    get ready() {
        return at(this) ? this._readyPromise : d(mt("ready"));
    }
    abort(e) {
        return at(this) ? void 0 === this._ownerWritableStream ? d(yt("abort")) : function(e, t) {
            return Je(e._ownerWritableStream, t);
        }(this, e) : d(mt("abort"));
    }
    close() {
        if (!at(this)) return d(mt("close"));
        const e = this._ownerWritableStream;
        return void 0 === e ? d(yt("close")) : rt(e) ? d(new TypeError("Cannot close an already-closing stream")) : Ke(this._ownerWritableStream);
    }
    releaseLock() {
        if (!at(this)) throw mt("releaseLock");
        void 0 !== this._ownerWritableStream && function(e) {
            const t = e._ownerWritableStream, r = new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");
            it(e, r), function(e, t) {
                "pending" === e._closedPromiseState ? St(e, t) : function(e, t) {
                    wt(e, t);
                }(e, t);
            }(e, r), t._writer = void 0, e._ownerWritableStream = void 0;
        }(this);
    }
    write(e) {
        return at(this) ? void 0 === this._ownerWritableStream ? d(yt("write to")) : function(e, t) {
            const r = e._ownerWritableStream, o = r._writableStreamController, n = function(e, t) {
                try {
                    return e._strategySizeAlgorithm(t);
                } catch (t) {
                    return ft(e, t), 1;
                }
            }(o, t);
            if (r !== e._ownerWritableStream) return d(yt("write to"));
            const a = r._state;
            if ("errored" === a) return d(r._storedError);
            if (rt(r) || "closed" === a) return d(new TypeError("The stream is closing or closed and cannot be written to"));
            if ("erroring" === a) return d(r._storedError);
            const i = function(e) {
                return u((t, r)=>{
                    const o = {
                        _resolve: t,
                        _reject: r
                    };
                    e._writeRequests.push(o);
                });
            }(r);
            return function(e, t, r) {
                try {
                    ue(e, t, r);
                } catch (t) {
                    return void ft(e, t);
                }
                const o = e._controlledWritableStream;
                if (!rt(o) && "writable" === o._state) {
                    nt(o, bt(e));
                }
                dt(e);
            }(o, t, n), i;
        }(this, e) : d(mt("write"));
    }
}
function at(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_ownerWritableStream") && e instanceof WritableStreamDefaultWriter;
}
function it(e, t) {
    "pending" === e._readyPromiseState ? Ct(e, t) : function(e, t) {
        Tt(e, t);
    }(e, t);
}
Object.defineProperties(WritableStreamDefaultWriter.prototype, {
    abort: {
        enumerable: !0
    },
    close: {
        enumerable: !0
    },
    releaseLock: {
        enumerable: !0
    },
    write: {
        enumerable: !0
    },
    closed: {
        enumerable: !0
    },
    desiredSize: {
        enumerable: !0
    },
    ready: {
        enumerable: !0
    }
}), n(WritableStreamDefaultWriter.prototype.abort, "abort"), n(WritableStreamDefaultWriter.prototype.close, "close"), n(WritableStreamDefaultWriter.prototype.releaseLock, "releaseLock"), n(WritableStreamDefaultWriter.prototype.write, "write"), "symbol" == typeof e.toStringTag && Object.defineProperty(WritableStreamDefaultWriter.prototype, e.toStringTag, {
    value: "WritableStreamDefaultWriter",
    configurable: !0
});
const lt = {};
class WritableStreamDefaultController {
    constructor(){
        throw new TypeError("Illegal constructor");
    }
    get abortReason() {
        if (!st(this)) throw pt("abortReason");
        return this._abortReason;
    }
    get signal() {
        if (!st(this)) throw pt("signal");
        if (void 0 === this._abortController) throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");
        return this._abortController.signal;
    }
    error(e) {
        if (!st(this)) throw pt("error");
        "writable" === this._controlledWritableStream._state && ht(this, e);
    }
    [v](e) {
        const t = this._abortAlgorithm(e);
        return ut(this), t;
    }
    [R]() {
        ce(this);
    }
}
function st(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_controlledWritableStream") && e instanceof WritableStreamDefaultController;
}
function ut(e) {
    e._writeAlgorithm = void 0, e._closeAlgorithm = void 0, e._abortAlgorithm = void 0, e._strategySizeAlgorithm = void 0;
}
function ct(e) {
    return e._strategyHWM - e._queueTotalSize;
}
function dt(e) {
    const t = e._controlledWritableStream;
    if (!e._started) return;
    if (void 0 !== t._inFlightWriteRequest) return;
    if ("erroring" === t._state) return void tt(t);
    if (0 === e._queue.length) return;
    const r = e._queue.peek().value;
    r === lt ? function(e) {
        const t = e._controlledWritableStream;
        (function(e) {
            e._inFlightCloseRequest = e._closeRequest, e._closeRequest = void 0;
        })(t), se(e);
        const r = e._closeAlgorithm();
        ut(e), b(r, ()=>((function(e) {
                e._inFlightCloseRequest._resolve(void 0), e._inFlightCloseRequest = void 0, "erroring" === e._state && (e._storedError = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._resolve(), e._pendingAbortRequest = void 0)), e._state = "closed";
                const t = e._writer;
                void 0 !== t && vt(t);
            })(t), null), (e)=>((function(e, t) {
                e._inFlightCloseRequest._reject(t), e._inFlightCloseRequest = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._reject(t), e._pendingAbortRequest = void 0), Ze(e, t);
            })(t, e), null));
    }(e) : function(e, t) {
        const r = e._controlledWritableStream;
        !function(e) {
            e._inFlightWriteRequest = e._writeRequests.shift();
        }(r);
        b(e._writeAlgorithm(t), ()=>{
            !function(e) {
                e._inFlightWriteRequest._resolve(void 0), e._inFlightWriteRequest = void 0;
            }(r);
            const t = r._state;
            if (se(e), !rt(r) && "writable" === t) {
                const t = bt(e);
                nt(r, t);
            }
            return dt(e), null;
        }, (t)=>("writable" === r._state && ut(e), function(e, t) {
                e._inFlightWriteRequest._reject(t), e._inFlightWriteRequest = void 0, Ze(e, t);
            }(r, t), null));
    }(e, r);
}
function ft(e, t) {
    "writable" === e._controlledWritableStream._state && ht(e, t);
}
function bt(e) {
    return ct(e) <= 0;
}
function ht(e, t) {
    const r = e._controlledWritableStream;
    ut(e), et(r, t);
}
function _t(e) {
    return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`);
}
function pt(e) {
    return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`);
}
function mt(e) {
    return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`);
}
function yt(e) {
    return new TypeError("Cannot " + e + " a stream using a released writer");
}
function gt(e) {
    e._closedPromise = u((t, r)=>{
        e._closedPromise_resolve = t, e._closedPromise_reject = r, e._closedPromiseState = "pending";
    });
}
function wt(e, t) {
    gt(e), St(e, t);
}
function St(e, t) {
    void 0 !== e._closedPromise_reject && (m(e._closedPromise), e._closedPromise_reject(t), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = "rejected");
}
function vt(e) {
    void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = "resolved");
}
function Rt(e) {
    e._readyPromise = u((t, r)=>{
        e._readyPromise_resolve = t, e._readyPromise_reject = r;
    }), e._readyPromiseState = "pending";
}
function Tt(e, t) {
    Rt(e), Ct(e, t);
}
function qt(e) {
    Rt(e), Et(e);
}
function Ct(e, t) {
    void 0 !== e._readyPromise_reject && (m(e._readyPromise), e._readyPromise_reject(t), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = "rejected");
}
function Et(e) {
    void 0 !== e._readyPromise_resolve && (e._readyPromise_resolve(void 0), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = "fulfilled");
}
Object.defineProperties(WritableStreamDefaultController.prototype, {
    abortReason: {
        enumerable: !0
    },
    signal: {
        enumerable: !0
    },
    error: {
        enumerable: !0
    }
}), "symbol" == typeof e.toStringTag && Object.defineProperty(WritableStreamDefaultController.prototype, e.toStringTag, {
    value: "WritableStreamDefaultController",
    configurable: !0
});
const Pt = "undefined" != typeof DOMException ? DOMException : void 0;
const Wt = function(e) {
    if ("function" != typeof e && "object" != typeof e) return !1;
    try {
        return new e, !0;
    } catch (e) {
        return !1;
    }
}(Pt) ? Pt : function() {
    const e = function(e, t) {
        this.message = e || "", this.name = t || "Error", Error.captureStackTrace && Error.captureStackTrace(this, this.constructor);
    };
    return e.prototype = Object.create(Error.prototype), Object.defineProperty(e.prototype, "constructor", {
        value: e,
        writable: !0,
        configurable: !0
    }), e;
}();
function kt(e, t, r, o, n, a) {
    const i = e.getReader(), l = t.getWriter();
    Vt(e) && (e._disturbed = !0);
    let s, _, g, w = !1, S = !1, v = "readable", R = "writable", T = !1, q = !1;
    const C = u((e)=>{
        g = e;
    });
    let E = Promise.resolve(void 0);
    return u((P, W)=>{
        let k;
        function O() {
            if (w) return;
            const e = u((e, t)=>{
                !function r(o) {
                    o ? e() : f(function() {
                        if (w) return c(!0);
                        return f(l.ready, ()=>f(i.read(), (e)=>!!e.done || (E = l.write(e.value), m(E), !1)));
                    }(), r, t);
                }(!1);
            });
            m(e);
        }
        function B() {
            return v = "closed", r ? L() : z(()=>(Ge(t) && (T = rt(t), R = t._state), T || "closed" === R ? c(void 0) : "erroring" === R || "errored" === R ? d(_) : (T = !0, l.close())), !1, void 0), null;
        }
        function A(e) {
            return w || (v = "errored", s = e, o ? L(!0, e) : z(()=>l.abort(e), !0, e)), null;
        }
        function j(e) {
            return S || (R = "errored", _ = e, n ? L(!0, e) : z(()=>i.cancel(e), !0, e)), null;
        }
        if (void 0 !== a && (k = ()=>{
            const e = void 0 !== a.reason ? a.reason : new Wt("Aborted", "AbortError"), t = [];
            o || t.push(()=>"writable" === R ? l.abort(e) : c(void 0)), n || t.push(()=>"readable" === v ? i.cancel(e) : c(void 0)), z(()=>Promise.all(t.map((e)=>e())), !0, e);
        }, a.aborted ? k() : a.addEventListener("abort", k)), Vt(e) && (v = e._state, s = e._storedError), Ge(t) && (R = t._state, _ = t._storedError, T = rt(t)), Vt(e) && Ge(t) && (q = !0, g()), "errored" === v) A(s);
        else if ("erroring" === R || "errored" === R) j(_);
        else if ("closed" === v) B();
        else if (T || "closed" === R) {
            const e = new TypeError("the destination writable stream closed before all data could be piped to it");
            n ? L(!0, e) : z(()=>i.cancel(e), !0, e);
        }
        function z(e, t, r) {
            function o() {
                return "writable" !== R || T ? n() : h(function() {
                    let e;
                    return c(function t() {
                        if (e !== E) return e = E, p(E, t, t);
                    }());
                }(), n), null;
            }
            function n() {
                return e ? b(e(), ()=>F(t, r), (e)=>F(!0, e)) : F(t, r), null;
            }
            w || (w = !0, q ? o() : h(C, o));
        }
        function L(e, t) {
            z(void 0, e, t);
        }
        function F(e, t) {
            return S = !0, l.releaseLock(), i.releaseLock(), void 0 !== a && a.removeEventListener("abort", k), e ? W(t) : P(void 0), null;
        }
        w || (b(i.closed, B, A), b(l.closed, function() {
            return S || (R = "closed"), null;
        }, j)), q ? O() : y(()=>{
            q = !0, g(), O();
        });
    });
}
function Ot(e, t) {
    return function(e) {
        try {
            return e.getReader({
                mode: "byob"
            }).releaseLock(), !0;
        } catch (e) {
            return !1;
        }
    }(e) ? function(e) {
        let t, r, o, n, a, i = e.getReader(), l = !1, s = !1, d = !1, f = !1, h = !1, p = !1;
        const m = u((e)=>{
            a = e;
        });
        function y(e) {
            _(e.closed, (t)=>(e !== i || (o.error(t), n.error(t), h && p || a(void 0)), null));
        }
        function g() {
            l && (i.releaseLock(), i = e.getReader(), y(i), l = !1), b(i.read(), (e)=>{
                var t, r;
                if (d = !1, f = !1, e.done) return h || o.close(), p || n.close(), null === (t = o.byobRequest) || void 0 === t || t.respond(0), null === (r = n.byobRequest) || void 0 === r || r.respond(0), h && p || a(void 0), null;
                const l = e.value, u = l;
                let c = l;
                if (!h && !p) try {
                    c = le(l);
                } catch (e) {
                    return o.error(e), n.error(e), a(i.cancel(e)), null;
                }
                return h || o.enqueue(u), p || n.enqueue(c), s = !1, d ? S() : f && v(), null;
            }, ()=>(s = !1, null));
        }
        function w(t, r) {
            l || (i.releaseLock(), i = e.getReader({
                mode: "byob"
            }), y(i), l = !0);
            const u = r ? n : o, c = r ? o : n;
            b(i.read(t), (e)=>{
                var t;
                d = !1, f = !1;
                const o = r ? p : h, n = r ? h : p;
                if (e.done) {
                    o || u.close(), n || c.close();
                    const r = e.value;
                    return void 0 !== r && (o || u.byobRequest.respondWithNewView(r), n || null === (t = c.byobRequest) || void 0 === t || t.respond(0)), o && n || a(void 0), null;
                }
                const l = e.value;
                if (n) o || u.byobRequest.respondWithNewView(l);
                else {
                    let e;
                    try {
                        e = le(l);
                    } catch (e) {
                        return u.error(e), c.error(e), a(i.cancel(e)), null;
                    }
                    o || u.byobRequest.respondWithNewView(l), c.enqueue(e);
                }
                return s = !1, d ? S() : f && v(), null;
            }, ()=>(s = !1, null));
        }
        function S() {
            if (s) return d = !0, c(void 0);
            s = !0;
            const e = o.byobRequest;
            return null === e ? g() : w(e.view, !1), c(void 0);
        }
        function v() {
            if (s) return f = !0, c(void 0);
            s = !0;
            const e = n.byobRequest;
            return null === e ? g() : w(e.view, !0), c(void 0);
        }
        function R(e) {
            if (h = !0, t = e, p) {
                const e = [
                    t,
                    r
                ], o = i.cancel(e);
                a(o);
            }
            return m;
        }
        function T(e) {
            if (p = !0, r = e, h) {
                const e = [
                    t,
                    r
                ], o = i.cancel(e);
                a(o);
            }
            return m;
        }
        const q = new ReadableStream({
            type: "bytes",
            start (e) {
                o = e;
            },
            pull: S,
            cancel: R
        }), C = new ReadableStream({
            type: "bytes",
            start (e) {
                n = e;
            },
            pull: v,
            cancel: T
        });
        return y(i), [
            q,
            C
        ];
    }(e) : function(e, t) {
        const r = e.getReader();
        let o, n, a, i, l, s = !1, d = !1, f = !1, h = !1;
        const p = u((e)=>{
            l = e;
        });
        function m() {
            return s ? (d = !0, c(void 0)) : (s = !0, b(r.read(), (e)=>{
                if (d = !1, e.done) return f || a.close(), h || i.close(), f && h || l(void 0), null;
                const t = e.value, r = t, o = t;
                return f || a.enqueue(r), h || i.enqueue(o), s = !1, d && m(), null;
            }, ()=>(s = !1, null)), c(void 0));
        }
        function y(e) {
            if (f = !0, o = e, h) {
                const e = [
                    o,
                    n
                ], t = r.cancel(e);
                l(t);
            }
            return p;
        }
        function g(e) {
            if (h = !0, n = e, f) {
                const e = [
                    o,
                    n
                ], t = r.cancel(e);
                l(t);
            }
            return p;
        }
        const w = new ReadableStream({
            start (e) {
                a = e;
            },
            pull: m,
            cancel: y
        }), S = new ReadableStream({
            start (e) {
                i = e;
            },
            pull: m,
            cancel: g
        });
        return _(r.closed, (e)=>(a.error(e), i.error(e), f && h || l(void 0), null)), [
            w,
            S
        ];
    }(e);
}
class ReadableStreamDefaultController {
    constructor(){
        throw new TypeError("Illegal constructor");
    }
    get desiredSize() {
        if (!Bt(this)) throw Dt("desiredSize");
        return Lt(this);
    }
    close() {
        if (!Bt(this)) throw Dt("close");
        if (!Ft(this)) throw new TypeError("The stream is not in a state that permits close");
        !function(e) {
            if (!Ft(e)) return;
            const t = e._controlledReadableStream;
            e._closeRequested = !0, 0 === e._queue.length && (jt(e), Xt(t));
        }(this);
    }
    enqueue(e) {
        if (!Bt(this)) throw Dt("enqueue");
        if (!Ft(this)) throw new TypeError("The stream is not in a state that permits enqueue");
        return function(e, t) {
            if (!Ft(e)) return;
            const r = e._controlledReadableStream;
            if (Ut(r) && X(r) > 0) G(r, t, !1);
            else {
                let r;
                try {
                    r = e._strategySizeAlgorithm(t);
                } catch (t) {
                    throw zt(e, t), t;
                }
                try {
                    ue(e, t, r);
                } catch (t) {
                    throw zt(e, t), t;
                }
            }
            At(e);
        }(this, e);
    }
    error(e) {
        if (!Bt(this)) throw Dt("error");
        zt(this, e);
    }
    [T](e) {
        ce(this);
        const t = this._cancelAlgorithm(e);
        return jt(this), t;
    }
    [q](e) {
        const t = this._controlledReadableStream;
        if (this._queue.length > 0) {
            const r = se(this);
            this._closeRequested && 0 === this._queue.length ? (jt(this), Xt(t)) : At(this), e._chunkSteps(r);
        } else U(t, e), At(this);
    }
    [C]() {}
}
function Bt(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_controlledReadableStream") && e instanceof ReadableStreamDefaultController;
}
function At(e) {
    const t = function(e) {
        const t = e._controlledReadableStream;
        if (!Ft(e)) return !1;
        if (!e._started) return !1;
        if (Ut(t) && X(t) > 0) return !0;
        if (Lt(e) > 0) return !0;
        return !1;
    }(e);
    if (!t) return;
    if (e._pulling) return void (e._pullAgain = !0);
    e._pulling = !0;
    b(e._pullAlgorithm(), ()=>(e._pulling = !1, e._pullAgain && (e._pullAgain = !1, At(e)), null), (t)=>(zt(e, t), null));
}
function jt(e) {
    e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0, e._strategySizeAlgorithm = void 0;
}
function zt(e, t) {
    const r = e._controlledReadableStream;
    "readable" === r._state && (ce(e), jt(e), Jt(r, t));
}
function Lt(e) {
    const t = e._controlledReadableStream._state;
    return "errored" === t ? null : "closed" === t ? 0 : e._strategyHWM - e._queueTotalSize;
}
function Ft(e) {
    return !e._closeRequested && "readable" === e._controlledReadableStream._state;
}
function It(e, t, r, o) {
    const n = Object.create(ReadableStreamDefaultController.prototype);
    let a, i, l;
    a = void 0 !== t.start ? ()=>t.start(n) : ()=>{}, i = void 0 !== t.pull ? ()=>t.pull(n) : ()=>c(void 0), l = void 0 !== t.cancel ? (e)=>t.cancel(e) : ()=>c(void 0), function(e, t, r, o, n, a, i) {
        t._controlledReadableStream = e, t._queue = void 0, t._queueTotalSize = void 0, ce(t), t._started = !1, t._closeRequested = !1, t._pullAgain = !1, t._pulling = !1, t._strategySizeAlgorithm = i, t._strategyHWM = a, t._pullAlgorithm = o, t._cancelAlgorithm = n, e._readableStreamController = t, b(c(r()), ()=>(t._started = !0, At(t), null), (e)=>(zt(t, e), null));
    }(e, n, a, i, l, r, o);
}
function Dt(e) {
    return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`);
}
function $t(e, t, r) {
    return I(e, r), (r)=>w(e, t, [
            r
        ]);
}
function Mt(e, t, r) {
    return I(e, r), (r)=>w(e, t, [
            r
        ]);
}
function Yt(e, t, r) {
    return I(e, r), (r)=>g(e, t, [
            r
        ]);
}
function Qt(e, t) {
    if ("bytes" !== (e = `${e}`)) throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);
    return e;
}
function Nt(e, t) {
    if ("byob" !== (e = `${e}`)) throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);
    return e;
}
function Ht(e, t) {
    F(e, t);
    const r = null == e ? void 0 : e.preventAbort, o = null == e ? void 0 : e.preventCancel, n = null == e ? void 0 : e.preventClose, a = null == e ? void 0 : e.signal;
    return void 0 !== a && function(e, t) {
        if (!function(e) {
            if ("object" != typeof e || null === e) return !1;
            try {
                return "boolean" == typeof e.aborted;
            } catch (e) {
                return !1;
            }
        }(e)) throw new TypeError(`${t} is not an AbortSignal.`);
    }(a, `${t} has member 'signal' that`), {
        preventAbort: Boolean(r),
        preventCancel: Boolean(o),
        preventClose: Boolean(n),
        signal: a
    };
}
function xt(e, t) {
    F(e, t);
    const r = null == e ? void 0 : e.readable;
    M(r, "readable", "ReadableWritablePair"), function(e, t) {
        if (!H(e)) throw new TypeError(`${t} is not a ReadableStream.`);
    }(r, `${t} has member 'readable' that`);
    const o = null == e ? void 0 : e.writable;
    return M(o, "writable", "ReadableWritablePair"), function(e, t) {
        if (!x(e)) throw new TypeError(`${t} is not a WritableStream.`);
    }(o, `${t} has member 'writable' that`), {
        readable: r,
        writable: o
    };
}
Object.defineProperties(ReadableStreamDefaultController.prototype, {
    close: {
        enumerable: !0
    },
    enqueue: {
        enumerable: !0
    },
    error: {
        enumerable: !0
    },
    desiredSize: {
        enumerable: !0
    }
}), n(ReadableStreamDefaultController.prototype.close, "close"), n(ReadableStreamDefaultController.prototype.enqueue, "enqueue"), n(ReadableStreamDefaultController.prototype.error, "error"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableStreamDefaultController.prototype, e.toStringTag, {
    value: "ReadableStreamDefaultController",
    configurable: !0
});
class ReadableStream {
    constructor(e = {}, t = {}){
        void 0 === e ? e = null : D(e, "First parameter");
        const r = Ye(t, "Second parameter"), o = function(e, t) {
            F(e, t);
            const r = e, o = null == r ? void 0 : r.autoAllocateChunkSize, n = null == r ? void 0 : r.cancel, a = null == r ? void 0 : r.pull, i = null == r ? void 0 : r.start, l = null == r ? void 0 : r.type;
            return {
                autoAllocateChunkSize: void 0 === o ? void 0 : N(o, `${t} has member 'autoAllocateChunkSize' that`),
                cancel: void 0 === n ? void 0 : $t(n, r, `${t} has member 'cancel' that`),
                pull: void 0 === a ? void 0 : Mt(a, r, `${t} has member 'pull' that`),
                start: void 0 === i ? void 0 : Yt(i, r, `${t} has member 'start' that`),
                type: void 0 === l ? void 0 : Qt(l, `${t} has member 'type' that`)
            };
        }(e, "First parameter");
        var n;
        if ((n = this)._state = "readable", n._reader = void 0, n._storedError = void 0, n._disturbed = !1, "bytes" === o.type) {
            if (void 0 !== r.size) throw new RangeError("The strategy for a byte stream cannot have a size function");
            Oe(this, o, $e(r, 0));
        } else {
            const e = Me(r);
            It(this, o, $e(r, 1), e);
        }
    }
    get locked() {
        if (!Vt(this)) throw Kt("locked");
        return Ut(this);
    }
    cancel(e) {
        return Vt(this) ? Ut(this) ? d(new TypeError("Cannot cancel a stream that already has a reader")) : Gt(this, e) : d(Kt("cancel"));
    }
    getReader(e) {
        if (!Vt(this)) throw Kt("getReader");
        return void 0 === function(e, t) {
            F(e, t);
            const r = null == e ? void 0 : e.mode;
            return {
                mode: void 0 === r ? void 0 : Nt(r, `${t} has member 'mode' that`)
            };
        }(e, "First parameter").mode ? new ReadableStreamDefaultReader(this) : function(e) {
            return new ReadableStreamBYOBReader(e);
        }(this);
    }
    pipeThrough(e, t = {}) {
        if (!H(this)) throw Kt("pipeThrough");
        $(e, 1, "pipeThrough");
        const r = xt(e, "First parameter"), o = Ht(t, "Second parameter");
        if (this.locked) throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");
        if (r.writable.locked) throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");
        return m(kt(this, r.writable, o.preventClose, o.preventAbort, o.preventCancel, o.signal)), r.readable;
    }
    pipeTo(e, t = {}) {
        if (!H(this)) return d(Kt("pipeTo"));
        if (void 0 === e) return d("Parameter 1 is required in 'pipeTo'.");
        if (!x(e)) return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));
        let r;
        try {
            r = Ht(t, "Second parameter");
        } catch (e) {
            return d(e);
        }
        return this.locked ? d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")) : e.locked ? d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")) : kt(this, e, r.preventClose, r.preventAbort, r.preventCancel, r.signal);
    }
    tee() {
        if (!H(this)) throw Kt("tee");
        if (this.locked) throw new TypeError("Cannot tee a stream that already has a reader");
        return Ot(this);
    }
    values(e) {
        if (!H(this)) throw Kt("values");
        return function(e, t) {
            const r = e.getReader(), o = new te(r, t), n = Object.create(re);
            return n._asyncIteratorImpl = o, n;
        }(this, function(e, t) {
            F(e, t);
            const r = null == e ? void 0 : e.preventCancel;
            return {
                preventCancel: Boolean(r)
            };
        }(e, "First parameter").preventCancel);
    }
}
function Vt(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_readableStreamController") && e instanceof ReadableStream;
}
function Ut(e) {
    return void 0 !== e._reader;
}
function Gt(e, r) {
    if (e._disturbed = !0, "closed" === e._state) return c(void 0);
    if ("errored" === e._state) return d(e._storedError);
    Xt(e);
    const o = e._reader;
    if (void 0 !== o && Fe(o)) {
        const e = o._readIntoRequests;
        o._readIntoRequests = new S, e.forEach((e)=>{
            e._closeSteps(void 0);
        });
    }
    return p(e._readableStreamController[T](r), t);
}
function Xt(e) {
    e._state = "closed";
    const t = e._reader;
    if (void 0 !== t && (j(t), K(t))) {
        const e = t._readRequests;
        t._readRequests = new S, e.forEach((e)=>{
            e._closeSteps();
        });
    }
}
function Jt(e, t) {
    e._state = "errored", e._storedError = t;
    const r = e._reader;
    void 0 !== r && (A(r, t), K(r) ? Z(r, t) : Ie(r, t));
}
function Kt(e) {
    return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`);
}
function Zt(e, t) {
    F(e, t);
    const r = null == e ? void 0 : e.highWaterMark;
    return M(r, "highWaterMark", "QueuingStrategyInit"), {
        highWaterMark: Y(r)
    };
}
Object.defineProperties(ReadableStream.prototype, {
    cancel: {
        enumerable: !0
    },
    getReader: {
        enumerable: !0
    },
    pipeThrough: {
        enumerable: !0
    },
    pipeTo: {
        enumerable: !0
    },
    tee: {
        enumerable: !0
    },
    values: {
        enumerable: !0
    },
    locked: {
        enumerable: !0
    }
}), n(ReadableStream.prototype.cancel, "cancel"), n(ReadableStream.prototype.getReader, "getReader"), n(ReadableStream.prototype.pipeThrough, "pipeThrough"), n(ReadableStream.prototype.pipeTo, "pipeTo"), n(ReadableStream.prototype.tee, "tee"), n(ReadableStream.prototype.values, "values"), "symbol" == typeof e.toStringTag && Object.defineProperty(ReadableStream.prototype, e.toStringTag, {
    value: "ReadableStream",
    configurable: !0
}), "symbol" == typeof e.asyncIterator && Object.defineProperty(ReadableStream.prototype, e.asyncIterator, {
    value: ReadableStream.prototype.values,
    writable: !0,
    configurable: !0
});
const er = (e)=>e.byteLength;
n(er, "size");
class ByteLengthQueuingStrategy {
    constructor(e){
        $(e, 1, "ByteLengthQueuingStrategy"), e = Zt(e, "First parameter"), this._byteLengthQueuingStrategyHighWaterMark = e.highWaterMark;
    }
    get highWaterMark() {
        if (!rr(this)) throw tr("highWaterMark");
        return this._byteLengthQueuingStrategyHighWaterMark;
    }
    get size() {
        if (!rr(this)) throw tr("size");
        return er;
    }
}
function tr(e) {
    return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`);
}
function rr(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_byteLengthQueuingStrategyHighWaterMark") && e instanceof ByteLengthQueuingStrategy;
}
Object.defineProperties(ByteLengthQueuingStrategy.prototype, {
    highWaterMark: {
        enumerable: !0
    },
    size: {
        enumerable: !0
    }
}), "symbol" == typeof e.toStringTag && Object.defineProperty(ByteLengthQueuingStrategy.prototype, e.toStringTag, {
    value: "ByteLengthQueuingStrategy",
    configurable: !0
});
const or = ()=>1;
n(or, "size");
class CountQueuingStrategy {
    constructor(e){
        $(e, 1, "CountQueuingStrategy"), e = Zt(e, "First parameter"), this._countQueuingStrategyHighWaterMark = e.highWaterMark;
    }
    get highWaterMark() {
        if (!ar(this)) throw nr("highWaterMark");
        return this._countQueuingStrategyHighWaterMark;
    }
    get size() {
        if (!ar(this)) throw nr("size");
        return or;
    }
}
function nr(e) {
    return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`);
}
function ar(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_countQueuingStrategyHighWaterMark") && e instanceof CountQueuingStrategy;
}
function ir(e, t, r) {
    return I(e, r), (r)=>w(e, t, [
            r
        ]);
}
function lr(e, t, r) {
    return I(e, r), (r)=>g(e, t, [
            r
        ]);
}
function sr(e, t, r) {
    return I(e, r), (r, o)=>w(e, t, [
            r,
            o
        ]);
}
Object.defineProperties(CountQueuingStrategy.prototype, {
    highWaterMark: {
        enumerable: !0
    },
    size: {
        enumerable: !0
    }
}), "symbol" == typeof e.toStringTag && Object.defineProperty(CountQueuingStrategy.prototype, e.toStringTag, {
    value: "CountQueuingStrategy",
    configurable: !0
});
class TransformStream {
    constructor(e = {}, t = {}, r = {}){
        void 0 === e && (e = null);
        const o = Ye(t, "Second parameter"), n = Ye(r, "Third parameter"), a = function(e, t) {
            F(e, t);
            const r = null == e ? void 0 : e.flush, o = null == e ? void 0 : e.readableType, n = null == e ? void 0 : e.start, a = null == e ? void 0 : e.transform, i = null == e ? void 0 : e.writableType;
            return {
                flush: void 0 === r ? void 0 : ir(r, e, `${t} has member 'flush' that`),
                readableType: o,
                start: void 0 === n ? void 0 : lr(n, e, `${t} has member 'start' that`),
                transform: void 0 === a ? void 0 : sr(a, e, `${t} has member 'transform' that`),
                writableType: i
            };
        }(e, "First parameter");
        if (void 0 !== a.readableType) throw new RangeError("Invalid readableType specified");
        if (void 0 !== a.writableType) throw new RangeError("Invalid writableType specified");
        const i = $e(n, 0), l = Me(n), s = $e(o, 1), f = Me(o);
        let b;
        !function(e, t, r, o, n, a) {
            function i() {
                return t;
            }
            function l(t) {
                return function(e, t) {
                    const r = e._transformStreamController;
                    if (e._backpressure) {
                        return p(e._backpressureChangePromise, ()=>{
                            if ("erroring" === (Ge(e._writable) ? e._writable._state : e._writableState)) throw Ge(e._writable) ? e._writable._storedError : e._writableStoredError;
                            return pr(r, t);
                        });
                    }
                    return pr(r, t);
                }(e, t);
            }
            function s(t) {
                return function(e, t) {
                    return cr(e, t), c(void 0);
                }(e, t);
            }
            function u() {
                return function(e) {
                    const t = e._transformStreamController, r = t._flushAlgorithm();
                    return hr(t), p(r, ()=>{
                        if ("errored" === e._readableState) throw e._readableStoredError;
                        gr(e) && wr(e);
                    }, (t)=>{
                        throw cr(e, t), e._readableStoredError;
                    });
                }(e);
            }
            function d() {
                return function(e) {
                    return fr(e, !1), e._backpressureChangePromise;
                }(e);
            }
            function f(t) {
                return dr(e, t), c(void 0);
            }
            e._writableState = "writable", e._writableStoredError = void 0, e._writableHasInFlightOperation = !1, e._writableStarted = !1, e._writable = function(e, t, r, o, n, a, i) {
                return new WritableStream({
                    start (r) {
                        e._writableController = r;
                        try {
                            const t = r.signal;
                            void 0 !== t && t.addEventListener("abort", ()=>{
                                "writable" === e._writableState && (e._writableState = "erroring", t.reason && (e._writableStoredError = t.reason));
                            });
                        } catch (e) {}
                        return p(t(), ()=>(e._writableStarted = !0, Cr(e), null), (t)=>{
                            throw e._writableStarted = !0, Rr(e, t), t;
                        });
                    },
                    write: (t)=>((function(e) {
                            e._writableHasInFlightOperation = !0;
                        })(e), p(r(t), ()=>((function(e) {
                                e._writableHasInFlightOperation = !1;
                            })(e), Cr(e), null), (t)=>{
                            throw function(e, t) {
                                e._writableHasInFlightOperation = !1, Rr(e, t);
                            }(e, t), t;
                        })),
                    close: ()=>((function(e) {
                            e._writableHasInFlightOperation = !0;
                        })(e), p(o(), ()=>((function(e) {
                                e._writableHasInFlightOperation = !1;
                                "erroring" === e._writableState && (e._writableStoredError = void 0);
                                e._writableState = "closed";
                            })(e), null), (t)=>{
                            throw function(e, t) {
                                e._writableHasInFlightOperation = !1, e._writableState, Rr(e, t);
                            }(e, t), t;
                        })),
                    abort: (t)=>(e._writableState = "errored", e._writableStoredError = t, n(t))
                }, {
                    highWaterMark: a,
                    size: i
                });
            }(e, i, l, u, s, r, o), e._readableState = "readable", e._readableStoredError = void 0, e._readableCloseRequested = !1, e._readablePulling = !1, e._readable = function(e, t, r, o, n, a) {
                return new ReadableStream({
                    start: (r)=>(e._readableController = r, t().catch((t)=>{
                            Sr(e, t);
                        })),
                    pull: ()=>(e._readablePulling = !0, r().catch((t)=>{
                            Sr(e, t);
                        })),
                    cancel: (t)=>(e._readableState = "closed", o(t))
                }, {
                    highWaterMark: n,
                    size: a
                });
            }(e, i, d, f, n, a), e._backpressure = void 0, e._backpressureChangePromise = void 0, e._backpressureChangePromise_resolve = void 0, fr(e, !0), e._transformStreamController = void 0;
        }(this, u((e)=>{
            b = e;
        }), s, f, i, l), function(e, t) {
            const r = Object.create(TransformStreamDefaultController.prototype);
            let o, n;
            o = void 0 !== t.transform ? (e)=>t.transform(e, r) : (e)=>{
                try {
                    return _r(r, e), c(void 0);
                } catch (e) {
                    return d(e);
                }
            };
            n = void 0 !== t.flush ? ()=>t.flush(r) : ()=>c(void 0);
            !function(e, t, r, o) {
                t._controlledTransformStream = e, e._transformStreamController = t, t._transformAlgorithm = r, t._flushAlgorithm = o;
            }(e, r, o, n);
        }(this, a), void 0 !== a.start ? b(a.start(this._transformStreamController)) : b(void 0);
    }
    get readable() {
        if (!ur(this)) throw yr("readable");
        return this._readable;
    }
    get writable() {
        if (!ur(this)) throw yr("writable");
        return this._writable;
    }
}
function ur(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_transformStreamController") && e instanceof TransformStream;
}
function cr(e, t) {
    Sr(e, t), dr(e, t);
}
function dr(e, t) {
    hr(e._transformStreamController), function(e, t) {
        e._writableController.error(t);
        "writable" === e._writableState && Tr(e, t);
    }(e, t), e._backpressure && fr(e, !1);
}
function fr(e, t) {
    void 0 !== e._backpressureChangePromise && e._backpressureChangePromise_resolve(), e._backpressureChangePromise = u((t)=>{
        e._backpressureChangePromise_resolve = t;
    }), e._backpressure = t;
}
Object.defineProperties(TransformStream.prototype, {
    readable: {
        enumerable: !0
    },
    writable: {
        enumerable: !0
    }
}), "symbol" == typeof e.toStringTag && Object.defineProperty(TransformStream.prototype, e.toStringTag, {
    value: "TransformStream",
    configurable: !0
});
class TransformStreamDefaultController {
    constructor(){
        throw new TypeError("Illegal constructor");
    }
    get desiredSize() {
        if (!br(this)) throw mr("desiredSize");
        return vr(this._controlledTransformStream);
    }
    enqueue(e) {
        if (!br(this)) throw mr("enqueue");
        _r(this, e);
    }
    error(e) {
        if (!br(this)) throw mr("error");
        var t;
        t = e, cr(this._controlledTransformStream, t);
    }
    terminate() {
        if (!br(this)) throw mr("terminate");
        !function(e) {
            const t = e._controlledTransformStream;
            gr(t) && wr(t);
            const r = new TypeError("TransformStream terminated");
            dr(t, r);
        }(this);
    }
}
function br(e) {
    return !!r(e) && !!Object.prototype.hasOwnProperty.call(e, "_controlledTransformStream") && e instanceof TransformStreamDefaultController;
}
function hr(e) {
    e._transformAlgorithm = void 0, e._flushAlgorithm = void 0;
}
function _r(e, t) {
    const r = e._controlledTransformStream;
    if (!gr(r)) throw new TypeError("Readable side is not in a state that permits enqueue");
    try {
        !function(e, t) {
            e._readablePulling = !1;
            try {
                e._readableController.enqueue(t);
            } catch (t) {
                throw Sr(e, t), t;
            }
        }(r, t);
    } catch (e) {
        throw dr(r, e), r._readableStoredError;
    }
    const o = function(e) {
        return !function(e) {
            if (!gr(e)) return !1;
            if (e._readablePulling) return !0;
            if (vr(e) > 0) return !0;
            return !1;
        }(e);
    }(r);
    o !== r._backpressure && fr(r, !0);
}
function pr(e, t) {
    return p(e._transformAlgorithm(t), void 0, (t)=>{
        throw cr(e._controlledTransformStream, t), t;
    });
}
function mr(e) {
    return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`);
}
function yr(e) {
    return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`);
}
function gr(e) {
    return !e._readableCloseRequested && "readable" === e._readableState;
}
function wr(e) {
    e._readableState = "closed", e._readableCloseRequested = !0, e._readableController.close();
}
function Sr(e, t) {
    "readable" === e._readableState && (e._readableState = "errored", e._readableStoredError = t), e._readableController.error(t);
}
function vr(e) {
    return e._readableController.desiredSize;
}
function Rr(e, t) {
    "writable" !== e._writableState ? qr(e) : Tr(e, t);
}
function Tr(e, t) {
    e._writableState = "erroring", e._writableStoredError = t, !function(e) {
        return e._writableHasInFlightOperation;
    }(e) && e._writableStarted && qr(e);
}
function qr(e) {
    e._writableState = "errored";
}
function Cr(e) {
    "erroring" === e._writableState && qr(e);
}
Object.defineProperties(TransformStreamDefaultController.prototype, {
    enqueue: {
        enumerable: !0
    },
    error: {
        enumerable: !0
    },
    terminate: {
        enumerable: !0
    },
    desiredSize: {
        enumerable: !0
    }
}), n(TransformStreamDefaultController.prototype.enqueue, "enqueue"), n(TransformStreamDefaultController.prototype.error, "error"), n(TransformStreamDefaultController.prototype.terminate, "terminate"), "symbol" == typeof e.toStringTag && Object.defineProperty(TransformStreamDefaultController.prototype, e.toStringTag, {
    value: "TransformStreamDefaultController",
    configurable: !0
});
;
}}),
"[project]/node_modules/formdata-node/lib/esm/isFunction.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isFunction": (()=>isFunction)
});
const isFunction = (value)=>typeof value === "function";
}}),
"[project]/node_modules/formdata-node/lib/esm/blobHelpers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*! Based on fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> & David Frank */ __turbopack_context__.s({
    "consumeBlobParts": (()=>consumeBlobParts),
    "sliceBlob": (()=>sliceBlob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/isFunction.js [app-route] (ecmascript)");
;
const CHUNK_SIZE = 65536;
async function* clonePart(part) {
    const end = part.byteOffset + part.byteLength;
    let position = part.byteOffset;
    while(position !== end){
        const size = Math.min(end - position, CHUNK_SIZE);
        const chunk = part.buffer.slice(position, position + size);
        position += chunk.byteLength;
        yield new Uint8Array(chunk);
    }
}
async function* consumeNodeBlob(blob) {
    let position = 0;
    while(position !== blob.size){
        const chunk = blob.slice(position, Math.min(blob.size, position + CHUNK_SIZE));
        const buffer = await chunk.arrayBuffer();
        position += buffer.byteLength;
        yield new Uint8Array(buffer);
    }
}
async function* consumeBlobParts(parts, clone = false) {
    for (const part of parts){
        if (ArrayBuffer.isView(part)) {
            if (clone) {
                yield* clonePart(part);
            } else {
                yield part;
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(part.stream)) {
            yield* part.stream();
        } else {
            yield* consumeNodeBlob(part);
        }
    }
}
function* sliceBlob(blobParts, blobSize, start = 0, end) {
    end !== null && end !== void 0 ? end : end = blobSize;
    let relativeStart = start < 0 ? Math.max(blobSize + start, 0) : Math.min(start, blobSize);
    let relativeEnd = end < 0 ? Math.max(blobSize + end, 0) : Math.min(end, blobSize);
    const span = Math.max(relativeEnd - relativeStart, 0);
    let added = 0;
    for (const part of blobParts){
        if (added >= span) {
            break;
        }
        const partSize = ArrayBuffer.isView(part) ? part.byteLength : part.size;
        if (relativeStart && partSize <= relativeStart) {
            relativeStart -= partSize;
            relativeEnd -= partSize;
        } else {
            let chunk;
            if (ArrayBuffer.isView(part)) {
                chunk = part.subarray(relativeStart, Math.min(partSize, relativeEnd));
                added += chunk.byteLength;
            } else {
                chunk = part.slice(relativeStart, Math.min(partSize, relativeEnd));
                added += chunk.size;
            }
            relativeEnd -= partSize;
            relativeStart = 0;
            yield chunk;
        }
    }
}
}}),
"[project]/node_modules/formdata-node/lib/esm/Blob.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*! Based on fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> & David Frank */ __turbopack_context__.s({
    "Blob": (()=>Blob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$streams$2d$polyfill$2f$dist$2f$ponyfill$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/web-streams-polyfill/dist/ponyfill.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/isFunction.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$blobHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/blobHelpers.js [app-route] (ecmascript)");
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var _Blob_parts, _Blob_type, _Blob_size;
;
;
;
class Blob {
    constructor(blobParts = [], options = {}){
        _Blob_parts.set(this, []);
        _Blob_type.set(this, "");
        _Blob_size.set(this, 0);
        options !== null && options !== void 0 ? options : options = {};
        if (typeof blobParts !== "object" || blobParts === null) {
            throw new TypeError("Failed to construct 'Blob': " + "The provided value cannot be converted to a sequence.");
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(blobParts[Symbol.iterator])) {
            throw new TypeError("Failed to construct 'Blob': " + "The object must have a callable @@iterator property.");
        }
        if (typeof options !== "object" && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(options)) {
            throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");
        }
        const encoder = new TextEncoder();
        for (const raw of blobParts){
            let part;
            if (ArrayBuffer.isView(raw)) {
                part = new Uint8Array(raw.buffer.slice(raw.byteOffset, raw.byteOffset + raw.byteLength));
            } else if (raw instanceof ArrayBuffer) {
                part = new Uint8Array(raw.slice(0));
            } else if (raw instanceof Blob) {
                part = raw;
            } else {
                part = encoder.encode(String(raw));
            }
            __classPrivateFieldSet(this, _Blob_size, __classPrivateFieldGet(this, _Blob_size, "f") + (ArrayBuffer.isView(part) ? part.byteLength : part.size), "f");
            __classPrivateFieldGet(this, _Blob_parts, "f").push(part);
        }
        const type = options.type === undefined ? "" : String(options.type);
        __classPrivateFieldSet(this, _Blob_type, /^[\x20-\x7E]*$/.test(type) ? type : "", "f");
    }
    static [(_Blob_parts = new WeakMap(), _Blob_type = new WeakMap(), _Blob_size = new WeakMap(), Symbol.hasInstance)](value) {
        return Boolean(value && typeof value === "object" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.constructor) && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.stream) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.arrayBuffer)) && /^(Blob|File)$/.test(value[Symbol.toStringTag]));
    }
    get type() {
        return __classPrivateFieldGet(this, _Blob_type, "f");
    }
    get size() {
        return __classPrivateFieldGet(this, _Blob_size, "f");
    }
    slice(start, end, contentType) {
        return new Blob((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$blobHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sliceBlob"])(__classPrivateFieldGet(this, _Blob_parts, "f"), this.size, start, end), {
            type: contentType
        });
    }
    async text() {
        const decoder = new TextDecoder();
        let result = "";
        for await (const chunk of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$blobHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["consumeBlobParts"])(__classPrivateFieldGet(this, _Blob_parts, "f"))){
            result += decoder.decode(chunk, {
                stream: true
            });
        }
        result += decoder.decode();
        return result;
    }
    async arrayBuffer() {
        const view = new Uint8Array(this.size);
        let offset = 0;
        for await (const chunk of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$blobHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["consumeBlobParts"])(__classPrivateFieldGet(this, _Blob_parts, "f"))){
            view.set(chunk, offset);
            offset += chunk.length;
        }
        return view.buffer;
    }
    stream() {
        const iterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$blobHelpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["consumeBlobParts"])(__classPrivateFieldGet(this, _Blob_parts, "f"), true);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$web$2d$streams$2d$polyfill$2f$dist$2f$ponyfill$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ReadableStream"]({
            async pull (controller) {
                const { value, done } = await iterator.next();
                if (done) {
                    return queueMicrotask(()=>controller.close());
                }
                controller.enqueue(value);
            },
            async cancel () {
                await iterator.return();
            }
        });
    }
    get [Symbol.toStringTag]() {
        return "Blob";
    }
}
Object.defineProperties(Blob.prototype, {
    type: {
        enumerable: true
    },
    size: {
        enumerable: true
    },
    slice: {
        enumerable: true
    },
    stream: {
        enumerable: true
    },
    text: {
        enumerable: true
    },
    arrayBuffer: {
        enumerable: true
    }
});
}}),
"[project]/node_modules/formdata-node/lib/esm/File.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "File": (()=>File)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/Blob.js [app-route] (ecmascript)");
var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _File_name, _File_lastModified;
;
class File extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Blob"] {
    constructor(fileBits, name, options = {}){
        super(fileBits, options);
        _File_name.set(this, void 0);
        _File_lastModified.set(this, 0);
        if (arguments.length < 2) {
            throw new TypeError("Failed to construct 'File': 2 arguments required, " + `but only ${arguments.length} present.`);
        }
        __classPrivateFieldSet(this, _File_name, String(name), "f");
        const lastModified = options.lastModified === undefined ? Date.now() : Number(options.lastModified);
        if (!Number.isNaN(lastModified)) {
            __classPrivateFieldSet(this, _File_lastModified, lastModified, "f");
        }
    }
    static [(_File_name = new WeakMap(), _File_lastModified = new WeakMap(), Symbol.hasInstance)](value) {
        return value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Blob"] && value[Symbol.toStringTag] === "File" && typeof value.name === "string";
    }
    get name() {
        return __classPrivateFieldGet(this, _File_name, "f");
    }
    get lastModified() {
        return __classPrivateFieldGet(this, _File_lastModified, "f");
    }
    get webkitRelativePath() {
        return "";
    }
    get [Symbol.toStringTag]() {
        return "File";
    }
}
}}),
"[project]/node_modules/formdata-node/lib/esm/isFile.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isFile": (()=>isFile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/File.js [app-route] (ecmascript)");
;
const isFile = (value)=>value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["File"];
}}),
"[project]/node_modules/formdata-node/lib/esm/isBlob.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isBlob": (()=>isBlob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/Blob.js [app-route] (ecmascript)");
;
const isBlob = (value)=>value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Blob"];
}}),
"[project]/node_modules/formdata-node/lib/esm/deprecateConstructorEntries.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deprecateConstructorEntries": (()=>deprecateConstructorEntries)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/util [external] (util, cjs)");
;
const deprecateConstructorEntries = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["deprecate"])(()=>{}, "Constructor \"entries\" argument is not spec-compliant " + "and will be removed in next major release.");
}}),
"[project]/node_modules/formdata-node/lib/esm/FormData.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FormData": (()=>FormData)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/util [external] (util, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/File.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFile$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/isFile.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isBlob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/isBlob.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/isFunction.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$deprecateConstructorEntries$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/deprecateConstructorEntries.js [app-route] (ecmascript)");
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _FormData_instances, _FormData_entries, _FormData_setEntry;
;
;
;
;
;
;
class FormData {
    constructor(entries){
        _FormData_instances.add(this);
        _FormData_entries.set(this, new Map());
        if (entries) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$deprecateConstructorEntries$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deprecateConstructorEntries"])();
            entries.forEach(({ name, value, fileName })=>this.append(name, value, fileName));
        }
    }
    static [(_FormData_entries = new WeakMap(), _FormData_instances = new WeakSet(), Symbol.hasInstance)](value) {
        return Boolean(value && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.constructor) && value[Symbol.toStringTag] === "FormData" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.append) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.set) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.get) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.getAll) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.has) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.delete) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.entries) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.values) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.keys) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value[Symbol.iterator]) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFunction"])(value.forEach));
    }
    append(name, value, fileName) {
        __classPrivateFieldGet(this, _FormData_instances, "m", _FormData_setEntry).call(this, {
            name,
            fileName,
            append: true,
            rawValue: value,
            argsLength: arguments.length
        });
    }
    set(name, value, fileName) {
        __classPrivateFieldGet(this, _FormData_instances, "m", _FormData_setEntry).call(this, {
            name,
            fileName,
            append: false,
            rawValue: value,
            argsLength: arguments.length
        });
    }
    get(name) {
        const field = __classPrivateFieldGet(this, _FormData_entries, "f").get(String(name));
        if (!field) {
            return null;
        }
        return field[0];
    }
    getAll(name) {
        const field = __classPrivateFieldGet(this, _FormData_entries, "f").get(String(name));
        if (!field) {
            return [];
        }
        return field.slice();
    }
    has(name) {
        return __classPrivateFieldGet(this, _FormData_entries, "f").has(String(name));
    }
    delete(name) {
        __classPrivateFieldGet(this, _FormData_entries, "f").delete(String(name));
    }
    *keys() {
        for (const key of __classPrivateFieldGet(this, _FormData_entries, "f").keys()){
            yield key;
        }
    }
    *entries() {
        for (const name of this.keys()){
            const values = this.getAll(name);
            for (const value of values){
                yield [
                    name,
                    value
                ];
            }
        }
    }
    *values() {
        for (const [, value] of this){
            yield value;
        }
    }
    [(_FormData_setEntry = function _FormData_setEntry({ name, rawValue, append, fileName, argsLength }) {
        const methodName = append ? "append" : "set";
        if (argsLength < 2) {
            throw new TypeError(`Failed to execute '${methodName}' on 'FormData': ` + `2 arguments required, but only ${argsLength} present.`);
        }
        name = String(name);
        let value;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isFile$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFile"])(rawValue)) {
            value = fileName === undefined ? rawValue : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["File"]([
                rawValue
            ], fileName, {
                type: rawValue.type,
                lastModified: rawValue.lastModified
            });
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$isBlob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isBlob"])(rawValue)) {
            value = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["File"]([
                rawValue
            ], fileName === undefined ? "blob" : fileName, {
                type: rawValue.type
            });
        } else if (fileName) {
            throw new TypeError(`Failed to execute '${methodName}' on 'FormData': ` + "parameter 2 is not of type 'Blob'.");
        } else {
            value = String(rawValue);
        }
        const values = __classPrivateFieldGet(this, _FormData_entries, "f").get(name);
        if (!values) {
            return void __classPrivateFieldGet(this, _FormData_entries, "f").set(name, [
                value
            ]);
        }
        if (!append) {
            return void __classPrivateFieldGet(this, _FormData_entries, "f").set(name, [
                value
            ]);
        }
        values.push(value);
    }, Symbol.iterator)]() {
        return this.entries();
    }
    forEach(callback, thisArg) {
        for (const [name, value] of this){
            callback.call(thisArg, value, name, this);
        }
    }
    get [Symbol.toStringTag]() {
        return "FormData";
    }
    [__TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["inspect"].custom]() {
        return this[Symbol.toStringTag];
    }
}
}}),
"[project]/node_modules/formdata-node/lib/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$FormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/FormData.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/Blob.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/File.js [app-route] (ecmascript)");
;
;
;
}}),
"[project]/node_modules/formdata-node/lib/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$FormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/FormData.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$Blob$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/Blob.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$File$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/File.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$formdata$2d$node$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/formdata-node/lib/esm/index.js [app-route] (ecmascript) <locals>");
}}),
"[project]/node_modules/ms/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * Helpers.
 */ var s = 1000;
var m = s * 60;
var h = m * 60;
var d = h * 24;
var w = d * 7;
var y = d * 365.25;
/**
 * Parse or format the given `val`.
 *
 * Options:
 *
 *  - `long` verbose formatting [false]
 *
 * @param {String|Number} val
 * @param {Object} [options]
 * @throws {Error} throw an error if val is not a non-empty string or a number
 * @return {String|Number}
 * @api public
 */ module.exports = function(val, options) {
    options = options || {};
    var type = typeof val;
    if (type === 'string' && val.length > 0) {
        return parse(val);
    } else if (type === 'number' && isFinite(val)) {
        return options.long ? fmtLong(val) : fmtShort(val);
    }
    throw new Error('val is not a non-empty string or a valid number. val=' + JSON.stringify(val));
};
/**
 * Parse the given `str` and return milliseconds.
 *
 * @param {String} str
 * @return {Number}
 * @api private
 */ function parse(str) {
    str = String(str);
    if (str.length > 100) {
        return;
    }
    var match = /^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(str);
    if (!match) {
        return;
    }
    var n = parseFloat(match[1]);
    var type = (match[2] || 'ms').toLowerCase();
    switch(type){
        case 'years':
        case 'year':
        case 'yrs':
        case 'yr':
        case 'y':
            return n * y;
        case 'weeks':
        case 'week':
        case 'w':
            return n * w;
        case 'days':
        case 'day':
        case 'd':
            return n * d;
        case 'hours':
        case 'hour':
        case 'hrs':
        case 'hr':
        case 'h':
            return n * h;
        case 'minutes':
        case 'minute':
        case 'mins':
        case 'min':
        case 'm':
            return n * m;
        case 'seconds':
        case 'second':
        case 'secs':
        case 'sec':
        case 's':
            return n * s;
        case 'milliseconds':
        case 'millisecond':
        case 'msecs':
        case 'msec':
        case 'ms':
            return n;
        default:
            return undefined;
    }
}
/**
 * Short format for `ms`.
 *
 * @param {Number} ms
 * @return {String}
 * @api private
 */ function fmtShort(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
        return Math.round(ms / d) + 'd';
    }
    if (msAbs >= h) {
        return Math.round(ms / h) + 'h';
    }
    if (msAbs >= m) {
        return Math.round(ms / m) + 'm';
    }
    if (msAbs >= s) {
        return Math.round(ms / s) + 's';
    }
    return ms + 'ms';
}
/**
 * Long format for `ms`.
 *
 * @param {Number} ms
 * @return {String}
 * @api private
 */ function fmtLong(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
        return plural(ms, msAbs, d, 'day');
    }
    if (msAbs >= h) {
        return plural(ms, msAbs, h, 'hour');
    }
    if (msAbs >= m) {
        return plural(ms, msAbs, m, 'minute');
    }
    if (msAbs >= s) {
        return plural(ms, msAbs, s, 'second');
    }
    return ms + ' ms';
}
/**
 * Pluralization helper.
 */ function plural(ms, msAbs, n, name) {
    var isPlural = msAbs >= n * 1.5;
    return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');
}
}}),
"[project]/node_modules/humanize-ms/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*!
 * humanize-ms - index.js
 * Copyright(c) 2014 dead_horse <<EMAIL>>
 * MIT Licensed
 */ 'use strict';
/**
 * Module dependencies.
 */ var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
var ms = __turbopack_context__.r("[project]/node_modules/ms/index.js [app-route] (ecmascript)");
module.exports = function(t) {
    if (typeof t === 'number') return t;
    var r = ms(t);
    if (r === undefined) {
        var err = new Error(util.format('humanize-ms(%j) result undefined', t));
        console.warn(err.stack);
    }
    return r;
};
}}),
"[project]/node_modules/agentkeepalive/lib/constants.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
module.exports = {
    // agent
    CURRENT_ID: Symbol('agentkeepalive#currentId'),
    CREATE_ID: Symbol('agentkeepalive#createId'),
    INIT_SOCKET: Symbol('agentkeepalive#initSocket'),
    CREATE_HTTPS_CONNECTION: Symbol('agentkeepalive#createHttpsConnection'),
    // socket
    SOCKET_CREATED_TIME: Symbol('agentkeepalive#socketCreatedTime'),
    SOCKET_NAME: Symbol('agentkeepalive#socketName'),
    SOCKET_REQUEST_COUNT: Symbol('agentkeepalive#socketRequestCount'),
    SOCKET_REQUEST_FINISHED_COUNT: Symbol('agentkeepalive#socketRequestFinishedCount')
};
}}),
"[project]/node_modules/agentkeepalive/lib/agent.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const OriginalAgent = __turbopack_context__.r("[externals]/http [external] (http, cjs)").Agent;
const ms = __turbopack_context__.r("[project]/node_modules/humanize-ms/index.js [app-route] (ecmascript)");
const debug = __turbopack_context__.r("[externals]/util [external] (util, cjs)").debuglog('agentkeepalive');
const { INIT_SOCKET, CURRENT_ID, CREATE_ID, SOCKET_CREATED_TIME, SOCKET_NAME, SOCKET_REQUEST_COUNT, SOCKET_REQUEST_FINISHED_COUNT } = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/constants.js [app-route] (ecmascript)");
// OriginalAgent come from
// - https://github.com/nodejs/node/blob/v8.12.0/lib/_http_agent.js
// - https://github.com/nodejs/node/blob/v10.12.0/lib/_http_agent.js
// node <= 10
let defaultTimeoutListenerCount = 1;
const majorVersion = parseInt(process.version.split('.', 1)[0].substring(1));
if (majorVersion >= 11 && majorVersion <= 12) {
    defaultTimeoutListenerCount = 2;
} else if (majorVersion >= 13) {
    defaultTimeoutListenerCount = 3;
}
function deprecate(message) {
    console.log('[agentkeepalive:deprecated] %s', message);
}
class Agent extends OriginalAgent {
    constructor(options){
        options = options || {};
        options.keepAlive = options.keepAlive !== false;
        // default is keep-alive and 4s free socket timeout
        // see https://medium.com/ssense-tech/reduce-networking-errors-in-nodejs-23b4eb9f2d83
        if (options.freeSocketTimeout === undefined) {
            options.freeSocketTimeout = 4000;
        }
        // Legacy API: keepAliveTimeout should be rename to `freeSocketTimeout`
        if (options.keepAliveTimeout) {
            deprecate('options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead');
            options.freeSocketTimeout = options.keepAliveTimeout;
            delete options.keepAliveTimeout;
        }
        // Legacy API: freeSocketKeepAliveTimeout should be rename to `freeSocketTimeout`
        if (options.freeSocketKeepAliveTimeout) {
            deprecate('options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead');
            options.freeSocketTimeout = options.freeSocketKeepAliveTimeout;
            delete options.freeSocketKeepAliveTimeout;
        }
        // Sets the socket to timeout after timeout milliseconds of inactivity on the socket.
        // By default is double free socket timeout.
        if (options.timeout === undefined) {
            // make sure socket default inactivity timeout >= 8s
            options.timeout = Math.max(options.freeSocketTimeout * 2, 8000);
        }
        // support humanize format
        options.timeout = ms(options.timeout);
        options.freeSocketTimeout = ms(options.freeSocketTimeout);
        options.socketActiveTTL = options.socketActiveTTL ? ms(options.socketActiveTTL) : 0;
        super(options);
        this[CURRENT_ID] = 0;
        // create socket success counter
        this.createSocketCount = 0;
        this.createSocketCountLastCheck = 0;
        this.createSocketErrorCount = 0;
        this.createSocketErrorCountLastCheck = 0;
        this.closeSocketCount = 0;
        this.closeSocketCountLastCheck = 0;
        // socket error event count
        this.errorSocketCount = 0;
        this.errorSocketCountLastCheck = 0;
        // request finished counter
        this.requestCount = 0;
        this.requestCountLastCheck = 0;
        // including free socket timeout counter
        this.timeoutSocketCount = 0;
        this.timeoutSocketCountLastCheck = 0;
        this.on('free', (socket)=>{
            // https://github.com/nodejs/node/pull/32000
            // Node.js native agent will check socket timeout eqs agent.options.timeout.
            // Use the ttl or freeSocketTimeout to overwrite.
            const timeout = this.calcSocketTimeout(socket);
            if (timeout > 0 && socket.timeout !== timeout) {
                socket.setTimeout(timeout);
            }
        });
    }
    get freeSocketKeepAliveTimeout() {
        deprecate('agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead');
        return this.options.freeSocketTimeout;
    }
    get timeout() {
        deprecate('agent.timeout is deprecated, please use agent.options.timeout instead');
        return this.options.timeout;
    }
    get socketActiveTTL() {
        deprecate('agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead');
        return this.options.socketActiveTTL;
    }
    calcSocketTimeout(socket) {
        /**
     * return <= 0: should free socket
     * return > 0: should update socket timeout
     * return undefined: not find custom timeout
     */ let freeSocketTimeout = this.options.freeSocketTimeout;
        const socketActiveTTL = this.options.socketActiveTTL;
        if (socketActiveTTL) {
            // check socketActiveTTL
            const aliveTime = Date.now() - socket[SOCKET_CREATED_TIME];
            const diff = socketActiveTTL - aliveTime;
            if (diff <= 0) {
                return diff;
            }
            if (freeSocketTimeout && diff < freeSocketTimeout) {
                freeSocketTimeout = diff;
            }
        }
        // set freeSocketTimeout
        if (freeSocketTimeout) {
            // set free keepalive timer
            // try to use socket custom freeSocketTimeout first, support headers['keep-alive']
            // https://github.com/node-modules/urllib/blob/b76053020923f4d99a1c93cf2e16e0c5ba10bacf/lib/urllib.js#L498
            const customFreeSocketTimeout = socket.freeSocketTimeout || socket.freeSocketKeepAliveTimeout;
            return customFreeSocketTimeout || freeSocketTimeout;
        }
    }
    keepSocketAlive(socket) {
        const result = super.keepSocketAlive(socket);
        // should not keepAlive, do nothing
        if (!result) return result;
        const customTimeout = this.calcSocketTimeout(socket);
        if (typeof customTimeout === 'undefined') {
            return true;
        }
        if (customTimeout <= 0) {
            debug('%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], customTimeout);
            return false;
        }
        if (socket.timeout !== customTimeout) {
            socket.setTimeout(customTimeout);
        }
        return true;
    }
    // only call on addRequest
    reuseSocket(...args) {
        // reuseSocket(socket, req)
        super.reuseSocket(...args);
        const socket = args[0];
        const req = args[1];
        req.reusedSocket = true;
        const agentTimeout = this.options.timeout;
        if (getSocketTimeout(socket) !== agentTimeout) {
            // reset timeout before use
            socket.setTimeout(agentTimeout);
            debug('%s reset timeout to %sms', socket[SOCKET_NAME], agentTimeout);
        }
        socket[SOCKET_REQUEST_COUNT]++;
        debug('%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], getSocketTimeout(socket));
    }
    [CREATE_ID]() {
        const id = this[CURRENT_ID]++;
        if (this[CURRENT_ID] === Number.MAX_SAFE_INTEGER) this[CURRENT_ID] = 0;
        return id;
    }
    [INIT_SOCKET](socket, options) {
        // bugfix here.
        // https on node 8, 10 won't set agent.options.timeout by default
        // TODO: need to fix on node itself
        if (options.timeout) {
            const timeout = getSocketTimeout(socket);
            if (!timeout) {
                socket.setTimeout(options.timeout);
            }
        }
        if (this.options.keepAlive) {
            // Disable Nagle's algorithm: http://blog.caustik.com/2012/04/08/scaling-node-js-to-100k-concurrent-connections/
            // https://fengmk2.com/benchmark/nagle-algorithm-delayed-ack-mock.html
            socket.setNoDelay(true);
        }
        this.createSocketCount++;
        if (this.options.socketActiveTTL) {
            socket[SOCKET_CREATED_TIME] = Date.now();
        }
        // don't show the hole '-----BEGIN CERTIFICATE----' key string
        socket[SOCKET_NAME] = `sock[${this[CREATE_ID]()}#${options._agentKey}]`.split('-----BEGIN', 1)[0];
        socket[SOCKET_REQUEST_COUNT] = 1;
        socket[SOCKET_REQUEST_FINISHED_COUNT] = 0;
        installListeners(this, socket, options);
    }
    createConnection(options, oncreate) {
        let called = false;
        const onNewCreate = (err, socket)=>{
            if (called) return;
            called = true;
            if (err) {
                this.createSocketErrorCount++;
                return oncreate(err);
            }
            this[INIT_SOCKET](socket, options);
            oncreate(err, socket);
        };
        const newSocket = super.createConnection(options, onNewCreate);
        if (newSocket) onNewCreate(null, newSocket);
        return newSocket;
    }
    get statusChanged() {
        const changed = this.createSocketCount !== this.createSocketCountLastCheck || this.createSocketErrorCount !== this.createSocketErrorCountLastCheck || this.closeSocketCount !== this.closeSocketCountLastCheck || this.errorSocketCount !== this.errorSocketCountLastCheck || this.timeoutSocketCount !== this.timeoutSocketCountLastCheck || this.requestCount !== this.requestCountLastCheck;
        if (changed) {
            this.createSocketCountLastCheck = this.createSocketCount;
            this.createSocketErrorCountLastCheck = this.createSocketErrorCount;
            this.closeSocketCountLastCheck = this.closeSocketCount;
            this.errorSocketCountLastCheck = this.errorSocketCount;
            this.timeoutSocketCountLastCheck = this.timeoutSocketCount;
            this.requestCountLastCheck = this.requestCount;
        }
        return changed;
    }
    getCurrentStatus() {
        return {
            createSocketCount: this.createSocketCount,
            createSocketErrorCount: this.createSocketErrorCount,
            closeSocketCount: this.closeSocketCount,
            errorSocketCount: this.errorSocketCount,
            timeoutSocketCount: this.timeoutSocketCount,
            requestCount: this.requestCount,
            freeSockets: inspect(this.freeSockets),
            sockets: inspect(this.sockets),
            requests: inspect(this.requests)
        };
    }
}
// node 8 don't has timeout attribute on socket
// https://github.com/nodejs/node/pull/21204/files#diff-e6ef024c3775d787c38487a6309e491dR408
function getSocketTimeout(socket) {
    return socket.timeout || socket._idleTimeout;
}
function installListeners(agent, socket, options) {
    debug('%s create, timeout %sms', socket[SOCKET_NAME], getSocketTimeout(socket));
    // listener socket events: close, timeout, error, free
    function onFree() {
        // create and socket.emit('free') logic
        // https://github.com/nodejs/node/blob/master/lib/_http_agent.js#L311
        // no req on the socket, it should be the new socket
        if (!socket._httpMessage && socket[SOCKET_REQUEST_COUNT] === 1) return;
        socket[SOCKET_REQUEST_FINISHED_COUNT]++;
        agent.requestCount++;
        debug('%s(requests: %s, finished: %s) free', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);
        // should reuse on pedding requests?
        const name = agent.getName(options);
        if (socket.writable && agent.requests[name] && agent.requests[name].length) {
            // will be reuse on agent free listener
            socket[SOCKET_REQUEST_COUNT]++;
            debug('%s(requests: %s, finished: %s) will be reuse on agent free event', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);
        }
    }
    socket.on('free', onFree);
    function onClose(isError) {
        debug('%s(requests: %s, finished: %s) close, isError: %s', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], isError);
        agent.closeSocketCount++;
    }
    socket.on('close', onClose);
    // start socket timeout handler
    function onTimeout() {
        // onTimeout and emitRequestTimeout(_http_client.js)
        // https://github.com/nodejs/node/blob/v12.x/lib/_http_client.js#L711
        const listenerCount = socket.listeners('timeout').length;
        // node <= 10, default listenerCount is 1, onTimeout
        // 11 < node <= 12, default listenerCount is 2, onTimeout and emitRequestTimeout
        // node >= 13, default listenerCount is 3, onTimeout,
        //   onTimeout(https://github.com/nodejs/node/pull/32000/files#diff-5f7fb0850412c6be189faeddea6c5359R333)
        //   and emitRequestTimeout
        const timeout = getSocketTimeout(socket);
        const req = socket._httpMessage;
        const reqTimeoutListenerCount = req && req.listeners('timeout').length || 0;
        debug('%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], timeout, listenerCount, defaultTimeoutListenerCount, !!req, reqTimeoutListenerCount);
        if (debug.enabled) {
            debug('timeout listeners: %s', socket.listeners('timeout').map((f)=>f.name).join(', '));
        }
        agent.timeoutSocketCount++;
        const name = agent.getName(options);
        if (agent.freeSockets[name] && agent.freeSockets[name].indexOf(socket) !== -1) {
            // free socket timeout, destroy quietly
            socket.destroy();
            // Remove it from freeSockets list immediately to prevent new requests
            // from being sent through this socket.
            agent.removeSocket(socket, options);
            debug('%s is free, destroy quietly', socket[SOCKET_NAME]);
        } else {
            // if there is no any request socket timeout handler,
            // agent need to handle socket timeout itself.
            //
            // custom request socket timeout handle logic must follow these rules:
            //  1. Destroy socket first
            //  2. Must emit socket 'agentRemove' event tell agent remove socket
            //     from freeSockets list immediately.
            //     Otherise you may be get 'socket hang up' error when reuse
            //     free socket and timeout happen in the same time.
            if (reqTimeoutListenerCount === 0) {
                const error = new Error('Socket timeout');
                error.code = 'ERR_SOCKET_TIMEOUT';
                error.timeout = timeout;
                // must manually call socket.end() or socket.destroy() to end the connection.
                // https://nodejs.org/dist/latest-v10.x/docs/api/net.html#net_socket_settimeout_timeout_callback
                socket.destroy(error);
                agent.removeSocket(socket, options);
                debug('%s destroy with timeout error', socket[SOCKET_NAME]);
            }
        }
    }
    socket.on('timeout', onTimeout);
    function onError(err) {
        const listenerCount = socket.listeners('error').length;
        debug('%s(requests: %s, finished: %s) error: %s, listenerCount: %s', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], err, listenerCount);
        agent.errorSocketCount++;
        if (listenerCount === 1) {
            // if socket don't contain error event handler, don't catch it, emit it again
            debug('%s emit uncaught error event', socket[SOCKET_NAME]);
            socket.removeListener('error', onError);
            socket.emit('error', err);
        }
    }
    socket.on('error', onError);
    function onRemove() {
        debug('%s(requests: %s, finished: %s) agentRemove', socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);
        // We need this function for cases like HTTP 'upgrade'
        // (defined by WebSockets) where we need to remove a socket from the
        // pool because it'll be locked up indefinitely
        socket.removeListener('close', onClose);
        socket.removeListener('error', onError);
        socket.removeListener('free', onFree);
        socket.removeListener('timeout', onTimeout);
        socket.removeListener('agentRemove', onRemove);
    }
    socket.on('agentRemove', onRemove);
}
module.exports = Agent;
function inspect(obj) {
    const res = {};
    for(const key in obj){
        res[key] = obj[key].length;
    }
    return res;
}
}}),
"[project]/node_modules/agentkeepalive/lib/https_agent.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const OriginalHttpsAgent = __turbopack_context__.r("[externals]/https [external] (https, cjs)").Agent;
const HttpAgent = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/agent.js [app-route] (ecmascript)");
const { INIT_SOCKET, CREATE_HTTPS_CONNECTION } = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/constants.js [app-route] (ecmascript)");
class HttpsAgent extends HttpAgent {
    constructor(options){
        super(options);
        this.defaultPort = 443;
        this.protocol = 'https:';
        this.maxCachedSessions = this.options.maxCachedSessions;
        /* istanbul ignore next */ if (this.maxCachedSessions === undefined) {
            this.maxCachedSessions = 100;
        }
        this._sessionCache = {
            map: {},
            list: []
        };
    }
    createConnection(options, oncreate) {
        const socket = this[CREATE_HTTPS_CONNECTION](options, oncreate);
        this[INIT_SOCKET](socket, options);
        return socket;
    }
}
// https://github.com/nodejs/node/blob/master/lib/https.js#L89
HttpsAgent.prototype[CREATE_HTTPS_CONNECTION] = OriginalHttpsAgent.prototype.createConnection;
[
    'getName',
    '_getSession',
    '_cacheSession',
    // https://github.com/nodejs/node/pull/4982
    '_evictSession'
].forEach(function(method) {
    /* istanbul ignore next */ if (typeof OriginalHttpsAgent.prototype[method] === 'function') {
        HttpsAgent.prototype[method] = OriginalHttpsAgent.prototype[method];
    }
});
module.exports = HttpsAgent;
}}),
"[project]/node_modules/agentkeepalive/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
const HttpAgent = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/agent.js [app-route] (ecmascript)");
module.exports = HttpAgent;
module.exports.HttpAgent = HttpAgent;
module.exports.HttpsAgent = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/https_agent.js [app-route] (ecmascript)");
module.exports.constants = __turbopack_context__.r("[project]/node_modules/agentkeepalive/lib/constants.js [app-route] (ecmascript)");
}}),
"[project]/node_modules/event-target-shim/dist/event-target-shim.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * <AUTHOR> Nagashima <https://github.com/mysticatea>
 * @copyright 2015 Toru Nagashima. All rights reserved.
 * See LICENSE file in root directory for full license.
 */ 'use strict';
Object.defineProperty(exports, '__esModule', {
    value: true
});
/**
 * @typedef {object} PrivateData
 * @property {EventTarget} eventTarget The event target.
 * @property {{type:string}} event The original event object.
 * @property {number} eventPhase The current event phase.
 * @property {EventTarget|null} currentTarget The current event target.
 * @property {boolean} canceled The flag to prevent default.
 * @property {boolean} stopped The flag to stop propagation.
 * @property {boolean} immediateStopped The flag to stop propagation immediately.
 * @property {Function|null} passiveListener The listener if the current listener is passive. Otherwise this is null.
 * @property {number} timeStamp The unix time.
 * @private
 */ /**
 * Private data for event wrappers.
 * @type {WeakMap<Event, PrivateData>}
 * @private
 */ const privateData = new WeakMap();
/**
 * Cache for wrapper classes.
 * @type {WeakMap<Object, Function>}
 * @private
 */ const wrappers = new WeakMap();
/**
 * Get private data.
 * @param {Event} event The event object to get private data.
 * @returns {PrivateData} The private data of the event.
 * @private
 */ function pd(event) {
    const retv = privateData.get(event);
    console.assert(retv != null, "'this' is expected an Event object, but got", event);
    return retv;
}
/**
 * https://dom.spec.whatwg.org/#set-the-canceled-flag
 * @param data {PrivateData} private data.
 */ function setCancelFlag(data) {
    if (data.passiveListener != null) {
        if (typeof console !== "undefined" && typeof console.error === "function") {
            console.error("Unable to preventDefault inside passive event listener invocation.", data.passiveListener);
        }
        return;
    }
    if (!data.event.cancelable) {
        return;
    }
    data.canceled = true;
    if (typeof data.event.preventDefault === "function") {
        data.event.preventDefault();
    }
}
/**
 * @see https://dom.spec.whatwg.org/#interface-event
 * @private
 */ /**
 * The event wrapper.
 * @constructor
 * @param {EventTarget} eventTarget The event target of this dispatching.
 * @param {Event|{type:string}} event The original event to wrap.
 */ function Event(eventTarget, event) {
    privateData.set(this, {
        eventTarget,
        event,
        eventPhase: 2,
        currentTarget: eventTarget,
        canceled: false,
        stopped: false,
        immediateStopped: false,
        passiveListener: null,
        timeStamp: event.timeStamp || Date.now()
    });
    // https://heycam.github.io/webidl/#Unforgeable
    Object.defineProperty(this, "isTrusted", {
        value: false,
        enumerable: true
    });
    // Define accessors
    const keys = Object.keys(event);
    for(let i = 0; i < keys.length; ++i){
        const key = keys[i];
        if (!(key in this)) {
            Object.defineProperty(this, key, defineRedirectDescriptor(key));
        }
    }
}
// Should be enumerable, but class methods are not enumerable.
Event.prototype = {
    /**
     * The type of this event.
     * @type {string}
     */ get type () {
        return pd(this).event.type;
    },
    /**
     * The target of this event.
     * @type {EventTarget}
     */ get target () {
        return pd(this).eventTarget;
    },
    /**
     * The target of this event.
     * @type {EventTarget}
     */ get currentTarget () {
        return pd(this).currentTarget;
    },
    /**
     * @returns {EventTarget[]} The composed path of this event.
     */ composedPath () {
        const currentTarget = pd(this).currentTarget;
        if (currentTarget == null) {
            return [];
        }
        return [
            currentTarget
        ];
    },
    /**
     * Constant of NONE.
     * @type {number}
     */ get NONE () {
        return 0;
    },
    /**
     * Constant of CAPTURING_PHASE.
     * @type {number}
     */ get CAPTURING_PHASE () {
        return 1;
    },
    /**
     * Constant of AT_TARGET.
     * @type {number}
     */ get AT_TARGET () {
        return 2;
    },
    /**
     * Constant of BUBBLING_PHASE.
     * @type {number}
     */ get BUBBLING_PHASE () {
        return 3;
    },
    /**
     * The target of this event.
     * @type {number}
     */ get eventPhase () {
        return pd(this).eventPhase;
    },
    /**
     * Stop event bubbling.
     * @returns {void}
     */ stopPropagation () {
        const data = pd(this);
        data.stopped = true;
        if (typeof data.event.stopPropagation === "function") {
            data.event.stopPropagation();
        }
    },
    /**
     * Stop event bubbling.
     * @returns {void}
     */ stopImmediatePropagation () {
        const data = pd(this);
        data.stopped = true;
        data.immediateStopped = true;
        if (typeof data.event.stopImmediatePropagation === "function") {
            data.event.stopImmediatePropagation();
        }
    },
    /**
     * The flag to be bubbling.
     * @type {boolean}
     */ get bubbles () {
        return Boolean(pd(this).event.bubbles);
    },
    /**
     * The flag to be cancelable.
     * @type {boolean}
     */ get cancelable () {
        return Boolean(pd(this).event.cancelable);
    },
    /**
     * Cancel this event.
     * @returns {void}
     */ preventDefault () {
        setCancelFlag(pd(this));
    },
    /**
     * The flag to indicate cancellation state.
     * @type {boolean}
     */ get defaultPrevented () {
        return pd(this).canceled;
    },
    /**
     * The flag to be composed.
     * @type {boolean}
     */ get composed () {
        return Boolean(pd(this).event.composed);
    },
    /**
     * The unix time of this event.
     * @type {number}
     */ get timeStamp () {
        return pd(this).timeStamp;
    },
    /**
     * The target of this event.
     * @type {EventTarget}
     * @deprecated
     */ get srcElement () {
        return pd(this).eventTarget;
    },
    /**
     * The flag to stop event bubbling.
     * @type {boolean}
     * @deprecated
     */ get cancelBubble () {
        return pd(this).stopped;
    },
    set cancelBubble (value){
        if (!value) {
            return;
        }
        const data = pd(this);
        data.stopped = true;
        if (typeof data.event.cancelBubble === "boolean") {
            data.event.cancelBubble = true;
        }
    },
    /**
     * The flag to indicate cancellation state.
     * @type {boolean}
     * @deprecated
     */ get returnValue () {
        return !pd(this).canceled;
    },
    set returnValue (value){
        if (!value) {
            setCancelFlag(pd(this));
        }
    },
    /**
     * Initialize this event object. But do nothing under event dispatching.
     * @param {string} type The event type.
     * @param {boolean} [bubbles=false] The flag to be possible to bubble up.
     * @param {boolean} [cancelable=false] The flag to be possible to cancel.
     * @deprecated
     */ initEvent () {
    // Do nothing.
    }
};
// `constructor` is not enumerable.
Object.defineProperty(Event.prototype, "constructor", {
    value: Event,
    configurable: true,
    writable: true
});
// Ensure `event instanceof window.Event` is `true`.
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Get the property descriptor to redirect a given property.
 * @param {string} key Property name to define property descriptor.
 * @returns {PropertyDescriptor} The property descriptor to redirect the property.
 * @private
 */ function defineRedirectDescriptor(key) {
    return {
        get () {
            return pd(this).event[key];
        },
        set (value1) {
            pd(this).event[key] = value1;
        },
        configurable: true,
        enumerable: true
    };
}
/**
 * Get the property descriptor to call a given method property.
 * @param {string} key Property name to define property descriptor.
 * @returns {PropertyDescriptor} The property descriptor to call the method property.
 * @private
 */ function defineCallDescriptor(key) {
    return {
        value () {
            const event = pd(this).event;
            return event[key].apply(event, arguments);
        },
        configurable: true,
        enumerable: true
    };
}
/**
 * Define new wrapper class.
 * @param {Function} BaseEvent The base wrapper class.
 * @param {Object} proto The prototype of the original event.
 * @returns {Function} The defined wrapper class.
 * @private
 */ function defineWrapper(BaseEvent, proto) {
    const keys = Object.keys(proto);
    if (keys.length === 0) {
        return BaseEvent;
    }
    /** CustomEvent */ function CustomEvent(eventTarget, event) {
        BaseEvent.call(this, eventTarget, event);
    }
    CustomEvent.prototype = Object.create(BaseEvent.prototype, {
        constructor: {
            value: CustomEvent,
            configurable: true,
            writable: true
        }
    });
    // Define accessors.
    for(let i = 0; i < keys.length; ++i){
        const key = keys[i];
        if (!(key in BaseEvent.prototype)) {
            const descriptor = Object.getOwnPropertyDescriptor(proto, key);
            const isFunc = typeof descriptor.value === "function";
            Object.defineProperty(CustomEvent.prototype, key, isFunc ? defineCallDescriptor(key) : defineRedirectDescriptor(key));
        }
    }
    return CustomEvent;
}
/**
 * Get the wrapper class of a given prototype.
 * @param {Object} proto The prototype of the original event to get its wrapper.
 * @returns {Function} The wrapper class.
 * @private
 */ function getWrapper(proto) {
    if (proto == null || proto === Object.prototype) {
        return Event;
    }
    let wrapper = wrappers.get(proto);
    if (wrapper == null) {
        wrapper = defineWrapper(getWrapper(Object.getPrototypeOf(proto)), proto);
        wrappers.set(proto, wrapper);
    }
    return wrapper;
}
/**
 * Wrap a given event to management a dispatching.
 * @param {EventTarget} eventTarget The event target of this dispatching.
 * @param {Object} event The event to wrap.
 * @returns {Event} The wrapper instance.
 * @private
 */ function wrapEvent(eventTarget, event) {
    const Wrapper = getWrapper(Object.getPrototypeOf(event));
    return new Wrapper(eventTarget, event);
}
/**
 * Get the immediateStopped flag of a given event.
 * @param {Event} event The event to get.
 * @returns {boolean} The flag to stop propagation immediately.
 * @private
 */ function isStopped(event) {
    return pd(event).immediateStopped;
}
/**
 * Set the current event phase of a given event.
 * @param {Event} event The event to set current target.
 * @param {number} eventPhase New event phase.
 * @returns {void}
 * @private
 */ function setEventPhase(event, eventPhase) {
    pd(event).eventPhase = eventPhase;
}
/**
 * Set the current target of a given event.
 * @param {Event} event The event to set current target.
 * @param {EventTarget|null} currentTarget New current target.
 * @returns {void}
 * @private
 */ function setCurrentTarget(event, currentTarget) {
    pd(event).currentTarget = currentTarget;
}
/**
 * Set a passive listener of a given event.
 * @param {Event} event The event to set current target.
 * @param {Function|null} passiveListener New passive listener.
 * @returns {void}
 * @private
 */ function setPassiveListener(event, passiveListener) {
    pd(event).passiveListener = passiveListener;
}
/**
 * @typedef {object} ListenerNode
 * @property {Function} listener
 * @property {1|2|3} listenerType
 * @property {boolean} passive
 * @property {boolean} once
 * @property {ListenerNode|null} next
 * @private
 */ /**
 * @type {WeakMap<object, Map<string, ListenerNode>>}
 * @private
 */ const listenersMap = new WeakMap();
// Listener types
const CAPTURE = 1;
const BUBBLE = 2;
const ATTRIBUTE = 3;
/**
 * Check whether a given value is an object or not.
 * @param {any} x The value to check.
 * @returns {boolean} `true` if the value is an object.
 */ function isObject(x) {
    return x !== null && typeof x === "object" //eslint-disable-line no-restricted-syntax
    ;
}
/**
 * Get listeners.
 * @param {EventTarget} eventTarget The event target to get.
 * @returns {Map<string, ListenerNode>} The listeners.
 * @private
 */ function getListeners(eventTarget) {
    const listeners = listenersMap.get(eventTarget);
    if (listeners == null) {
        throw new TypeError("'this' is expected an EventTarget object, but got another value.");
    }
    return listeners;
}
/**
 * Get the property descriptor for the event attribute of a given event.
 * @param {string} eventName The event name to get property descriptor.
 * @returns {PropertyDescriptor} The property descriptor.
 * @private
 */ function defineEventAttributeDescriptor(eventName) {
    return {
        get () {
            const listeners = getListeners(this);
            let node = listeners.get(eventName);
            while(node != null){
                if (node.listenerType === ATTRIBUTE) {
                    return node.listener;
                }
                node = node.next;
            }
            return null;
        },
        set (listener) {
            if (typeof listener !== "function" && !isObject(listener)) {
                listener = null; // eslint-disable-line no-param-reassign
            }
            const listeners = getListeners(this);
            // Traverse to the tail while removing old value.
            let prev = null;
            let node = listeners.get(eventName);
            while(node != null){
                if (node.listenerType === ATTRIBUTE) {
                    // Remove old value.
                    if (prev !== null) {
                        prev.next = node.next;
                    } else if (node.next !== null) {
                        listeners.set(eventName, node.next);
                    } else {
                        listeners.delete(eventName);
                    }
                } else {
                    prev = node;
                }
                node = node.next;
            }
            // Add new value.
            if (listener !== null) {
                const newNode = {
                    listener,
                    listenerType: ATTRIBUTE,
                    passive: false,
                    once: false,
                    next: null
                };
                if (prev === null) {
                    listeners.set(eventName, newNode);
                } else {
                    prev.next = newNode;
                }
            }
        },
        configurable: true,
        enumerable: true
    };
}
/**
 * Define an event attribute (e.g. `eventTarget.onclick`).
 * @param {Object} eventTargetPrototype The event target prototype to define an event attrbite.
 * @param {string} eventName The event name to define.
 * @returns {void}
 */ function defineEventAttribute(eventTargetPrototype, eventName) {
    Object.defineProperty(eventTargetPrototype, `on${eventName}`, defineEventAttributeDescriptor(eventName));
}
/**
 * Define a custom EventTarget with event attributes.
 * @param {string[]} eventNames Event names for event attributes.
 * @returns {EventTarget} The custom EventTarget.
 * @private
 */ function defineCustomEventTarget(eventNames) {
    /** CustomEventTarget */ function CustomEventTarget() {
        EventTarget.call(this);
    }
    CustomEventTarget.prototype = Object.create(EventTarget.prototype, {
        constructor: {
            value: CustomEventTarget,
            configurable: true,
            writable: true
        }
    });
    for(let i = 0; i < eventNames.length; ++i){
        defineEventAttribute(CustomEventTarget.prototype, eventNames[i]);
    }
    return CustomEventTarget;
}
/**
 * EventTarget.
 *
 * - This is constructor if no arguments.
 * - This is a function which returns a CustomEventTarget constructor if there are arguments.
 *
 * For example:
 *
 *     class A extends EventTarget {}
 *     class B extends EventTarget("message") {}
 *     class C extends EventTarget("message", "error") {}
 *     class D extends EventTarget(["message", "error"]) {}
 */ function EventTarget() {
    /*eslint-disable consistent-return */ if (this instanceof EventTarget) {
        listenersMap.set(this, new Map());
        return;
    }
    if (arguments.length === 1 && Array.isArray(arguments[0])) {
        return defineCustomEventTarget(arguments[0]);
    }
    if (arguments.length > 0) {
        const types = new Array(arguments.length);
        for(let i = 0; i < arguments.length; ++i){
            types[i] = arguments[i];
        }
        return defineCustomEventTarget(types);
    }
    throw new TypeError("Cannot call a class as a function");
/*eslint-enable consistent-return */ }
// Should be enumerable, but class methods are not enumerable.
EventTarget.prototype = {
    /**
     * Add a given listener to this event target.
     * @param {string} eventName The event name to add.
     * @param {Function} listener The listener to add.
     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.
     * @returns {void}
     */ addEventListener (eventName, listener, options) {
        if (listener == null) {
            return;
        }
        if (typeof listener !== "function" && !isObject(listener)) {
            throw new TypeError("'listener' should be a function or an object.");
        }
        const listeners = getListeners(this);
        const optionsIsObj = isObject(options);
        const capture = optionsIsObj ? Boolean(options.capture) : Boolean(options);
        const listenerType = capture ? CAPTURE : BUBBLE;
        const newNode = {
            listener,
            listenerType,
            passive: optionsIsObj && Boolean(options.passive),
            once: optionsIsObj && Boolean(options.once),
            next: null
        };
        // Set it as the first node if the first node is null.
        let node = listeners.get(eventName);
        if (node === undefined) {
            listeners.set(eventName, newNode);
            return;
        }
        // Traverse to the tail while checking duplication..
        let prev = null;
        while(node != null){
            if (node.listener === listener && node.listenerType === listenerType) {
                // Should ignore duplication.
                return;
            }
            prev = node;
            node = node.next;
        }
        // Add it.
        prev.next = newNode;
    },
    /**
     * Remove a given listener from this event target.
     * @param {string} eventName The event name to remove.
     * @param {Function} listener The listener to remove.
     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.
     * @returns {void}
     */ removeEventListener (eventName, listener, options) {
        if (listener == null) {
            return;
        }
        const listeners = getListeners(this);
        const capture = isObject(options) ? Boolean(options.capture) : Boolean(options);
        const listenerType = capture ? CAPTURE : BUBBLE;
        let prev = null;
        let node = listeners.get(eventName);
        while(node != null){
            if (node.listener === listener && node.listenerType === listenerType) {
                if (prev !== null) {
                    prev.next = node.next;
                } else if (node.next !== null) {
                    listeners.set(eventName, node.next);
                } else {
                    listeners.delete(eventName);
                }
                return;
            }
            prev = node;
            node = node.next;
        }
    },
    /**
     * Dispatch a given event.
     * @param {Event|{type:string}} event The event to dispatch.
     * @returns {boolean} `false` if canceled.
     */ dispatchEvent (event) {
        if (event == null || typeof event.type !== "string") {
            throw new TypeError('"event.type" should be a string.');
        }
        // If listeners aren't registered, terminate.
        const listeners = getListeners(this);
        const eventName = event.type;
        let node = listeners.get(eventName);
        if (node == null) {
            return true;
        }
        // Since we cannot rewrite several properties, so wrap object.
        const wrappedEvent = wrapEvent(this, event);
        // This doesn't process capturing phase and bubbling phase.
        // This isn't participating in a tree.
        let prev = null;
        while(node != null){
            // Remove this listener if it's once
            if (node.once) {
                if (prev !== null) {
                    prev.next = node.next;
                } else if (node.next !== null) {
                    listeners.set(eventName, node.next);
                } else {
                    listeners.delete(eventName);
                }
            } else {
                prev = node;
            }
            // Call this listener
            setPassiveListener(wrappedEvent, node.passive ? node.listener : null);
            if (typeof node.listener === "function") {
                try {
                    node.listener.call(this, wrappedEvent);
                } catch (err) {
                    if (typeof console !== "undefined" && typeof console.error === "function") {
                        console.error(err);
                    }
                }
            } else if (node.listenerType !== ATTRIBUTE && typeof node.listener.handleEvent === "function") {
                node.listener.handleEvent(wrappedEvent);
            }
            // Break if `event.stopImmediatePropagation` was called.
            if (isStopped(wrappedEvent)) {
                break;
            }
            node = node.next;
        }
        setPassiveListener(wrappedEvent, null);
        setEventPhase(wrappedEvent, 0);
        setCurrentTarget(wrappedEvent, null);
        return !wrappedEvent.defaultPrevented;
    }
};
// `constructor` is not enumerable.
Object.defineProperty(EventTarget.prototype, "constructor", {
    value: EventTarget,
    configurable: true,
    writable: true
});
// Ensure `eventTarget instanceof window.EventTarget` is `true`.
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
exports.defineEventAttribute = defineEventAttribute;
exports.EventTarget = EventTarget;
exports.default = EventTarget;
module.exports = EventTarget;
module.exports.EventTarget = module.exports["default"] = EventTarget;
module.exports.defineEventAttribute = defineEventAttribute //# sourceMappingURL=event-target-shim.js.map
;
}}),
"[project]/node_modules/abort-controller/dist/abort-controller.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * <AUTHOR> Nagashima <https://github.com/mysticatea>
 * See LICENSE file in root directory for full license.
 */ 'use strict';
Object.defineProperty(exports, '__esModule', {
    value: true
});
var eventTargetShim = __turbopack_context__.r("[project]/node_modules/event-target-shim/dist/event-target-shim.js [app-route] (ecmascript)");
/**
 * The signal class.
 * @see https://dom.spec.whatwg.org/#abortsignal
 */ class AbortSignal extends eventTargetShim.EventTarget {
    /**
     * AbortSignal cannot be constructed directly.
     */ constructor(){
        super();
        throw new TypeError("AbortSignal cannot be constructed directly");
    }
    /**
     * Returns `true` if this `AbortSignal`'s `AbortController` has signaled to abort, and `false` otherwise.
     */ get aborted() {
        const aborted = abortedFlags.get(this);
        if (typeof aborted !== "boolean") {
            throw new TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this === null ? "null" : typeof this}`);
        }
        return aborted;
    }
}
eventTargetShim.defineEventAttribute(AbortSignal.prototype, "abort");
/**
 * Create an AbortSignal object.
 */ function createAbortSignal() {
    const signal = Object.create(AbortSignal.prototype);
    eventTargetShim.EventTarget.call(signal);
    abortedFlags.set(signal, false);
    return signal;
}
/**
 * Abort a given signal.
 */ function abortSignal(signal) {
    if (abortedFlags.get(signal) !== false) {
        return;
    }
    abortedFlags.set(signal, true);
    signal.dispatchEvent({
        type: "abort"
    });
}
/**
 * Aborted flag for each instances.
 */ const abortedFlags = new WeakMap();
// Properties should be enumerable.
Object.defineProperties(AbortSignal.prototype, {
    aborted: {
        enumerable: true
    }
});
// `toString()` should return `"[object AbortSignal]"`
if (typeof Symbol === "function" && typeof Symbol.toStringTag === "symbol") {
    Object.defineProperty(AbortSignal.prototype, Symbol.toStringTag, {
        configurable: true,
        value: "AbortSignal"
    });
}
/**
 * The AbortController.
 * @see https://dom.spec.whatwg.org/#abortcontroller
 */ class AbortController {
    /**
     * Initialize this controller.
     */ constructor(){
        signals.set(this, createAbortSignal());
    }
    /**
     * Returns the `AbortSignal` object associated with this object.
     */ get signal() {
        return getSignal(this);
    }
    /**
     * Abort and signal to any observers that the associated activity is to be aborted.
     */ abort() {
        abortSignal(getSignal(this));
    }
}
/**
 * Associated signals.
 */ const signals = new WeakMap();
/**
 * Get the associated signal of a given controller.
 */ function getSignal(controller) {
    const signal = signals.get(controller);
    if (signal == null) {
        throw new TypeError(`Expected 'this' to be an 'AbortController' object, but got ${controller === null ? "null" : typeof controller}`);
    }
    return signal;
}
// Properties should be enumerable.
Object.defineProperties(AbortController.prototype, {
    signal: {
        enumerable: true
    },
    abort: {
        enumerable: true
    }
});
if (typeof Symbol === "function" && typeof Symbol.toStringTag === "symbol") {
    Object.defineProperty(AbortController.prototype, Symbol.toStringTag, {
        configurable: true,
        value: "AbortController"
    });
}
exports.AbortController = AbortController;
exports.AbortSignal = AbortSignal;
exports.default = AbortController;
module.exports = AbortController;
module.exports.AbortController = module.exports["default"] = AbortController;
module.exports.AbortSignal = AbortSignal //# sourceMappingURL=abort-controller.js.map
;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/createBoundary.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const alphabet = "abcdefghijklmnopqrstuvwxyz0123456789";
function createBoundary() {
    let size = 16;
    let res = "";
    while(size--){
        res += alphabet[Math.random() * alphabet.length << 0];
    }
    return res;
}
const __TURBOPACK__default__export__ = createBoundary;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/isPlainObject.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const getType = (value)=>Object.prototype.toString.call(value).slice(8, -1).toLowerCase();
function isPlainObject(value) {
    if (getType(value) !== "object") {
        return false;
    }
    const pp = Object.getPrototypeOf(value);
    if (pp === null || pp === undefined) {
        return true;
    }
    const Ctor = pp.constructor && pp.constructor.toString();
    return Ctor === Object.toString();
}
const __TURBOPACK__default__export__ = isPlainObject;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/normalizeValue.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const normalizeValue = (value)=>String(value).replace(/\r|\n/g, (match, i, str)=>{
        if (match === "\r" && str[i + 1] !== "\n" || match === "\n" && str[i - 1] !== "\r") {
            return "\r\n";
        }
        return match;
    });
const __TURBOPACK__default__export__ = normalizeValue;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/escapeName.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const escapeName = (name)=>String(name).replace(/\r/g, "%0D").replace(/\n/g, "%0A").replace(/"/g, "%22");
const __TURBOPACK__default__export__ = escapeName;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/isFunction.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const isFunction = (value)=>typeof value === "function";
const __TURBOPACK__default__export__ = isFunction;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/isFileLike.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isFileLike": (()=>isFileLike)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFunction.js [app-route] (ecmascript)");
;
const isFileLike = (value)=>Boolean(value && typeof value === "object" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.constructor) && value[Symbol.toStringTag] === "File" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.stream) && value.name != null && value.size != null && value.lastModified != null);
}}),
"[project]/node_modules/form-data-encoder/lib/esm/util/isFormData.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isFormData": (()=>isFormData),
    "isFormDataLike": (()=>isFormDataLike)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFunction.js [app-route] (ecmascript)");
;
const isFormData = (value)=>Boolean(value && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.constructor) && value[Symbol.toStringTag] === "FormData" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.append) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.getAll) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.entries) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFunction$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value[Symbol.iterator]));
const isFormDataLike = isFormData;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/FormDataEncoder.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Encoder": (()=>Encoder),
    "FormDataEncoder": (()=>FormDataEncoder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$createBoundary$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/createBoundary.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isPlainObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isPlainObject.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$normalizeValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/normalizeValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$escapeName$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/escapeName.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFileLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFormData.js [app-route] (ecmascript)");
var __classPrivateFieldSet = this && this.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _FormDataEncoder_instances, _FormDataEncoder_CRLF, _FormDataEncoder_CRLF_BYTES, _FormDataEncoder_CRLF_BYTES_LENGTH, _FormDataEncoder_DASHES, _FormDataEncoder_encoder, _FormDataEncoder_footer, _FormDataEncoder_form, _FormDataEncoder_options, _FormDataEncoder_getFieldHeader;
;
;
;
;
;
;
const defaultOptions = {
    enableAdditionalHeaders: false
};
class FormDataEncoder {
    constructor(form, boundaryOrOptions, options){
        _FormDataEncoder_instances.add(this);
        _FormDataEncoder_CRLF.set(this, "\r\n");
        _FormDataEncoder_CRLF_BYTES.set(this, void 0);
        _FormDataEncoder_CRLF_BYTES_LENGTH.set(this, void 0);
        _FormDataEncoder_DASHES.set(this, "-".repeat(2));
        _FormDataEncoder_encoder.set(this, new TextEncoder());
        _FormDataEncoder_footer.set(this, void 0);
        _FormDataEncoder_form.set(this, void 0);
        _FormDataEncoder_options.set(this, void 0);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFormData"])(form)) {
            throw new TypeError("Expected first argument to be a FormData instance.");
        }
        let boundary;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isPlainObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(boundaryOrOptions)) {
            options = boundaryOrOptions;
        } else {
            boundary = boundaryOrOptions;
        }
        if (!boundary) {
            boundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$createBoundary$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        }
        if (typeof boundary !== "string") {
            throw new TypeError("Expected boundary argument to be a string.");
        }
        if (options && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isPlainObject$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(options)) {
            throw new TypeError("Expected options argument to be an object.");
        }
        __classPrivateFieldSet(this, _FormDataEncoder_form, form, "f");
        __classPrivateFieldSet(this, _FormDataEncoder_options, {
            ...defaultOptions,
            ...options
        }, "f");
        __classPrivateFieldSet(this, _FormDataEncoder_CRLF_BYTES, __classPrivateFieldGet(this, _FormDataEncoder_encoder, "f").encode(__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f")), "f");
        __classPrivateFieldSet(this, _FormDataEncoder_CRLF_BYTES_LENGTH, __classPrivateFieldGet(this, _FormDataEncoder_CRLF_BYTES, "f").byteLength, "f");
        this.boundary = `form-data-boundary-${boundary}`;
        this.contentType = `multipart/form-data; boundary=${this.boundary}`;
        __classPrivateFieldSet(this, _FormDataEncoder_footer, __classPrivateFieldGet(this, _FormDataEncoder_encoder, "f").encode(`${__classPrivateFieldGet(this, _FormDataEncoder_DASHES, "f")}${this.boundary}${__classPrivateFieldGet(this, _FormDataEncoder_DASHES, "f")}${__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f").repeat(2)}`), "f");
        this.contentLength = String(this.getContentLength());
        this.headers = Object.freeze({
            "Content-Type": this.contentType,
            "Content-Length": this.contentLength
        });
        Object.defineProperties(this, {
            boundary: {
                writable: false,
                configurable: false
            },
            contentType: {
                writable: false,
                configurable: false
            },
            contentLength: {
                writable: false,
                configurable: false
            },
            headers: {
                writable: false,
                configurable: false
            }
        });
    }
    getContentLength() {
        let length = 0;
        for (const [name, raw] of __classPrivateFieldGet(this, _FormDataEncoder_form, "f")){
            const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(raw) ? raw : __classPrivateFieldGet(this, _FormDataEncoder_encoder, "f").encode((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$normalizeValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(raw));
            length += __classPrivateFieldGet(this, _FormDataEncoder_instances, "m", _FormDataEncoder_getFieldHeader).call(this, name, value).byteLength;
            length += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(value) ? value.size : value.byteLength;
            length += __classPrivateFieldGet(this, _FormDataEncoder_CRLF_BYTES_LENGTH, "f");
        }
        return length + __classPrivateFieldGet(this, _FormDataEncoder_footer, "f").byteLength;
    }
    *values() {
        for (const [name, raw] of __classPrivateFieldGet(this, _FormDataEncoder_form, "f").entries()){
            const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(raw) ? raw : __classPrivateFieldGet(this, _FormDataEncoder_encoder, "f").encode((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$normalizeValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(raw));
            yield __classPrivateFieldGet(this, _FormDataEncoder_instances, "m", _FormDataEncoder_getFieldHeader).call(this, name, value);
            yield value;
            yield __classPrivateFieldGet(this, _FormDataEncoder_CRLF_BYTES, "f");
        }
        yield __classPrivateFieldGet(this, _FormDataEncoder_footer, "f");
    }
    async *encode() {
        for (const part of this.values()){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(part)) {
                yield* part.stream();
            } else {
                yield part;
            }
        }
    }
    [(_FormDataEncoder_CRLF = new WeakMap(), _FormDataEncoder_CRLF_BYTES = new WeakMap(), _FormDataEncoder_CRLF_BYTES_LENGTH = new WeakMap(), _FormDataEncoder_DASHES = new WeakMap(), _FormDataEncoder_encoder = new WeakMap(), _FormDataEncoder_footer = new WeakMap(), _FormDataEncoder_form = new WeakMap(), _FormDataEncoder_options = new WeakMap(), _FormDataEncoder_instances = new WeakSet(), _FormDataEncoder_getFieldHeader = function _FormDataEncoder_getFieldHeader(name, value) {
        let header = "";
        header += `${__classPrivateFieldGet(this, _FormDataEncoder_DASHES, "f")}${this.boundary}${__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f")}`;
        header += `Content-Disposition: form-data; name="${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$escapeName$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(name)}"`;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(value)) {
            header += `; filename="${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$escapeName$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(value.name)}"${__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f")}`;
            header += `Content-Type: ${value.type || "application/octet-stream"}`;
        }
        if (__classPrivateFieldGet(this, _FormDataEncoder_options, "f").enableAdditionalHeaders === true) {
            header += `${__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f")}Content-Length: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isFileLike"])(value) ? value.size : value.byteLength}`;
        }
        return __classPrivateFieldGet(this, _FormDataEncoder_encoder, "f").encode(`${header}${__classPrivateFieldGet(this, _FormDataEncoder_CRLF, "f").repeat(2)}`);
    }, Symbol.iterator)]() {
        return this.values();
    }
    [Symbol.asyncIterator]() {
        return this.encode();
    }
}
const Encoder = FormDataEncoder;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/FileLike.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/FormDataLike.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FormDataEncoder$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FormDataEncoder.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FileLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FormDataLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FormDataLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFileLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFormData.js [app-route] (ecmascript)");
;
;
;
;
;
}}),
"[project]/node_modules/form-data-encoder/lib/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FormDataEncoder$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FormDataEncoder.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FileLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$FormDataLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/FormDataLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFileLike$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFileLike.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$util$2f$isFormData$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/util/isFormData.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$form$2d$data$2d$encoder$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/form-data-encoder/lib/esm/index.js [app-route] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_c28e1a20._.js.map