{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/lib/drawio.ts"], "sourcesContent": ["// draw.io 相关的工具函数\n\nexport interface DiagramConfig {\n  width?: number;\n  height?: number;\n  toolbar?: boolean;\n  menubar?: boolean;\n  edit?: boolean;\n  zoom?: number;\n}\n\nexport const defaultConfig: DiagramConfig = {\n  width: 800,\n  height: 600,\n  toolbar: false,\n  menubar: false,\n  edit: false,\n  zoom: 1,\n};\n\n/**\n * 验证XML是否为有效的draw.io格式\n */\nexport function validateDrawioXML(xml: string): boolean {\n  try {\n    const parser = new DOMParser();\n    const doc = parser.parseFromString(xml, 'text/xml');\n    \n    // 检查是否有解析错误\n    const parseError = doc.querySelector('parsererror');\n    if (parseError) {\n      return false;\n    }\n    \n    // 检查是否包含必要的draw.io元素\n    const mxfile = doc.querySelector('mxfile');\n    const diagram = doc.querySelector('diagram');\n    const mxGraphModel = doc.querySelector('mxGraphModel');\n    \n    return !!(mxfile && diagram && mxGraphModel);\n  } catch (error) {\n    console.error('XML验证失败:', error);\n    return false;\n  }\n}\n\n/**\n * 清理和格式化XML内容\n */\nexport function cleanXML(xml: string): string {\n  // 移除可能的markdown代码块标记\n  let cleaned = xml.replace(/```xml\\s*/g, '').replace(/```\\s*/g, '');\n  \n  // 移除多余的空白字符\n  cleaned = cleaned.trim();\n  \n  return cleaned;\n}\n\n/**\n * 生成draw.io查看器的URL参数\n */\nexport function generateViewerParams(config: DiagramConfig = {}): string {\n  const finalConfig = { ...defaultConfig, ...config };\n  \n  const params = new URLSearchParams({\n    embed: '1',\n    ui: 'min',\n    spin: '1',\n    modified: 'unsavedChanges',\n    proto: 'json',\n  });\n\n  if (!finalConfig.toolbar) {\n    params.append('toolbar', '0');\n  }\n  \n  if (!finalConfig.menubar) {\n    params.append('menubar', '0');\n  }\n  \n  if (!finalConfig.edit) {\n    params.append('edit', '_blank');\n  }\n\n  return params.toString();\n}\n\n/**\n * 创建一个简单的示例XML用于测试\n */\nexport function createSampleXML(): string {\n  return `<mxfile host=\"app.diagrams.net\" modified=\"2024-01-01T00:00:00.000Z\" agent=\"5.0\" version=\"24.7.17\" etag=\"sample\">\n  <diagram name=\"Page-1\" id=\"sample-id\">\n    <mxGraphModel dx=\"1422\" dy=\"794\" grid=\"1\" gridSize=\"10\" guides=\"1\" tooltips=\"1\" connect=\"1\" arrows=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\" pageWidth=\"827\" pageHeight=\"1169\" math=\"0\" shadow=\"0\">\n      <root>\n        <mxCell id=\"0\"/>\n        <mxCell id=\"1\" parent=\"0\"/>\n        <mxCell id=\"2\" value=\"开始\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"40\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"3\" value=\"处理\" style=\"rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"140\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"4\" value=\"结束\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;\" vertex=\"1\" parent=\"1\">\n          <mxGeometry x=\"364\" y=\"240\" width=\"100\" height=\"60\" as=\"geometry\"/>\n        </mxCell>\n        <mxCell id=\"5\" value=\"\" style=\"endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;\" edge=\"1\" parent=\"1\" source=\"2\" target=\"3\">\n          <mxGeometry width=\"50\" height=\"50\" relative=\"1\" as=\"geometry\">\n            <mxPoint x=\"384\" y=\"180\" as=\"sourcePoint\"/>\n            <mxPoint x=\"434\" y=\"130\" as=\"targetPoint\"/>\n          </mxGeometry>\n        </mxCell>\n        <mxCell id=\"6\" value=\"\" style=\"endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;\" edge=\"1\" parent=\"1\" source=\"3\" target=\"4\">\n          <mxGeometry width=\"50\" height=\"50\" relative=\"1\" as=\"geometry\">\n            <mxPoint x=\"384\" y=\"280\" as=\"sourcePoint\"/>\n            <mxPoint x=\"434\" y=\"230\" as=\"targetPoint\"/>\n          </mxGeometry>\n        </mxCell>\n      </root>\n    </mxGraphModel>\n  </diagram>\n</mxfile>`;\n}\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;AAWX,MAAM,gBAA+B;IAC1C,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,MAAM;IACN,MAAM;AACR;AAKO,SAAS,kBAAkB,GAAW;IAC3C,IAAI;QACF,MAAM,SAAS,IAAI;QACnB,MAAM,MAAM,OAAO,eAAe,CAAC,KAAK;QAExC,YAAY;QACZ,MAAM,aAAa,IAAI,aAAa,CAAC;QACrC,IAAI,YAAY;YACd,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,SAAS,IAAI,aAAa,CAAC;QACjC,MAAM,UAAU,IAAI,aAAa,CAAC;QAClC,MAAM,eAAe,IAAI,aAAa,CAAC;QAEvC,OAAO,CAAC,CAAC,CAAC,UAAU,WAAW,YAAY;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,OAAO;IACT;AACF;AAKO,SAAS,SAAS,GAAW;IAClC,qBAAqB;IACrB,IAAI,UAAU,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,WAAW;IAE/D,YAAY;IACZ,UAAU,QAAQ,IAAI;IAEtB,OAAO;AACT;AAKO,SAAS,qBAAqB,SAAwB,CAAC,CAAC;IAC7D,MAAM,cAAc;QAAE,GAAG,aAAa;QAAE,GAAG,MAAM;IAAC;IAElD,MAAM,SAAS,IAAI,gBAAgB;QACjC,OAAO;QACP,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,OAAO,MAAM,CAAC,WAAW;IAC3B;IAEA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,OAAO,MAAM,CAAC,WAAW;IAC3B;IAEA,IAAI,CAAC,YAAY,IAAI,EAAE;QACrB,OAAO,MAAM,CAAC,QAAQ;IACxB;IAEA,OAAO,OAAO,QAAQ;AACxB;AAKO,SAAS;IACd,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8BD,CAAC;AACV", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/components/DiagramViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { DiagramConfig, generateViewerParams, validateDrawioXML } from '@/lib/drawio';\n\ninterface DiagramViewerProps {\n  xml: string;\n  config?: DiagramConfig;\n  className?: string;\n}\n\nexport default function DiagramViewer({ xml, config, className = '' }: DiagramViewerProps) {\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!xml) {\n      setError('没有提供XML数据');\n      setIsLoading(false);\n      return;\n    }\n\n    // 验证XML格式\n    if (!validateDrawioXML(xml)) {\n      setError('无效的draw.io XML格式');\n      setIsLoading(false);\n      return;\n    }\n\n    const iframe = iframeRef.current;\n    if (!iframe) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    // 生成draw.io查看器URL\n    const baseUrl = 'https://viewer.diagrams.net/';\n    const params = generateViewerParams(config);\n    const viewerUrl = `${baseUrl}?${params}`;\n\n    // 设置iframe源\n    iframe.src = viewerUrl;\n\n    // 监听iframe加载完成\n    const handleLoad = () => {\n      try {\n        // 向iframe发送XML数据\n        const message = {\n          action: 'load',\n          xml: xml,\n          format: 'xml'\n        };\n        \n        iframe.contentWindow?.postMessage(JSON.stringify(message), '*');\n        setIsLoading(false);\n      } catch (err) {\n        console.error('发送XML数据失败:', err);\n        setError('加载图表失败');\n        setIsLoading(false);\n      }\n    };\n\n    iframe.addEventListener('load', handleLoad);\n\n    // 监听来自iframe的消息\n    const handleMessage = (event: MessageEvent) => {\n      if (event.origin !== 'https://viewer.diagrams.net') return;\n      \n      try {\n        const data = JSON.parse(event.data);\n        if (data.event === 'init') {\n          // 初始化完成，发送XML数据\n          const message = {\n            action: 'load',\n            xml: xml,\n            format: 'xml'\n          };\n          iframe.contentWindow?.postMessage(JSON.stringify(message), '*');\n        }\n      } catch (err) {\n        console.error('处理iframe消息失败:', err);\n      }\n    };\n\n    window.addEventListener('message', handleMessage);\n\n    // 清理函数\n    return () => {\n      iframe.removeEventListener('load', handleLoad);\n      window.removeEventListener('message', handleMessage);\n    };\n  }, [xml, config]);\n\n  if (error) {\n    return (\n      <div className={`flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-8 ${className}`}>\n        <div className=\"text-center\">\n          <div className=\"text-red-600 text-lg font-medium mb-2\">加载失败</div>\n          <div className=\"text-red-500 text-sm\">{error}</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`relative bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>\n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-gray-50 z-10\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n            <span className=\"text-gray-600\">加载中...</span>\n          </div>\n        </div>\n      )}\n      <iframe\n        ref={iframeRef}\n        className=\"w-full h-full border-0\"\n        style={{ minHeight: '400px' }}\n        title=\"Draw.io Diagram Viewer\"\n        sandbox=\"allow-scripts allow-same-origin allow-forms\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,cAAc,EAAE,GAAG,EAAE,MAAM,EAAE,YAAY,EAAE,EAAsB;IACvF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,KAAK;YACR,SAAS;YACT,aAAa;YACb;QACF;QAEA,UAAU;QACV,IAAI,CAAC,CAAA,GAAA,6GAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;YAC3B,SAAS;YACT,aAAa;YACb;QACF;QAEA,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,aAAa;QACb,SAAS;QAET,kBAAkB;QAClB,MAAM,UAAU;QAChB,MAAM,SAAS,CAAA,GAAA,6GAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,QAAQ;QAExC,YAAY;QACZ,OAAO,GAAG,GAAG;QAEb,eAAe;QACf,MAAM,aAAa;YACjB,IAAI;gBACF,iBAAiB;gBACjB,MAAM,UAAU;oBACd,QAAQ;oBACR,KAAK;oBACL,QAAQ;gBACV;gBAEA,OAAO,aAAa,EAAE,YAAY,KAAK,SAAS,CAAC,UAAU;gBAC3D,aAAa;YACf,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,cAAc;gBAC5B,SAAS;gBACT,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,QAAQ;QAEhC,gBAAgB;QAChB,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,MAAM,KAAK,+BAA+B;YAEpD,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;gBAClC,IAAI,KAAK,KAAK,KAAK,QAAQ;oBACzB,gBAAgB;oBAChB,MAAM,UAAU;wBACd,QAAQ;wBACR,KAAK;wBACL,QAAQ;oBACV;oBACA,OAAO,aAAa,EAAE,YAAY,KAAK,SAAS,CAAC,UAAU;gBAC7D;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,iBAAiB;YACjC;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;QACP,OAAO;YACL,OAAO,mBAAmB,CAAC,QAAQ;YACnC,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAK;KAAO;IAEhB,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,gFAAgF,EAAE,WAAW;sBAC5G,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI/C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,oEAAoE,EAAE,WAAW;;YAC/F,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAQ;gBAC5B,OAAM;gBACN,SAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/components/DiagramGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './DiagramViewer';\nimport { cleanXML, createSampleXML } from '@/lib/drawio';\n\nexport default function DiagramGenerator() {\n  const [description, setDescription] = useState('');\n  const [generatedXML, setGeneratedXML] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleGenerate = async () => {\n    if (!description.trim()) {\n      setError('请输入流程图描述');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/generate-diagram', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ description }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || '生成失败');\n      }\n\n      if (data.success && data.xml) {\n        const cleanedXML = cleanXML(data.xml);\n        setGeneratedXML(cleanedXML);\n      } else {\n        throw new Error('生成的XML无效');\n      }\n    } catch (err) {\n      console.error('生成流程图失败:', err);\n      setError(err instanceof Error ? err.message : '生成失败');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLoadSample = () => {\n    const sampleXML = createSampleXML();\n    setGeneratedXML(sampleXML);\n    setDescription('示例流程图：开始 -> 处理 -> 结束');\n  };\n\n  const handleClearAll = () => {\n    setDescription('');\n    setGeneratedXML('');\n    setError(null);\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto p-6 space-y-6\">\n      {/* 标题 */}\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          AI 流程图生成器\n        </h1>\n        <p className=\"text-gray-600\">\n          描述你的流程，AI 将自动生成专业的流程图\n        </p>\n      </div>\n\n      {/* 输入区域 */}\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          流程描述\n        </label>\n        <textarea\n          id=\"description\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          placeholder=\"请描述你想要创建的流程图，例如：用户登录流程、订单处理流程、数据处理管道等...\"\n          className=\"w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n          disabled={isLoading}\n        />\n        \n        {/* 错误提示 */}\n        {error && (\n          <div className=\"mt-2 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-red-600 text-sm\">{error}</p>\n          </div>\n        )}\n\n        {/* 按钮组 */}\n        <div className=\"mt-4 flex flex-wrap gap-3\">\n          <button\n            onClick={handleGenerate}\n            disabled={isLoading || !description.trim()}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n          >\n            {isLoading && (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n            )}\n            <span>{isLoading ? '生成中...' : '生成流程图'}</span>\n          </button>\n          \n          <button\n            onClick={handleLoadSample}\n            disabled={isLoading}\n            className=\"px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            加载示例\n          </button>\n          \n          <button\n            onClick={handleClearAll}\n            disabled={isLoading}\n            className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            清空\n          </button>\n        </div>\n      </div>\n\n      {/* 流程图显示区域 */}\n      {generatedXML && (\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">生成的流程图</h2>\n          <DiagramViewer \n            xml={generatedXML} \n            className=\"w-full\"\n            config={{\n              width: 800,\n              height: 600,\n              toolbar: false,\n              menubar: false,\n              edit: false,\n            }}\n          />\n          \n          {/* XML 代码显示（可选） */}\n          <details className=\"mt-4\">\n            <summary className=\"cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900\">\n              查看 XML 代码\n            </summary>\n            <pre className=\"mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-xs overflow-x-auto\">\n              <code>{generatedXML}</code>\n            </pre>\n          </details>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,EAAE;gBAC5B,MAAM,aAAa,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;gBACpC,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,YAAY;YAC1B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,YAAY,CAAA,GAAA,6GAAA,CAAA,kBAAe,AAAD;QAChC,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,gBAAgB;QAChB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAA+C;;;;;;kCAGtF,8OAAC;wBACC,IAAG;wBACH,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,aAAY;wBACZ,WAAU;wBACV,UAAU;;;;;;oBAIX,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,aAAa,CAAC,YAAY,IAAI;gCACxC,WAAU;;oCAET,2BACC,8OAAC;wCAAI,WAAU;;;;;;kDAEjB,8OAAC;kDAAM,YAAY,WAAW;;;;;;;;;;;;0CAGhC,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC,4HAAA,CAAA,UAAa;wBACZ,KAAK;wBACL,WAAU;wBACV,QAAQ;4BACN,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,SAAS;4BACT,MAAM;wBACR;;;;;;kCAIF,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAQ,WAAU;0CAAuE;;;;;;0CAG1F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///matrix/0-Work/0_dev/auto_draw/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}