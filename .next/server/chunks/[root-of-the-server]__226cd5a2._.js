module.exports = {

"[project]/.next-internal/server/app/api/generate-diagram/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[project]/app/api/generate-diagram/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <locals>");
;
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]({
    apiKey: ("TURBOPACK compile-time value", "sk-0y5XldE1OSQ4locAhGqHcxXUsLQmBgA6DpAingBtpDMVHvK2"),
    baseURL: ("TURBOPACK compile-time value", "https://api.moleapi.com/v1")
});
// 修复XML中的重复属性
function fixDuplicateAttributes(xml) {
    // 匹配所有的XML标签
    return xml.replace(/<([^>]+)>/g, (match, content)=>{
        // 如果是闭合标签或注释，直接返回
        if (content.startsWith('/') || content.startsWith('!')) {
            return match;
        }
        // 分离标签名和属性
        const parts = content.trim().split(/\s+/);
        const tagName = parts[0];
        const attributes = parts.slice(1);
        // 收集唯一的属性
        const uniqueAttrs = new Map();
        for (const attr of attributes){
            if (attr.includes('=')) {
                const [key, ...valueParts] = attr.split('=');
                const value = valueParts.join('=');
                // 保留最后一个出现的属性值
                uniqueAttrs.set(key, value);
            }
        }
        // 重建标签
        const rebuiltAttrs = Array.from(uniqueAttrs.entries()).map(([key, value])=>`${key}=${value}`).join(' ');
        return rebuiltAttrs ? `<${tagName} ${rebuiltAttrs}>` : `<${tagName}>`;
    });
}
async function POST(request) {
    try {
        const { description } = await request.json();
        if (!description) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '请提供流程图描述'
            }, {
                status: 400
            });
        }
        const prompt = `
请根据以下描述生成一个draw.io格式的XML流程图。

重要要求：
1. 使用标准的draw.io XML格式
2. 每个XML元素的属性不能重复
3. 所有的mxCell元素必须有唯一的id
4. 使用vertex="1"表示节点，edge="1"表示连接线
5. 布局清晰，坐标合理分布

描述：${description}

请直接返回XML代码，不要包含任何其他文本或解释。

标准格式示例：
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="24.7.17" etag="example">
  <diagram name="Page-1" id="example-id">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="200" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="2" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
`;
        const completion = await openai.chat.completions.create({
            model: ("TURBOPACK compile-time value", "Qwen/Qwen3-14B") || "Qwen/Qwen3-14B",
            messages: [
                {
                    role: "system",
                    content: "你是一个专业的流程图设计师，擅长创建draw.io格式的XML流程图。请严格按照draw.io的XML格式标准生成流程图。"
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 8192
        });
        const rawContent = completion.choices[0]?.message?.content;
        if (!rawContent) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '生成流程图失败'
            }, {
                status: 500
            });
        }
        // 提取XML内容，去掉think标签和其他内容
        let xmlContent = rawContent;
        // 移除<think>标签及其内容
        xmlContent = xmlContent.replace(/<think>[\s\S]*?<\/think>/gi, '');
        // 移除markdown代码块标记
        xmlContent = xmlContent.replace(/```xml\s*/g, '').replace(/```\s*/g, '');
        // 查找XML内容（从<mxfile>开始到</mxfile>结束）
        const xmlMatch = xmlContent.match(/<mxfile[\s\S]*?<\/mxfile>/i);
        if (xmlMatch) {
            xmlContent = xmlMatch[0];
        } else {
            // 如果没有找到完整的mxfile标签，尝试查找其他可能的XML结构
            const altMatch = xmlContent.match(/<\?xml[\s\S]*?<\/[^>]+>/i) || xmlContent.match(/<[^>]+[\s\S]*?<\/[^>]+>/i);
            if (altMatch) {
                xmlContent = altMatch[0];
            }
        }
        // 清理多余的空白字符
        xmlContent = xmlContent.trim();
        // 解码可能的HTML实体
        xmlContent = xmlContent.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"').replace(/&#x27;/g, "'").replace(/&amp;/g, '&');
        // 修复可能的重复属性问题
        xmlContent = fixDuplicateAttributes(xmlContent);
        if (!xmlContent || !xmlContent.includes('<mxfile')) {
            console.error('Invalid XML generated:', xmlContent.substring(0, 200));
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '生成的XML格式无效，请重试'
            }, {
                status: 500
            });
        }
        console.log('Generated XML preview:', xmlContent.substring(0, 200));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            xml: xmlContent,
            success: true
        });
    } catch (error) {
        console.error('生成流程图时出错:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '服务器内部错误'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__226cd5a2._.js.map