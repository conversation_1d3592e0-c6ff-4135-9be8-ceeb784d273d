version: '3.8'

services:
  # Draw.io 本地服务
  drawio:
    image: jgraph/drawio:latest
    container_name: drawio-local
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      - DRAWIO_BASE_URL=http://localhost:8080
      - DRAWIO_CSP_HEADER=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self'; font-src 'self'; object-src 'none'; media-src 'self'; frame-src 'self';
    restart: unless-stopped
    volumes:
      - drawio_data:/opt/tomcat/webapps/draw/WEB-INF/data
    networks:
      - app-network

  # Ollama 本地LLM服务
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-local
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    networks:
      - app-network
    # 如果有GPU，取消注释以下行
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Next.js 应用
  nextjs-app:
    build: .
    container_name: auto-draw-app
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_DRAWIO_URL=http://localhost:8080
      - NEXT_PUBLIC_LLM_BASE_URL=http://ollama:11434
      - NEXT_PUBLIC_LLM_API_KEY=ollama
      - NEXT_PUBLIC_LLM_DEFAULT_MODEL=llama3.2
      - NEXT_PUBLIC_OFFLINE_MODE=true
    depends_on:
      - drawio
      - ollama
    restart: unless-stopped
    networks:
      - app-network

volumes:
  drawio_data:
  ollama_data:

networks:
  app-network:
    driver: bridge
