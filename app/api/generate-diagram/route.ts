import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_LLM_API_KEY,
  baseURL: process.env.NEXT_PUBLIC_LLM_BASE_URL,
});

// 修复XML中的重复属性
function fixDuplicateAttributes(xml: string): string {
  // 匹配所有的XML标签
  return xml.replace(/<([^>]+)>/g, (match, content) => {
    // 如果是闭合标签或注释，直接返回
    if (content.startsWith('/') || content.startsWith('!')) {
      return match;
    }

    // 分离标签名和属性
    const parts = content.trim().split(/\s+/);
    const tagName = parts[0];
    const attributes = parts.slice(1);

    // 收集唯一的属性
    const uniqueAttrs = new Map<string, string>();

    for (const attr of attributes) {
      if (attr.includes('=')) {
        const [key, ...valueParts] = attr.split('=');
        const value = valueParts.join('=');
        // 保留最后一个出现的属性值
        uniqueAttrs.set(key, value);
      }
    }

    // 重建标签
    const rebuiltAttrs = Array.from(uniqueAttrs.entries())
      .map(([key, value]) => `${key}=${value}`)
      .join(' ');

    return rebuiltAttrs ? `<${tagName} ${rebuiltAttrs}>` : `<${tagName}>`;
  });
}

export async function POST(request: NextRequest) {
  try {
    const { description } = await request.json();

    if (!description) {
      return NextResponse.json(
        { error: '请提供流程图描述' },
        { status: 400 }
      );
    }

    const prompt = `
请根据以下描述生成一个draw.io格式的XML流程图。

重要要求：
1. 使用标准的draw.io XML格式
2. 每个XML元素的属性不能重复
3. 所有的mxCell元素必须有唯一的id
4. 使用vertex="1"表示节点，edge="1"表示连接线
5. 布局清晰，坐标合理分布

描述：${description}

请直接返回XML代码，不要包含任何其他文本或解释。

标准格式示例：
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="24.7.17" etag="example">
  <diagram name="Page-1" id="example-id">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="200" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1" source="2" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
`;

    const completion = await openai.chat.completions.create({
      model: process.env.NEXT_PUBLIC_LLM_DEFAULT_MODEL || "Qwen/Qwen3-14B",
      messages: [
        {
          role: "system",
          content: "你是一个专业的流程图设计师，擅长创建draw.io格式的XML流程图。请严格按照draw.io的XML格式标准生成流程图。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 8192,
    });

    const rawContent = completion.choices[0]?.message?.content;

    if (!rawContent) {
      return NextResponse.json(
        { error: '生成流程图失败' },
        { status: 500 }
      );
    }

    // 提取XML内容，去掉think标签和其他内容
    let xmlContent = rawContent;

    // 移除<think>标签及其内容
    xmlContent = xmlContent.replace(/<think>[\s\S]*?<\/think>/gi, '');

    // 移除markdown代码块标记
    xmlContent = xmlContent.replace(/```xml\s*/g, '').replace(/```\s*/g, '');

    // 查找XML内容（从<mxfile>开始到</mxfile>结束）
    const xmlMatch = xmlContent.match(/<mxfile[\s\S]*?<\/mxfile>/i);

    if (xmlMatch) {
      xmlContent = xmlMatch[0];
    } else {
      // 如果没有找到完整的mxfile标签，尝试查找其他可能的XML结构
      const altMatch = xmlContent.match(/<\?xml[\s\S]*?<\/[^>]+>/i) ||
                      xmlContent.match(/<[^>]+[\s\S]*?<\/[^>]+>/i);
      if (altMatch) {
        xmlContent = altMatch[0];
      }
    }

    // 清理多余的空白字符
    xmlContent = xmlContent.trim();

    // 解码可能的HTML实体
    xmlContent = xmlContent
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#x27;/g, "'")
      .replace(/&amp;/g, '&');

    // 修复可能的重复属性问题
    xmlContent = fixDuplicateAttributes(xmlContent);

    if (!xmlContent || !xmlContent.includes('<mxfile')) {
      console.error('Invalid XML generated:', xmlContent.substring(0, 200));
      return NextResponse.json(
        { error: '生成的XML格式无效，请重试' },
        { status: 500 }
      );
    }

    console.log('Generated XML preview:', xmlContent.substring(0, 200));

    return NextResponse.json({
      xml: xmlContent,
      success: true
    });

  } catch (error) {
    console.error('生成流程图时出错:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
