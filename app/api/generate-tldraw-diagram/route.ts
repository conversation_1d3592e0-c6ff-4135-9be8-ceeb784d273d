import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_LLM_API_KEY,
  baseURL: process.env.NEXT_PUBLIC_LLM_BASE_URL,
});

// 生成唯一ID
function generateId(prefix: string = 'shape'): string {
  return `${prefix}:${Math.random().toString(36).substr(2, 9)}`;
}

// 解析AI生成的流程图描述并转换为Tldraw数据
function parseFlowchartToTldraw(description: string): any {
  // 这里是一个简化的解析器，实际项目中可以使用更复杂的AI解析
  const shapes: any = {};
  const pageId = 'page:1';
  
  // 基本形状配置
  const shapeDefaults = {
    w: 120,
    h: 80,
    fill: 'solid',
    color: 'blue',
    size: 'm',
    font: 'draw',
  };

  // 简单的关键词匹配来创建形状
  const keywords = [
    { words: ['开始', 'start', '起始'], shape: 'ellipse', color: 'green' },
    { words: ['结束', 'end', '终止'], shape: 'ellipse', color: 'red' },
    { words: ['处理', 'process', '执行'], shape: 'rectangle', color: 'blue' },
    { words: ['判断', 'decision', '选择'], shape: 'diamond', color: 'yellow' },
    { words: ['输入', 'input', '数据'], shape: 'rectangle', color: 'purple' },
    { words: ['输出', 'output', '结果'], shape: 'rectangle', color: 'orange' },
  ];

  let x = 100;
  let y = 100;
  const stepHeight = 150;
  let shapeCount = 0;

  // 分析描述文本
  const sentences = description.split(/[，。,.\n]/).filter(s => s.trim());
  
  sentences.forEach((sentence, index) => {
    const trimmed = sentence.trim();
    if (!trimmed) return;

    // 查找匹配的关键词
    let matchedKeyword = keywords.find(k => 
      k.words.some(word => trimmed.toLowerCase().includes(word.toLowerCase()))
    );

    // 如果没有匹配，默认为处理步骤
    if (!matchedKeyword) {
      matchedKeyword = { words: ['处理'], shape: 'rectangle', color: 'blue' };
    }

    const shapeId = generateId('shape');
    
    shapes[shapeId] = {
      id: shapeId,
      type: 'geo',
      x: x,
      y: y + (index * stepHeight),
      props: {
        ...shapeDefaults,
        geo: matchedKeyword.shape,
        color: matchedKeyword.color,
        text: trimmed.length > 20 ? trimmed.substring(0, 20) + '...' : trimmed,
      },
    };

    // 如果不是最后一个形状，添加箭头
    if (index < sentences.length - 1) {
      const arrowId = generateId('arrow');
      shapes[arrowId] = {
        id: arrowId,
        type: 'arrow',
        props: {
          start: { x: x + 60, y: y + (index * stepHeight) + 80 },
          end: { x: x + 60, y: y + ((index + 1) * stepHeight) },
          color: 'black',
          size: 'm',
        },
      };
    }

    shapeCount++;
  });

  // 如果没有解析出任何形状，创建一个默认示例
  if (Object.keys(shapes).length === 0) {
    const shape1Id = generateId('shape');
    const shape2Id = generateId('shape');
    const arrowId = generateId('arrow');

    shapes[shape1Id] = {
      id: shape1Id,
      type: 'geo',
      x: 100,
      y: 100,
      props: {
        ...shapeDefaults,
        geo: 'ellipse',
        color: 'green',
        text: '开始',
      },
    };

    shapes[shape2Id] = {
      id: shape2Id,
      type: 'geo',
      x: 100,
      y: 250,
      props: {
        ...shapeDefaults,
        geo: 'rectangle',
        color: 'blue',
        text: description.length > 20 ? description.substring(0, 20) + '...' : description,
      },
    };

    shapes[arrowId] = {
      id: arrowId,
      type: 'arrow',
      props: {
        start: { x: 160, y: 180 },
        end: { x: 160, y: 250 },
        color: 'black',
        size: 'm',
      },
    };
  }

  return {
    document: {
      id: 'doc',
      name: 'Generated Diagram',
      version: 1,
      pages: {
        [pageId]: {
          id: pageId,
          name: 'Page 1',
          childIndex: 1,
          shapes: shapes,
        },
      },
      pageStates: {
        [pageId]: {
          id: pageId,
          selectedIds: [],
          camera: { x: 0, y: 0, z: 1 },
        },
      },
    },
  };
}

export async function POST(request: NextRequest) {
  try {
    const { description } = await request.json();

    if (!description) {
      return NextResponse.json(
        { error: '请提供流程图描述' },
        { status: 400 }
      );
    }

    // 使用AI优化描述（可选）
    const prompt = `
请将以下流程图描述转换为清晰的步骤列表，每个步骤用逗号分隔：

描述：${description}

要求：
1. 识别流程的开始、处理步骤、判断点和结束
2. 每个步骤简洁明了，不超过15个字
3. 按照逻辑顺序排列
4. 用逗号分隔各个步骤

示例输出：开始,用户登录,验证身份,判断权限,进入系统,结束
`;

    let processedDescription = description;

    try {
      const completion = await openai.chat.completions.create({
        model: process.env.NEXT_PUBLIC_LLM_DEFAULT_MODEL || "Qwen/Qwen3-14B",
        messages: [
          {
            role: "system",
            content: "你是一个专业的流程图分析师，擅长将复杂的流程描述转换为清晰的步骤列表。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500,
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (aiResponse && aiResponse.trim()) {
        processedDescription = aiResponse.trim();
      }
    } catch (aiError) {
      console.warn('AI处理失败，使用原始描述:', aiError);
      // 如果AI处理失败，继续使用原始描述
    }

    // 转换为Tldraw数据格式
    const diagramData = parseFlowchartToTldraw(processedDescription);

    console.log('Generated Tldraw data:', JSON.stringify(diagramData, null, 2));

    return NextResponse.json({
      diagramData: diagramData,
      success: true,
      processedDescription: processedDescription
    });

  } catch (error) {
    console.error('生成Tldraw流程图时出错:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
