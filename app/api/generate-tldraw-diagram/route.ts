import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_LLM_API_KEY,
  baseURL: process.env.NEXT_PUBLIC_LLM_BASE_URL,
});

// 生成唯一ID
function generateId(prefix: string = 'shape'): string {
  return `${prefix}:${Math.random().toString(36).substr(2, 9)}`;
}

// 解析AI生成的流程图描述并转换为Tldraw数据
function parseFlowchartToTldraw(description: string): any {
  const pageId = 'page:page';
  const shapes: Record<string, any> = {};

  // 简单的关键词匹配来创建形状
  const keywords = [
    { words: ['开始', 'start', '起始'], shape: 'ellipse', color: 'green' },
    { words: ['结束', 'end', '终止'], shape: 'ellipse', color: 'red' },
    { words: ['处理', 'process', '执行'], shape: 'rectangle', color: 'blue' },
    { words: ['判断', 'decision', '选择'], shape: 'diamond', color: 'yellow' },
    { words: ['输入', 'input', '数据'], shape: 'rectangle', color: 'violet' },
    { words: ['输出', 'output', '结果'], shape: 'rectangle', color: 'orange' },
  ];

  let x = 200;
  let y = 200;
  const stepHeight = 150;

  // 分析描述文本
  const sentences = description.split(/[，。,.\n]/).filter(s => s.trim());

  sentences.forEach((sentence, index) => {
    const trimmed = sentence.trim();
    if (!trimmed) return;

    // 查找匹配的关键词
    let matchedKeyword = keywords.find(k =>
      k.words.some(word => trimmed.toLowerCase().includes(word.toLowerCase()))
    );

    // 如果没有匹配，默认为处理步骤
    if (!matchedKeyword) {
      matchedKeyword = { words: ['处理'], shape: 'rectangle', color: 'blue' };
    }

    const shapeId = generateId('shape');

    shapes[shapeId] = {
      id: shapeId,
      type: 'geo',
      x: x,
      y: y + (index * stepHeight),
      rotation: 0,
      index: `a${index + 1}`,
      parentId: pageId,
      isLocked: false,
      opacity: 1,
      meta: {},
      props: {
        w: 160,
        h: 80,
        geo: matchedKeyword.shape,
        color: matchedKeyword.color,
        labelColor: 'black',
        fill: 'solid',
        dash: 'draw',
        size: 'm',
        font: 'draw',
        text: trimmed.length > 15 ? trimmed.substring(0, 15) + '...' : trimmed,
        align: 'middle',
        verticalAlign: 'middle',
        growY: 0,
        url: '',
      },
      typeName: 'shape',
    };

    // 如果不是最后一个形状，添加箭头
    if (index < sentences.length - 1) {
      const arrowId = generateId('shape');
      shapes[arrowId] = {
        id: arrowId,
        type: 'arrow',
        x: 0,
        y: 0,
        rotation: 0,
        index: `a${index + 1}.5`,
        parentId: pageId,
        isLocked: false,
        opacity: 1,
        meta: {},
        props: {
          dash: 'draw',
          size: 'm',
          fill: 'none',
          color: 'black',
          labelColor: 'black',
          bend: 0,
          start: {
            type: 'binding',
            boundShapeId: shapeId,
            normalizedAnchor: { x: 0.5, y: 1 },
            isExact: false,
          },
          end: {
            type: 'point',
            x: x + 80,
            y: y + (index + 1) * stepHeight,
          },
          arrowheadStart: 'none',
          arrowheadEnd: 'arrow',
          text: '',
          labelPosition: 0.5,
          font: 'draw',
        },
        typeName: 'shape',
      };
    }
  });

  // 如果没有解析出任何形状，创建一个默认示例
  if (Object.keys(shapes).length === 0) {
    const shape1Id = generateId('shape');
    const shape2Id = generateId('shape');
    const arrowId = generateId('shape');

    shapes[shape1Id] = {
      id: shape1Id,
      type: 'geo',
      x: 200,
      y: 200,
      rotation: 0,
      index: 'a1',
      parentId: pageId,
      isLocked: false,
      opacity: 1,
      meta: {},
      props: {
        w: 160,
        h: 80,
        geo: 'ellipse',
        color: 'green',
        labelColor: 'black',
        fill: 'solid',
        dash: 'draw',
        size: 'm',
        font: 'draw',
        text: '开始',
        align: 'middle',
        verticalAlign: 'middle',
        growY: 0,
        url: '',
      },
      typeName: 'shape',
    };

    shapes[shape2Id] = {
      id: shape2Id,
      type: 'geo',
      x: 200,
      y: 350,
      rotation: 0,
      index: 'a2',
      parentId: pageId,
      isLocked: false,
      opacity: 1,
      meta: {},
      props: {
        w: 160,
        h: 80,
        geo: 'rectangle',
        color: 'blue',
        labelColor: 'black',
        fill: 'solid',
        dash: 'draw',
        size: 'm',
        font: 'draw',
        text: description.length > 15 ? description.substring(0, 15) + '...' : description,
        align: 'middle',
        verticalAlign: 'middle',
        growY: 0,
        url: '',
      },
      typeName: 'shape',
    };

    shapes[arrowId] = {
      id: arrowId,
      type: 'arrow',
      x: 0,
      y: 0,
      rotation: 0,
      index: 'a1.5',
      parentId: pageId,
      isLocked: false,
      opacity: 1,
      meta: {},
      props: {
        dash: 'draw',
        size: 'm',
        fill: 'none',
        color: 'black',
        labelColor: 'black',
        bend: 0,
        start: {
          type: 'binding',
          boundShapeId: shape1Id,
          normalizedAnchor: { x: 0.5, y: 1 },
          isExact: false,
        },
        end: {
          type: 'binding',
          boundShapeId: shape2Id,
          normalizedAnchor: { x: 0.5, y: 0 },
          isExact: false,
        },
        arrowheadStart: 'none',
        arrowheadEnd: 'arrow',
        text: '',
        labelPosition: 0.5,
        font: 'draw',
      },
      typeName: 'shape',
    };
  }

  // 返回正确的Tldraw数据格式
  return {
    schemaVersion: 2,
    store: {
      'document:document': {
        gridSize: 10,
        name: '',
        meta: {},
        id: 'document:document',
        typeName: 'document',
      },
      [pageId]: {
        meta: {},
        id: pageId,
        name: 'Page 1',
        index: 'a1',
        typeName: 'page',
      },
      ...shapes,
    },
  };
}

export async function POST(request: NextRequest) {
  try {
    const { description } = await request.json();

    if (!description) {
      return NextResponse.json(
        { error: '请提供流程图描述' },
        { status: 400 }
      );
    }

    // 使用AI优化描述（可选）
    const prompt = `
请将以下流程图描述转换为清晰的步骤列表，每个步骤用逗号分隔：

描述：${description}

要求：
1. 识别流程的开始、处理步骤、判断点和结束
2. 每个步骤简洁明了，不超过15个字
3. 按照逻辑顺序排列
4. 用逗号分隔各个步骤

示例输出：开始,用户登录,验证身份,判断权限,进入系统,结束
`;

    let processedDescription = description;

    try {
      const completion = await openai.chat.completions.create({
        model: process.env.NEXT_PUBLIC_LLM_DEFAULT_MODEL || "Qwen/Qwen3-14B",
        messages: [
          {
            role: "system",
            content: "你是一个专业的流程图分析师，擅长将复杂的流程描述转换为清晰的步骤列表。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500,
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (aiResponse && aiResponse.trim()) {
        processedDescription = aiResponse.trim();
      }
    } catch (aiError) {
      console.warn('AI处理失败，使用原始描述:', aiError);
      // 如果AI处理失败，继续使用原始描述
    }

    // 转换为Tldraw数据格式
    const diagramData = parseFlowchartToTldraw(processedDescription);

    console.log('Generated Tldraw data:', JSON.stringify(diagramData, null, 2));

    return NextResponse.json({
      diagramData: diagramData,
      success: true,
      processedDescription: processedDescription
    });

  } catch (error) {
    console.error('生成Tldraw流程图时出错:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
