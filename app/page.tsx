import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-12 px-6">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI 流程图生成器
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            选择您喜欢的绘图引擎来生成专业的流程图
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Draw.io 版本 */}
          <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Draw.io 版本</h2>
              <p className="text-gray-600 mb-6">
                使用经典的Draw.io引擎，功能强大，支持复杂的流程图绘制
              </p>
              <div className="space-y-2 text-sm text-gray-500 mb-6">
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  专业级绘图工具
                </div>
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  丰富的形状库
                </div>
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                  需要网络连接
                </div>
              </div>
              <Link
                href="/drawio"
                className="inline-block w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                使用 Draw.io 版本
              </Link>
            </div>
          </div>

          {/* Tldraw 版本 */}
          <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow border-2 border-purple-200">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Tldraw 版本
                <span className="ml-2 px-2 py-1 text-xs bg-purple-100 text-purple-600 rounded-full">推荐</span>
              </h2>
              <p className="text-gray-600 mb-6">
                现代化的绘图引擎，完全离线运行，支持高清PNG导出
              </p>
              <div className="space-y-2 text-sm text-gray-500 mb-6">
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  完全离线运行
                </div>
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  高清透明PNG导出
                </div>
                <div className="flex items-center justify-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  现代化界面
                </div>
              </div>
              <Link
                href="/tldraw"
                className="inline-block w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                使用 Tldraw 版本
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">💡 离线部署说明</h3>
            <p className="text-blue-700">
              <strong>Tldraw版本</strong> 支持完全离线部署，无需外部依赖。
              <strong>Draw.io版本</strong> 需要连接到embed.diagrams.net服务。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
