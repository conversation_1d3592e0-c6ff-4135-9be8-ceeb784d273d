// draw.io 相关的工具函数

export interface DiagramConfig {
  width?: number;
  height?: number;
  toolbar?: boolean;
  menubar?: boolean;
  edit?: boolean;
  zoom?: number;
}

export const defaultConfig: DiagramConfig = {
  width: 800,
  height: 600,
  toolbar: false,
  menubar: false,
  edit: false,
  zoom: 1,
};

/**
 * 验证XML是否为有效的draw.io格式
 */
export function validateDrawioXML(xml: string): boolean {
  try {
    // 基本检查：是否包含XML内容
    if (!xml || typeof xml !== 'string') {
      console.log('XML validation failed: empty or invalid input');
      return false;
    }

    // 检查是否包含基本的draw.io标签
    if (!xml.includes('<mxfile') || !xml.includes('</mxfile>')) {
      console.log('XML validation failed: missing mxfile tags');
      return false;
    }

    // 尝试解析XML
    const parser = new DOMParser();
    const doc = parser.parseFromString(xml, 'text/xml');

    // 检查是否有解析错误
    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      console.log('XML validation failed: parse error', parseError.textContent);
      return false;
    }

    // 检查是否包含必要的draw.io元素
    const mxfile = doc.querySelector('mxfile');
    if (!mxfile) {
      console.log('XML validation failed: no mxfile element found');
      return false;
    }

    console.log('XML validation passed');
    return true;
  } catch (error) {
    console.error('XML验证失败:', error);
    return false;
  }
}

/**
 * 清理和格式化XML内容
 */
export function cleanXML(xml: string): string {
  let cleaned = xml;

  // 移除<think>标签及其内容
  cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, '');

  // 移除markdown代码块标记
  cleaned = cleaned.replace(/```xml\s*/g, '').replace(/```\s*/g, '');

  // 查找并提取XML内容（从<mxfile>开始到</mxfile>结束）
  const xmlMatch = cleaned.match(/<mxfile[\s\S]*?<\/mxfile>/i);

  if (xmlMatch) {
    cleaned = xmlMatch[0];
  } else {
    // 如果没有找到完整的mxfile标签，尝试查找其他可能的XML结构
    const altMatch = cleaned.match(/<\?xml[\s\S]*?<\/[^>]+>/i) ||
                    cleaned.match(/<[^>]+[\s\S]*?<\/[^>]+>/i);
    if (altMatch) {
      cleaned = altMatch[0];
    }
  }

  // 移除多余的空白字符
  cleaned = cleaned.trim();

  return cleaned;
}

/**
 * 生成draw.io查看器的URL参数
 */
export function generateViewerParams(config: DiagramConfig = {}): string {
  const finalConfig = { ...defaultConfig, ...config };

  const params = new URLSearchParams({
    embed: '1',
    ui: 'min',
    spin: '1',
    modified: 'unsavedChanges',
    proto: 'json',
  });

  if (!finalConfig.toolbar) {
    params.append('toolbar', '0');
  }

  if (!finalConfig.menubar) {
    params.append('menubar', '0');
  }

  if (!finalConfig.edit) {
    params.append('edit', '_blank');
  }

  return params.toString();
}

/**
 * 创建一个简单的示例XML用于测试
 */
export function createSampleXML(): string {
  return `<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" version="24.7.17" etag="sample">
  <diagram name="Page-1" id="sample-id">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="40" width="100" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="364" y="140" width="100" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="364" y="240" width="100" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="2" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="384" y="180" as="sourcePoint"/>
            <mxPoint x="434" y="130" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="384" y="280" as="sourcePoint"/>
            <mxPoint x="434" y="230" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>`;
}
