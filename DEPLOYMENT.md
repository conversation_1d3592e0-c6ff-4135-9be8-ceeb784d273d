# AI 流程图生成器 - 离线部署指南

## 项目概述

本项目提供两种绘图引擎：
- **Tldraw版本** - 完全离线，推荐用于离线部署
- **Draw.io版本** - 需要网络连接到embed.diagrams.net

## 离线部署能力分析

### ✅ Tldraw版本 - 完全离线
**依赖分析：**
- ✅ Next.js 15.3.2 - 本地打包
- ✅ React 19.0.0 - 本地打包  
- ✅ Tldraw 3.13.1 - 本地打包
- ✅ TailwindCSS 4 - 本地打包
- ⚠️ OpenAI API - 需要LLM服务（可用本地LLM替代）

**离线功能：**
- ✅ 图表绘制和编辑
- ✅ 高清PNG导出（透明背景）
- ✅ SVG导出
- ✅ 本地数据存储
- ✅ 完整的绘图工具

### ⚠️ Draw.io版本 - 部分离线
**外部依赖：**
- ❌ https://embed.diagrams.net - 图表显示和编辑
- ❌ https://app.diagrams.net - 在线编辑功能
- ⚠️ OpenAI API - 需要LLM服务

## 完全离线部署方案

### 方案一：Tldraw + 本地LLM（推荐）

#### 1. 环境准备
```bash
# 安装Node.js 18+
node --version
npm --version

# 克隆项目
git clone <repository-url>
cd auto_draw
npm install
```

#### 2. 配置本地LLM（Ollama）
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama3.2
ollama pull qwen2.5

# 启动服务
ollama serve
```

#### 3. 环境变量配置
```bash
# .env.local
NEXT_PUBLIC_LLM_BASE_URL=http://localhost:11434
NEXT_PUBLIC_LLM_API_KEY=ollama
NEXT_PUBLIC_LLM_DEFAULT_MODEL=llama3.2
NEXT_PUBLIC_OFFLINE_MODE=true
```

#### 4. 构建和部署
```bash
# 开发模式
npm run dev

# 生产构建
npm run build
npm start

# 或使用静态导出
npm run build
npm run export
```

### 方案二：Docker部署

#### 1. 创建Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

#### 2. Docker Compose（包含Ollama）
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_LLM_BASE_URL=http://ollama:11434
      - NEXT_PUBLIC_LLM_API_KEY=ollama
      - NEXT_PUBLIC_LLM_DEFAULT_MODEL=llama3.2
    depends_on:
      - ollama

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama

volumes:
  ollama_data:
```

## 功能对比

| 功能 | Tldraw版本 | Draw.io版本 |
|------|------------|-------------|
| 离线运行 | ✅ 完全支持 | ❌ 需要网络 |
| PNG导出 | ✅ 高清透明 | ✅ 高清 |
| SVG导出 | ✅ 支持 | ✅ 支持 |
| 绘图工具 | ✅ 现代化 | ✅ 专业级 |
| 形状库 | ✅ 基础形状 | ✅ 丰富形状 |
| 文件大小 | 📦 较小 | 📦 较大 |
| 加载速度 | ⚡ 快速 | ⚡ 中等 |

## 部署检查清单

### 离线部署前检查
- [ ] Node.js 18+ 已安装
- [ ] 所有npm依赖已安装
- [ ] 本地LLM服务已配置
- [ ] 环境变量已设置
- [ ] 项目可以成功构建

### 运行时检查
- [ ] 应用可以正常启动
- [ ] Tldraw编辑器可以正常显示
- [ ] PNG导出功能正常
- [ ] LLM生成功能正常（如果配置）

## 故障排除

### 常见问题

1. **Tldraw不显示**
   ```bash
   # 检查CSS是否正确加载
   # 确保'tldraw/tldraw.css'已导入
   ```

2. **PNG导出失败**
   ```bash
   # 检查浏览器控制台错误
   # 确保有绘制内容
   ```

3. **LLM连接失败**
   ```bash
   # 检查Ollama服务状态
   ollama list
   curl http://localhost:11434/api/tags
   ```

## 性能优化

### 生产环境优化
```bash
# 启用压缩
npm install compression

# 优化图片
npm install next-optimized-images

# 启用缓存
# 配置nginx或CDN
```

### 内存优化
```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['tldraw']
  }
}
```

## 总结

**推荐部署方案：**
1. **完全离线** - 使用Tldraw版本 + 本地Ollama
2. **混合模式** - Tldraw版本 + 云端LLM API
3. **在线模式** - Draw.io版本（需要网络）

**Tldraw版本的优势：**
- ✅ 完全离线运行
- ✅ 现代化用户界面
- ✅ 高质量PNG导出
- ✅ 轻量级部署
- ✅ 无外部依赖

这使得项目可以在完全隔离的网络环境中运行，满足企业内网部署需求。
